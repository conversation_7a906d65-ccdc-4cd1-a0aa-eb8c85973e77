import re
import os

# 要替换的内容
replacement_code = '''    def check_chapter(self, chapter_number: int) -> Tuple[bool, Dict[str, Any]]:
        """
        检查指定章节
        
        Args:
            chapter_number: 章节号
            
        Returns:
            (检查是否成功, 检查结果)
        """
        # 实例化检查器
        checker = NovelChecker()
        
        # 加载检查器数据
        success = checker.load_data()
        if not success:
            print("加载检查器数据失败")
            return False, {"passed": False, "issues": [{"type": "checker_init_error", "description": "加载检查器数据失败"}]}
        
        # 为检查器设置总章节数（必需属性）
        checker.total_chapters = self.total_chapters
        
        # 调用检查器的check_chapter方法
        success, result = checker.check_chapter(chapter_number, api=self.api)
        
        # 如果检查成功且通过了验证，应用更新（更新人物状态和战力）
        if success and result and result.get("passed", True):
            # 直接应用章节更新（会更新current_status和current_power）
            checker.apply_chapter_updates(chapter_number, result)
            print(f"第{chapter_number}章的人物状态和战力已更新")
        
        return success, result
'''

# 读取原文件
file_path = 'novel/generator.py'

with open(file_path, 'r', encoding='utf-8') as f:
    content = f.read()

# 定义要替换的方法
pattern = r'    def check_chapter\(self, chapter_number: int\) -> Tuple\[bool, Dict\[str, Any\]\]:.*?return checker\.check_chapter\(chapter_number, api=self\.api\)'

# 使用正则表达式替换，使用DOTALL标志来匹配包含换行符的文本
new_content = re.sub(pattern, replacement_code.strip(), content, flags=re.DOTALL)

# 检查是否有变化
if new_content != content:
    # 创建备份
    backup_path = file_path + '.bak'
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"已创建备份文件: {backup_path}")
    
    # 保存修改后的文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    print("成功更新check_chapter方法，添加了人物状态和战力更新逻辑")
else:
    print("没有找到匹配的代码，请检查正则表达式") 
"""
DeepSeek API全功能模块
整合了DeepSeek API的所有功能，替代Gemini API
"""

import json
import requests
import re
import time
from typing import List, Dict, Any, Optional, Union, Tuple

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DEEPSEEK_API_URL, DEEPSEEK_API_KEY, MAX_RETRIES, TIMEOUT
from llm.deepseek import DeepSeekAPI

class DeepSeekAllAPI(DeepSeekAPI):
    """DeepSeek API全功能封装，包含所有原Gemini功能"""

    def generate_main_storyline(self, genre: str, style_guide: str, background: str, total_chapters: int, original_storyline: str = None, force_regenerate: bool = False) -> Optional[str]:
        """
        生成故事主线概述

        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            total_chapters: 总章节数
            original_storyline: 原有的故事主线，用于重新生成时参考
            force_regenerate: 是否强制完全重新生成，不参考原有内容

        Returns:
            故事主线概述，如果出错则返回None
        """
        base_prompt = f"""
        请根据以下男频网络小说信息，生成一个详细的结构化故事主线：

        流派：{genre}
        风格指南：{style_guide}
        背景设定：{background}
        总章节数：{total_chapters}

        请提供一个完整的故事主线规划，以JSON格式输出，必须包含以下内容：
        1. 故事标题
        2. 每一章的详细内容规划（从第1章到第{total_chapters}章，每章内容简约描述大约100字，不能遗漏任何章节）

        【JSON格式要求】
        必须严格遵循以下JSON结构：
        ```json
        {{
          "story_title": "小说标题",
          "outlines": [
            {{
              "index": 1,
              "title": "第一章的建议标题",
              "content": "本章内容简要描述（100字）"
            }},
            // 第2章到第{total_chapters-1}章的内容（不要使用省略号，必须完整列出所有章节）
            {{
              "index": {total_chapters},
              "title": "最终章的建议标题",
              "content": "最终章内容简要描述（100字）"
            }}
          ]
        }}
        ```

        【重要格式说明】：
        1. index必须是整数类型，不能是字符串，例如应该是"index": 1而不是"index": "1"
        2. outlines数组必须包含从第1章到第{total_chapters}章的所有章节，不能遗漏任何章节
        3. 严格确保生成确切的{total_chapters}章，不能多也不能少
        4. 这是硬性要求，必须严格遵守，否则将导致系统错误
        """

        # 如果有原有的故事主线且不是强制重新生成，则基于原有内容进行修改
        if original_storyline and not force_regenerate:
            prompt = f"""
            请根据以下男频网络小说信息，修改并结构化现有的故事主线：

            流派：{genre}
            风格指南：{style_guide}
            背景设定：{background}
            总章节数：{total_chapters}

            【现有故事主线】：
            {original_storyline}

            请基于现有的故事主线进行修改和完善，将其转换为结构化的JSON格式。保留原有故事主线中的核心元素、主要角色和关键情节，但解决以下问题：
            1. 修复与背景设定的任何冲突或不一致
            2. 确保故事结构更加合理和完整
            3. 增强故事的吸引力和紧凑性
            4. 确保为每一章都提供详细的内容指导

            【JSON格式要求】
            必须严格遵循以下JSON结构：
            ```json
            {{
              "story_title": "小说标题",
              "outlines": [
                {{
                  "index": 1,
                  "title": "第一章的建议标题",
                  "content": "本章内容简要描述（100字）"
                }},
                // 第2章到第{total_chapters-1}章
                {{
                  "index": {total_chapters},
                  "title": "最终章的建议标题",
                  "content": "最终章内容简要描述（100字）"
                }}
              ]
            }}
            ```

            【重要格式及内容说明】：
            1. index必须是整数类型，不能是字符串，例如应该是"index": 1而不是"index": "1"
            2. outlines数组必须包含从第1章到第{total_chapters}章的所有章节，不能遗漏任何章节
            3. 严格确保生成确切的{total_chapters}章，不能多也不能少
            4. 这是硬性要求，必须严格遵守，否则将导致系统错误
            5. 如果现有故事主线章节数量不符合要求，请完整重新创建所有{total_chapters}章的内容
            """
        else:
            prompt = base_prompt

        result = self.generate_content(prompt, temperature=0.7, max_tokens=8000)
        if not result:
            return None

        # 规范化结果，确保只包含story_title和outlines字段
        try:
            # 提取JSON
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            json_str = json_match.group(1) if json_match else result

            try:
                # 尝试解析JSON
                data = json.loads(json_str)

                # 规范化数据 - 只保留必要字段
                clean_data = {
                    "story_title": data.get("story_title", "未知标题")
                }

                # 处理章节大纲
                outlines = []
                if "outlines" in data and isinstance(data["outlines"], list):
                    outlines = data["outlines"]
                elif "chapter_outlines" in data and isinstance(data["chapter_outlines"], list):
                    outlines = data["chapter_outlines"]

                # 检查章节数量
                if len(outlines) != total_chapters:
                    print(f"警告：生成的章节数量({len(outlines)})与要求的章节数量({total_chapters})不符，将进行完全重新生成")

                    # 与之前不同，现在我们不再尝试补充缺失的章节，而是进行完全重新生成
                    return self.generate_main_storyline(genre, style_guide, background, total_chapters, None, True)

                # 添加规范化的outlines字段
                clean_data["outlines"] = outlines

                # 验证最终章节数量
                final_chapters_count = len(clean_data["outlines"])
                if final_chapters_count != total_chapters:
                    print(f"警告：最终章节数量({final_chapters_count})仍与要求的章节数量({total_chapters})不符，将再次尝试完全重新生成")
                    return self.generate_main_storyline(genre, style_guide, background, total_chapters, None, True)
                else:
                    print(f"最终生成的主线包含{final_chapters_count}章，符合要求")

                # 返回干净的JSON字符串
                return json.dumps(clean_data, ensure_ascii=False, indent=2)

            except json.JSONDecodeError as e:
                print(f"解析JSON失败: {e}")
                # 如果解析失败，使用简单的修复方法
                try:
                    # 使用辅助函数修复JSON
                    fixed_data = self._fix_json(json_str)
                    if fixed_data:
                        # 规范化并清理
                        clean_data = {
                            "story_title": fixed_data.get("story_title", "未知标题"),
                            "outlines": fixed_data.get("outlines", fixed_data.get("chapter_outlines", []))
                        }

                        # 检查修复后的章节数量
                        if len(clean_data["outlines"]) != total_chapters:
                            print(f"警告：修复后的章节数量({len(clean_data['outlines'])})仍与要求的章节数量({total_chapters})不符，将进行完全重新生成")
                            return self.generate_main_storyline(genre, style_guide, background, total_chapters, None, True)

                        return json.dumps(clean_data, ensure_ascii=False, indent=2)
                except Exception as e2:
                    print(f"修复JSON失败: {e2}")

        except Exception as e:
            print(f"处理生成的故事主线时出错: {e}")

        # 如果所有处理尝试都失败，尝试重新生成
        if not force_regenerate:
            print("所有处理尝试失败，将进行完全重新生成")
            return self.generate_main_storyline(genre, style_guide, background, total_chapters, None, True)

        # 如果已经是强制重新生成模式仍然失败，返回原始结果
        return result

    def generate_novel_style(self, genre: str, target_length: int = None, total_chapters: int = None) -> Optional[str]:
        """
        生成小说风格提示词

        Args:
            genre: 小说流派
            target_length: 目标小说总长度（字数），默认为None
            total_chapters: 总章节数，默认为None

        Returns:
            风格提示词，如果出错则返回None
        """
        # 导入必要的模块
        import math
        from config import DEFAULT_CHAPTER_LENGTH, TARGET_NOVEL_LENGTH

        # 设置默认值
        if target_length is None:
            target_length = TARGET_NOVEL_LENGTH

        # 如果未提供总章节数，则计算
        if total_chapters is None:
            total_chapters = math.ceil(target_length / DEFAULT_CHAPTER_LENGTH)

        prompt = """
        请为一部男频网络小说的{0}流派生成详细的风格提示词。

        小说基本信息：
        - 总字数：约{1}字
        - 总章节数：{2}章

        提示词应包含以下内容：
        1. 该流派的文风特点
        2. 常用写作手法和套路
        3. 情节发展模式
        4. 角色塑造特点
        5. 世界观设定要点
        6. 读者期望
        7. 风格变化安排（如有）
            - 请确保风格变化的章节安排不超过总章节数{2}
            - 风格变化应该与小说的整体结构和发展阶段相匹配

        请以结构化方式输出，便于后续创作使用。
        """.format(genre, target_length, total_chapters)

        return self.generate_content(prompt, temperature=0.7)

    def generate_background(self, genre: str, style_guide: str) -> Optional[Dict[str, Any]]:
        """
        生成小说背景设定

        Args:
            genre: 小说流派
            style_guide: 风格指南

        Returns:
            背景设定字典，包含总体内容和分类内容，如果出错则返回None
        """
        prompt = """
        根据以下男频网络小说的流派和风格指南，生成详细的背景设定：

        流派：{0}
        风格指南：{1}

        请为这部小说创建完整且丰富的背景设定，需要分为以下几个类别，每个类别至少300字：

        1. 世界观（world_view）
            - 时代背景（具体年代、历史时期或架空世界的时间线）
            - 世界运行的基本规则（如有魔法、超能力等特殊元素）
            - 世界的整体格局和发展阶段

        2. 地理环境（geography）
            - 大陆分布、主要地形地貌
            - 重要的自然景观和地标
            - 气候特征与自然规律（可能的特殊天象、季节变化）

        3. 国家/区域（countries）
            - 主要国家、城市、区域的名称和特点
            - 各国家/区域之间的关系和边界
            - 主要国家的政治体制和统治者

        4. 组织/势力（organizations）
            - 主要门派、公会、宗教组织等
            - 各组织的宗旨、特点和影响力
            - 组织之间的关系和冲突
            - 至少10个组织/势力

        5. 重要物品/装备（items）
            - 世界中的特殊物品、武器、装备、绝学、武学、秘籍
            - 珍稀资源和材料
            - 具有特殊意义或力量的宝物
            - 至少50个重要物品/装备

        6. 修炼体系（cultivation）
            - 力量体系和等级划分
            - 修炼方法和境界
            - 特殊能力和技能体系

        7. 种族设定（races）
            - 世界中存在的种族及其特点
            - 种族之间的关系和地位差异
            - 特殊种族的能力和限制

        8. 历史背景（history）
            - 重要的历史事件和战争
            - 影响世界格局的重大变革
            - 古老的传说和预言

        9. 文化习俗（culture）
            - 主要文化特点和风俗习惯
            - 宗教信仰和禁忌
            - 节日庆典和传统活动

        10. 世界规则/法则（rules）
            - 特殊的自然法则或魔法规则
            - 社会规范和道德准则
            - 禁忌和限制

        请以JSON格式输出，每个类别作为一个键值对，同时提供一个总体内容的概述。确保所有设定相互协调，没有逻辑矛盾。
        所有设定必须符合{0}类型小说的特点，并与风格指南保持一致。

        输出格式示例：
        ```json
        {{
            "content": "总体背景设定概述...",
            "categories": {{
                "world_view": "详细的世界观设定...",
                "geography": "详细的地理环境设定...",
                "countries": "详细的国家/区域设定...",
                "organizations": "详细的组织/势力设定...",
                "items": "详细的重要物品/装备设定...",
                "cultivation": "详细的修炼体系设定...",
                "races": "详细的种族设定...",
                "history": "详细的历史背景设定...",
                "culture": "详细的文化习俗设定...",
                "rules": "详细的世界规则/法则设定..."
            }}
        }}
        ```
        """.format(genre, style_guide)

        response = self.generate_content(prompt, temperature=0.7)

        if not response:
            return None

        try:
            # 尝试提取JSON部分
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                response = json_match.group(1)

            # 解析JSON
            import json
            background_data = json.loads(response)

            # 确保有必要的字段
            if "content" not in background_data:
                background_data["content"] = "未提供总体背景设定"

            if "categories" not in background_data:
                background_data["categories"] = {}

            # 确保所有类别都存在
            required_categories = [
                "world_view", "geography", "countries", "organizations",
                "items", "cultivation", "races", "history", "culture", "rules"
            ]

            for category in required_categories:
                if category not in background_data["categories"]:
                    background_data["categories"][category] = ""

            return background_data
        except Exception as e:
            print(f"解析背景设定JSON时出错: {str(e)}")

            # 如果解析失败，创建一个基本结构
            return {
                "content": response,
                "categories": {
                    "world_view": "", "geography": "", "countries": "",
                    "organizations": "", "items": "", "cultivation": "",
                    "races": "", "history": "", "culture": "", "rules": ""
                }
            }

    def generate_new_characters(self, chapter_outline: str, characters_info: str, background: str) -> Optional[str]:
        """
        根据章节大纲生成新角色

        Args:
            chapter_outline: 章节大纲
            characters_info: 现有人物信息
            background: 背景设定

        Returns:
            新角色信息的JSON字符串，如果出错则返回None
        """
        prompt = f"""
        根据以下章节大纲和现有人物信息，为小说创建新角色：

        【章节大纲】
        {chapter_outline}

        【现有人物信息】
        {characters_info}

        【背景设定】
        {background}

        请根据章节大纲的需要，设计0-3个新角色。这些角色应该与大纲中描述的情节和场景相符，能够自然地融入故事。

        请以JSON格式输出新角色信息：
        ```json
        {{
            "new_characters": [
                {{
                    "name": "角色名",
                    "role": "角色类型（主角/女主/主要配角/次要配角/主要反派/次要反派）",
                    "basic_info": {{
                        "age": "年龄",
                        "gender": "性别",
                        "occupation": "职业"
                    }},
                    "appearance": "外貌描述",
                    "personality": "性格特点",
                    "abilities": ["能力1", "能力2"],
                    "background": "背景故事",
                    "motivation": "动机",
                    "relationships": {{
                        "已有角色名1": ["关系描述"],
                        "已有角色名2": ["关系描述"]
                    }},
                    "growth_path": "成长路径",
                    "current_status": "当前状态",
                    "current_power": "当前战力"
                }}
            ]
        }}
        ```

        重要格式说明：
        1. 所有字段必须使用双引号，不能使用单引号
        2. 字符串中如有引号，必须使用反斜杠转义，如 "他说\\"你好\\""
        3. relationships字段必须使用如下格式：{{"角色名1": ["关系1", "关系2"], "角色名2": ["关系3"]}}
        4. 不要在JSON中使用注释（如//开头的注释）
        5. 确保所有字段的值格式正确，不要有多余的逗号或缺少逗号

        如果章节大纲中没有需要新角色的情节，可以返回空数组：
        ```json
        {{
            "new_characters": []
        }}
        ```

        请确保新角色的设计符合男频网络小说的特点，并且能够为故事增添新的元素和可能性。
        """

        result = self.generate_content(prompt, temperature=0.7)

        # 提取JSON部分
        if result:
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                return json_match.group(1)
            else:
                return result

        return None

    def generate_outline(self, genre: str, style_guide: str, background: str,
                         current_chapter: int, total_chapters: int,
                         previous_chapters_summary: Optional[str] = None,
                         characters_info: Optional[str] = None,
                         foreshadowing_info: Optional[str] = None,
                         appearing_characters: Optional[str] = None,
                         main_storyline: Optional[str] = None,
                         recent_scenes: Optional[str] = None,
                         already_planted_foreshadowings: Optional[str] = None,
                         foreshadowings_to_plant: Optional[str] = None,
                         foreshadowings_to_reveal: Optional[str] = None) -> Optional[str]:
        """
        生成章节大纲

        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            current_chapter: 当前章节号
            total_chapters: 总章节数
            previous_chapters_summary: 前几章摘要（可选）
            characters_info: 人物卡片（可选）
            foreshadowing_info: 伏笔管理（可选）
            appearing_characters: 本章出场人物（可选）
            main_storyline: 故事主线概述（可选）
            recent_scenes: 最近章节出现的场景列表（可选）
            already_planted_foreshadowings: 已埋下的伏笔列表（可选）
            foreshadowings_to_plant: 需要在本章埋下的伏笔列表（可选）
            foreshadowings_to_reveal: 需要在本章回收的伏笔列表（可选）

        Returns:
            章节大纲，JSON格式，如果出错则返回None
        """
        # 从主线中提取当前章节的内容指导
        chapter_guide = None
        chapter_type_guidance = ""
        chapter_specific_guidance = ""
        
        # 安全检查：处理章节号
        try:
            current_chapter = int(current_chapter)
        except (ValueError, TypeError):
            print(f"警告：章节号 '{current_chapter}' 无效，使用默认值1")
            current_chapter = 1
            
        # 计算当前进度百分比
        progress = current_chapter / total_chapters if total_chapters > 0 else 0
            
        if main_storyline and main_storyline != "无":
            try:
                chapter_guide = self._extract_chapter_from_storyline(main_storyline, current_chapter)
                if chapter_guide:
                    chapter_specific_guidance = f"""
        【主线中的本章内容指导】：
        {chapter_guide}

        【重要】：本章大纲必须严格遵循上述主线内容指导，包括关键事件、出场人物和伏笔安排。
        """
            except Exception as e:
                print(f"从主线提取章节内容时出错: {e}")
                pass
        
        # 根据章节位置确定章节类型指导
        if current_chapter == 1:
            chapter_type_guidance = """
        【特别说明】：
        这是小说的第一章，需要：
        - 引入主要角色和故事世界
        - 建立基本冲突
        - 设定小说的基调和节奏
        - 吸引读者继续阅读
        """
        elif current_chapter == total_chapters:
            chapter_type_guidance = """
        【特别说明】：
        这是小说的最后一章，需要：
        - 解决主要冲突
        - 完成主角的成长弧线
        - 处理所有主要伏笔
        - 给读者一个令人满意的结局
        """
        elif current_chapter / total_chapters < 0.25:
            chapter_type_guidance = """
        【特别说明】：
        这是小说的开端部分，需要：
        - 继续发展主要角色
        - 扩展故事世界
        - 加深冲突和挑战
        - 铺设重要伏笔
        """
        elif current_chapter / total_chapters > 0.75:
            chapter_type_guidance = """
        【特别说明】：
        这是小说的结尾部分，需要：
        - 推动故事朝着高潮发展
        - 开始解决次要冲突
        - 回收前面埋下的伏笔
        - 为最终结局做准备
        """
        else:
            chapter_type_guidance = """
        【特别说明】：
        这是小说的中段，需要：
        - 加剧冲突和障碍
        - 推动角色成长
        - 丰富次要情节线
        - 维持读者兴趣
        """
        
        # 如果提供了特殊伏笔要求，添加到提示词中
        if foreshadowings_to_plant:
            chapter_specific_guidance += f"""
        【本章必须埋下的伏笔】：
        {foreshadowings_to_plant}

        请确保在大纲中明确计划如何埋下这些伏笔，这是必须的。
        """
        
        if foreshadowings_to_reveal:
            chapter_specific_guidance += f"""
        【本章必须回收的伏笔】：
        {foreshadowings_to_reveal}

        请确保在大纲中明确计划如何回收这些伏笔，这是必须的。
        """
        
        # 处理格式化字符串中可能出现的问题字符
        # 避免出现类似 KeyError: '\n "title"' 的格式化错误
        try:
            # 安全处理，防止格式化时出现问题
            prompt_template = """
        请为以下设定的小说创作第{0}章的大纲。

        流派：{genre}
        风格指南：{style_guide}
        背景设定：{background}
        当前章节：第{0}章（共{total_chapters}章）
        前几章摘要：{previous_summary}
        
        {chapter_type}
        
        {chapter_specific}

        【人物卡片】：
        {characters}

        【伏笔管理】：
        {foreshadowing}

        请按照以下JSON格式输出章节大纲：

        ```json
        {{
          "title": "第{0}章 标题",
          "chapter_summary": {{
            "opening": "章节开始的场景描述",
            "development": "情节如何推进",
            "climax": "本章的高潮部分",
            "ending": "章节结束的场景描述，为下一章做铺垫"
          }},
          "characters": [
            {{
              "name": "角色1",
              "actions": "角色在本章的行动",
              "emotions": "角色在本章的情感变化"
            }},
            {{
              "name": "角色2",
              "actions": "角色在本章的行动",
              "emotions": "角色在本章的情感变化"
            }}
          ],
          "scenes": [
            "场景1：简短描述",
            "场景2：简短描述",
            "场景3：简短描述"
          ],
          "key_points": [
            "关键情节点1",
            "关键情节点2",
            "关键情节点3"
          ],
          "foreshadowings": {{
            "planted": [
              {{
                "id": "伏笔ID",
                "description": "如何在本章中埋下伏笔"
              }}
            ],
            "revealed": [
              {{
                "id": "伏笔ID",
                "description": "如何在本章中回收伏笔"
              }}
            ]
          }}
        }}
        ```

        {chapter_type}
        {chapter_specific}

        重要要求：
        1. 你必须以有效的JSON格式响应，确保JSON结构完整且符合上述格式
        2. 本章情节必须与前几章有明显区别，不要重复前几章的情节或场景
        3. 确保本章大纲与故事主线概述保持一致，符合当前进度（{1:.1%}）的情节发展要求
        4. title字段必须是完整的章节标题，包含章节序号，例如"第{0}章 英雄归来"，不要省略"第{0}章"的部分
        5. 创造新颖的情节和场景，避免套路化和重复前文的内容
        6. 如果前几章有未解决的冲突或悬念，本章应该推进这些情节，而不是创建完全无关的新情节
        7. 【重要】所有概念、地点、组织、物品、种族、能力等必须来自背景设定中已有的内容
        8. 【重要】不要创造背景设定中未提及的新概念、新地点、新组织、新物品、新种族或新能力
        9. 【重要】所有角色的能力和成长路径必须符合背景设定中的修炼体系和世界规则
        10. 【最重要】伏笔管理规则：
            a. 仅包含在"本章必须埋下的伏笔"和"本章必须回收的伏笔"中明确列出的伏笔
            b. 严格禁止提及或暗示任何未在本章指定的伏笔信息
            c. 不要预先回收应该在未来章节回收的伏笔
            d. 不要埋下任何未被明确要求埋下的伏笔
            e. 对每个需要埋下或回收的伏笔，必须在大纲中明确指出如何处理
        11. 【最重要】只返回JSON格式数据，不要返回其他任何文本或注释
        """
            
            # 准备用于格式化的参数字典
            format_params = {
                "genre": genre,
                "style_guide": style_guide,
                "background": background,
                "total_chapters": total_chapters,
                "previous_summary": previous_chapters_summary or "无",
                "characters": characters_info or "无",
                "foreshadowing": foreshadowing_info or "无",
                "chapter_type": chapter_type_guidance,
                "chapter_specific": chapter_specific_guidance,
            }
            
            # 使用命名参数格式化以避免位置参数的问题
            prompt = prompt_template.format(current_chapter, progress, **format_params)
            
        except KeyError as e:
            print(f"格式化大纲提示词时出现KeyError: {e}")
            # 尝试修复格式化错误
            try:
                # 特殊处理常见的KeyError问题
                if str(e).startswith("'\\n") or "title" in str(e):
                    print("检测到换行+title格式错误，尝试修复...")
                    # 使用简化的提示词模板
                    prompt = f"""
                请为以下设定的小说创作第{current_chapter}章的大纲。
                流派：{genre}
                请输出JSON格式的大纲，包含标题、章节摘要、人物、场景和关键情节点。
                """
                else:
                    # 其他未知的KeyError
                    raise
            except Exception as e2:
                print(f"尝试修复格式化错误时出现新错误: {e2}")
                return None
        except Exception as e:
            print(f"格式化大纲提示词时出错: {e}")
            return None

        result = self.generate_content(prompt, temperature=0.8)

        if result:
            try:
                # 尝试解析JSON，确保结构有效
                if result.strip().startswith("{") and result.strip().endswith("}"):
                    # 在解析前清理JSON字符串
                    cleaned_result = self._clean_json_string(result)
                    json_data = json.loads(cleaned_result)
                    return cleaned_result
                else:
                    # 尝试从文本中提取JSON
                    import re
                    json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(1)
                        # 在解析前清理JSON字符串
                        cleaned_json_str = self._clean_json_string(json_str)
                        json_data = json.loads(cleaned_json_str)
                        return cleaned_json_str
                    else:
                        # 如果找不到JSON，尝试修复并返回
                        cleaned_result = self._clean_json_string(result)
                        try:
                            json_data = json.loads(cleaned_result)
                            return cleaned_result
                        except:
                            # 如果仍然无法解析，返回原始结果
                            print("无法解析为JSON，返回原始结果")
                            # 移除Markdown代码块标记后再返回
                            result = re.sub(r'^```\w*\s*', '', result)
                            result = re.sub(r'\s*```\s*$', '', result)
                            return result
            except Exception as e:
                print(f"解析大纲JSON时出错: {e}")
                # 尝试更强力的修复方法
                try:
                    # 首先移除可能的Markdown代码块标记
                    result = re.sub(r'^```\w*\s*', '', result)
                    result = re.sub(r'\s*```\s*$', '', result)
                    
                    cleaned_result = self._clean_json_string(result, aggressive=True)
                    json_data = json.loads(cleaned_result)
                    print("使用强力修复方法成功解析JSON")
                    return cleaned_result
                except Exception as e2:
                    print(f"强力修复JSON失败: {e2}")
                    
                    # 尝试使用更多自定义修复方法
                    try:
                        from utils.fix_storyline import fix_storyline_json
                        from utils.json_helper import fix_json
                        
                        # 移除可能的Markdown代码块标记
                        result = re.sub(r'^```\w*\s*', '', result)
                        result = re.sub(r'\s*```\s*$', '', result)
                        result = re.sub(r'```json\s*(.*?)\s*```', r'\1', result, flags=re.DOTALL)
                        
                        # 尝试多种修复方法
                        print("尝试使用fix_storyline_json修复...")
                        fixed_data = fix_storyline_json(result)
                        if fixed_data:
                            print("使用fix_storyline_json修复成功")
                            return json.dumps(fixed_data, ensure_ascii=False)
                            
                        print("尝试使用fix_json修复...")
                        fixed_json = fix_json(result)
                        if fixed_json:
                            try:
                                json.loads(fixed_json)  # 验证是否有效
                                print("使用fix_json修复成功")
                                return fixed_json
                            except:
                                pass
                                
                    except ImportError:
                        print("修复工具不可用")
                    except Exception as e3:
                        print(f"使用额外修复工具时出错: {e3}")
                    
                    # 最后尝试清除Markdown标记后返回
                    result = re.sub(r'```json\s*(.*?)\s*```', r'\1', result, flags=re.DOTALL)
                    return result

        return result

    def generate_characters(self, genre: str, style_guide: str, background: str, main_storyline: str = None) -> Optional[str]:
        """
        生成人物卡片

        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            main_storyline: 故事主线概述（可选）

        Returns:
            人物卡片，如果出错则返回None
        """
        # 添加故事主线参数
        storyline_part = ""
        if main_storyline:
            storyline_part = f"""
        故事主线概述：{main_storyline}

        【重要要求】：必须全面扫描故事主线，确保创建所有在主线中出现或提及的人物，不管是主要人物还是次要人物，一个都不能遗漏。
        1. 首先仔细阅读故事主线中的所有章节内容
        2. 识别并列出每一个被提及的角色名字（包括只出现一次的角色）
        3. 为每一个识别到的角色创建完整的人物卡片
        4. 确保人物创建的全面性和完整性，与主线故事保持一致
        5. 如果主线中的角色未详细描述，需根据上下文和背景设定合理推断其特征和背景
        """

        prompt = f"""
        根据以下男频网络小说信息，创建详细的人物卡片：

        流派：{genre}
        风格指南：{style_guide}
        背景设定：{background}
        {storyline_part}

        请为这部小说创建所有必要的人物卡片（包括主角、女主、重要配角、次要配角、主要反派、次要反派等所有在主线中出现过的角色），每个人物卡片应包含：
        1. 基本信息（姓名、年龄、性别、身份/职业等）
        2. 外貌特征
        3. 性格特点
        4. 特殊能力/技能
        5. 背景故事
        6. 动机和目标
        7. 与其他角色的关系（格式为数组，不要使用字符串描述）
        8. 成长路径
        9. 当前状态
        10. 当前战力（根据背景设定中的战力等级划分，明确标注角色当前的战力水平）
        11. 首次出场章节（主角默认为1，其他角色可根据故事需要设定）
        12. 出场章节列表（指明该角色应该在哪些章节中出场，必须是数字数组）

        【重要人物创建指南】
        1. 如果有主线故事，必须仔细分析故事主线中的所有章节内容
        2. 确保创建所有在主线中提及的人物角色，即使是只出现一次的次要角色
        3. 确保角色设定与主线故事情节完全匹配，包括他们的能力、背景和出场章节
        4. 特别注意那些在故事发展中可能扮演关键角色的配角，即使他们初期戏份不多
        5. 确保角色数量足够支撑整个故事，一般不少于10个主要角色（包括主角、重要配角和主要反派）

        请以结构化JSON格式输出，便于后续程序处理和更新。格式参考：
        ```json
        {{
            "characters": [
                {{
                    "name": "角色名",
                    "role": "主角/女主/主要配角/次要配角/主要反派/次要反派",
                    "basic_info": {{
                        "age": 25,
                        "gender": "男/女",
                        "identity": "身份/职业"
                    }},
                    "appearance": "外貌描述",
                    "personality": "性格描述",
                    "abilities": ["能力1", "能力2", "能力3"],
                    "background": "背景故事",
                    "motivation": "动机与目标",
                    "relationships": {{
                        "角色名1": ["朋友", "盟友"],
                        "角色名2": ["敌人"],
                        "角色名3": ["师徒"]
                    }},
                    "growth_path": "成长轨迹",
                    "current_status": "当前状态",
                    "current_power": "当前战力",
                    "first_appearance_chapter": 1,
                    "appearance_chapters": [1, 2, 5, 8, 10]
                }},
                // 继续创建所有必要的角色，确保包含主线中提及的所有人物
            ]
        }}
        ```

        【出场章节说明】
        1. 对于主角，建议设置appearance_chapters包括全部或大多数章节
        2. 对于重要角色（如女主、关键配角），应确保在关键章节中出场
        3. 对于次要角色，应适当安排出场章节，符合其在故事中的作用
        4. 出场章节必须是数字数组，格式为[1, 2, 3, ...]
        5. 必须为每个角色安排适当的出场章节
        6. 出场章节安排必须与主线故事情节吻合

        【角色全面性检查】
        在完成人物卡片创建后，请进行如下检查：
        1. 是否包含了主线中每一个提及的角色？（必须确保100%覆盖）
        2. 角色之间的关系网络是否完整且一致？（互相引用的关系必须匹配）
        3. 角色的能力和背景是否与世界观设定一致？
        4. 每个角色的动机和目标是否有助于推动故事发展？
        5. 是否为每个角色分配了合适的出场章节？

        重要格式说明：
        1. 所有字段必须使用双引号，不能使用单引号
        2. 字符串中如有引号，必须使用反斜杠转义，如 "他说\\"你好\\""
        3. relationships字段必须使用如下格式：{{"角色名1": ["关系1", "关系2"], "角色名2": ["关系3"]}}
        4. appearance_chapters字段必须是数字数组，例如：[1, 3, 5, 8]
        5. 不要在JSON中使用注释（如//开头的注释）
        6. 确保所有字段的值格式正确，不要有多余的逗号或缺少逗号

        确保人物设定符合男频网络小说特点，特别是主角应具备吸引力和成长空间。当前战力必须根据背景设定中的战力等级划分来确定，这将是后续章节中人物战力评估的重要依据。
        """

        return self.generate_content(prompt, temperature=0.7)

    def generate_chapter_foreshadowing(self, genre: str, style_guide: str, background: str,
                                    characters: str, chapter_outline: str, chapter_number: int,
                                    total_chapters: int = 50) -> Optional[str]:
        """
        根据章节大纲生成新伏笔

        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            characters: 人物卡片
            chapter_outline: 章节大纲
            chapter_number: 当前章节号
            total_chapters: 总章节数，默认为50

        Returns:
            新伏笔，如果出错则返回None
        """
        prompt = """
        根据以下男频网络小说信息，为当前章节创建新的伏笔：

        流派：{0}
        风格指南：{1}
        背景设定：{2}
        人物卡片：{3}
        当前章节大纲：{4}
        当前章节号：{5}
        总章节数：{6}

        请根据当前章节大纲内容，创建2-3个新的伏笔，这些伏笔应该：
        1. 与当前章节内容紧密相关
        2. 能够在后续章节中回收
        3. 有助于推动故事情节发展
        4. 增加故事的悬念和吸引力

        对每个新伏笔，请提供：
        - 伏笔内容（具体描述）
        - 伏笔类型（主线/角色/世界观/感情线）
        - 埋下伏笔的章节（当前章节）
        - 回收伏笔的预计章节（必须大于当前章节且小于或等于总章节数{6}）
        - 伏笔的重要性（高/中/低）
        - 伏笔埋下方式（如何在当前章节中埋下这个伏笔）
        - 伏笔回收效果（回收时会对情节产生什么影响）
        - 相关角色（与伏笔相关的角色名列表）

        请以结构化JSON格式输出，便于后续程序处理和更新。格式参考：
        ```json
        {{
            "foreshadowings": [
                {{
                    "type": "主线/角色/世界观/感情线",
                    "content": "伏笔内容描述",
                    "plant_chapter": {5},
                    "reveal_chapter": 预计回收章节,
                    "importance": "高/中/低",
                    "plant_method": "埋下方式描述",
                    "reveal_effect": "回收效果描述",
                    "status": "未埋下",
                    "related_characters": ["角色名1", "角色名2"]
                }},
                // 其他伏笔...
            ]
        }}
        ```

        确保伏笔安排合理，能够增强故事的连贯性和吸引力。
        特别注意：所有回收章节号必须大于当前章节号{5}且小于或等于总章节数{6}。
        """.format(genre, style_guide, background, characters, chapter_outline, chapter_number, total_chapters)

        return self.generate_content(prompt, temperature=0.7)

    def generate_foreshadowing(self, genre: str, style_guide: str, background: str,
                                 characters: str, outline: str, total_chapters: int = 50) -> Optional[str]:
        """
        生成伏笔管理

        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            characters: 人物卡片
            outline: 大纲
            total_chapters: 总章节数，默认为50

        Returns:
            伏笔管理，如果出错则返回None
        """
        prompt = """
        根据以下男频网络小说信息，创建详细的伏笔管理计划：

        流派：{0}
        风格指南：{1}
        背景设定：{2}
        人物卡片：{3}
        大纲：{4}
        总章节数：{5}

        请规划整部小说中的伏笔安排，包括：
        1. 主线伏笔（影响主要剧情走向的关键线索）
        2. 角色伏笔（与角色成长、身世相关的线索）
        3. 世界观伏笔（与世界设定相关的隐藏信息）
        4. 感情线伏笔（与感情发展相关的铺垫）

        对每个伏笔，请提供：
        - 伏笔内容
        - 埋下伏笔的预计章节（必须小于或等于总章节数{5}）
        - 回收伏笔的预计章节（必须小于或等于总章节数{5}）
        - 伏笔的重要性（高/中/低）
        - 伏笔埋下方式
        - 伏笔回收效果

        伏笔管理的重要原则：
        1. 所有伏笔必须在小说结束前回收，不能留下未解决的伏笔
        2. 重要伏笔应该在小说的后半部分回收，特别是在高潮和结局部分
        3. 伏笔的回收应该分布在不同章节，避免在单一章节回收过多伏笔
        4. 主线伏笔的回收应该对剧情产生重大影响，特别是对结局的走向
        5. 最终章节应该回收最重要的伏笔，并与小说结局紧密相关
        6. 所有章节号必须在1到{5}之间，不能超出这个范围

        请以结构化JSON格式输出，便于后续程序处理和更新。格式参考：
        ```json
        {{
            "foreshadowings": [
                {{
                    "id": "f1",
                    "type": "主线",
                    "content": "伏笔内容描述",
                    "plant_chapter": 5,
                    "reveal_chapter": 20,
                    "importance": "高",
                    "plant_method": "埋下方式描述",
                    "reveal_effect": "回收效果描述",
                    "status": "未埋下",
                    "related_characters": ["角色名1", "角色名2"]
                }},
                {{
                    "id": "f2",
                    "type": "角色",
                    "content": "另一个伏笔内容描述",
                    "plant_chapter": 8,
                    "reveal_chapter": 25,
                    "importance": "中",
                    "plant_method": "埋下方式描述",
                    "reveal_effect": "回收效果描述",
                    "status": "未埋下",
                    "related_characters": ["角色名3"]
                }}
            ]
        }}
        ```

        【重要】JSON格式要求：
        1. 所有字段名必须使用双引号，不能使用单引号
        2. 字符串值必须使用双引号，不能使用单引号
        3. 数值不需要引号（如章节号）
        4. 数组和对象的最后一项后面不要加逗号
        5. 不要在JSON中添加注释
        6. 确保JSON格式严格正确，特别是逗号的使用

        确保伏笔安排合理，能够增强故事的连贯性和吸引力，并且所有伏笔都能在小说结束前得到回收和解释。
        特别注意：所有章节号必须在1到{5}之间，不能超出这个范围。
        """.format(genre, style_guide, background, characters, outline, total_chapters)

        return self.generate_content(prompt, temperature=0.7)

    def check_chapter_word_count(self, chapter_content: str) -> Dict[str, Any]:
        """
        检查章节字数

        Args:
            chapter_content: 章节内容

        Returns:
            检查结果字典
        """
        prompt = """
        请检查以下小说章节的字数是否合适：

        【章节内容】
        {0}

        要求：
        1. 章节长度应该在5000-12000字之间，理想长度约为10000字
        2. 如果字数不足5000字，标记为问题
        3. 如果字数超过12000字，可以接受但需要标记为提醒

        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "word_count": 实际字数,
            "issues": [
                {{
                    "type": "字数不足/字数过多",
                    "description": "问题详细描述",
                    "suggestion": "修改建议"
                }}
            ]
        }}
        ```

        如果字数合适，请返回passed为true且issues为空数组。
        """.format(chapter_content)

        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "description": "字数检查失败",
                    "suggestion": "请重新检查"
                }]
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)
            return check_result
        except Exception as e:
            print(f"解析字数检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "description": f"解析字数检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }]
            }

    def check_chapter_style(self, chapter_content: str, genre: str) -> Dict[str, Any]:
        """
        检查章节风格

        Args:
            chapter_content: 章节内容
            genre: 小说流派

        Returns:
            检查结果字典
        """
        prompt = """
        请检查以下小说章节的风格是否符合要求：

        【章节内容】
        {0}

        【小说流派】
        {1}

        要求：
        1. 文风应符合男频网络小说特点，具有代入感和爽感
        2. 应符合{1}流派的特点
        3. 语言应流畅，节奏感强，情节有起伏
        4. 描写应生动形象，有画面感

        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "issues": [
                {{
                    "type": "风格问题",
                    "description": "问题详细描述",
                    "location": "问题在文中的大致位置",
                    "suggestion": "修改建议"
                }}
            ]
        }}
        ```

        如果风格符合要求，请返回passed为true且issues为空数组。
        """.format(chapter_content, genre)

        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "description": "风格检查失败",
                    "suggestion": "请重新检查"
                }]
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)
            return check_result
        except Exception as e:
            print(f"解析风格检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "description": f"解析风格检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }]
            }

    def check_characters_storyline(self, characters_info: str, main_storyline: str) -> Dict[str, Any]:
        """
        检查人物是否与故事主线冲突

        Args:
            characters_info: 人物信息
            main_storyline: 故事主线概述

        Returns:
            检查结果字典
        """
        prompt = f"""
        请检查以下小说人物是否与故事主线冲突：

        【人物信息】
        {characters_info}

        【故事主线概述】
        {main_storyline}

        要求：
        1. 人物设定应与故事主线保持一致
        2. 主要人物的背景故事应符合故事主线的背景
        3. 人物的动机和目标应符合故事主线中的角色发展轨迹
        4. 人物之间的关系应符合故事主线中的角色关系
        5. 人物的能力和战力应符合故事主线中的角色能力设定

        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "issues": [
                {{
                    "type": "人物冲突",
                    "character": "角色名",
                    "description": "问题详细描述",
                    "suggestion": "修改建议"
                }}
            ]
        }}
        ```

        如果没有发现与故事主线的冲突，请返回passed为true且issues为空数组。
        """

        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "character": "全部",
                    "description": "人物与故事主线冲突检查失败",
                    "suggestion": "请重新检查"
                }]
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)
            return check_result
        except Exception as e:
            print(f"解析人物与故事主线冲突检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "character": "全部",
                    "description": f"解析人物与故事主线冲突检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }]
            }

    def check_chapter_characters(self, chapter_content: str, characters: str, appearing_characters: str = None) -> Dict[str, Any]:
        """
        检查章节是否与人物设定冲突

        Args:
            chapter_content: 章节内容
            characters: 人物卡片
            appearing_characters: 本章出场人物（可选）

        Returns:
            检查结果字典
        """
        # 构建提示词
        base_prompt = """
        请检查以下小说章节是否与人物设定冲突：

        【章节内容】
        {0}

        【人物卡片】
        {1}
        """

        # 如果提供了出场人物列表，则只检查这些人物
        if appearing_characters:
            base_prompt += """
        【本章出场人物】
        {2}

        要求：
        1. 只检查本章出场人物的设定冲突
        2. 人物言行应符合其性格设定
        3. 人物能力应符合其设定范围
        4. 人物关系应符合已有设定
        5. 人物当前战力应与章节内容表现一致
        6. 应更新本章出场角色的状态和当前战力
        7. 注意：不要创建新的人物，只更新现有人物状态
        """.format(chapter_content, characters, appearing_characters)
        else:
            base_prompt += """
        要求：
        1. 人物言行应符合其性格设定
        2. 人物能力应符合其设定范围
        3. 人物关系应符合已有设定
        4. 人物当前战力应与章节内容表现一致
        5. 应更新现有角色的状态和当前战力
        6. 注意：不要创建新的人物，只更新现有人物状态
        """.format(chapter_content, characters)

        prompt = base_prompt + """
        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "issues": [
                {{
                    "type": "人物冲突",
                    "description": "问题详细描述",
                    "location": "问题在文中的大致位置",
                    "suggestion": "修改建议"
                }}
            ],
            "character_updates": [
                {{
                    "name": "角色名",
                    "updates": {{
                        "current_status": "更新后的状态描述",
                        "current_power": "更新后的战力描述"
                    }}
                }}
            ],
            "appearing_character_names": ["角色名字1", "角色名字2", "角色名字3"]
        }}
        ```

        如果没有发现人物冲突，请返回passed为true且issues为空数组。
        即使没有问题，也必须提供character_updates，确保人物状态和战力得到更新。
        必须提供appearing_character_names，列出本章所有出场的角色名称。
        注意：只更新已有人物状态，不要创建新人物。
        """

        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "description": "人物冲突检查失败",
                    "suggestion": "请重新检查"
                }],
                "character_updates": [],
                "appearing_character_names": []
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)

            # 确保结果包含必要的字段
            if "character_updates" not in check_result:
                check_result["character_updates"] = []
            if "appearing_character_names" not in check_result:
                check_result["appearing_character_names"] = []
            
            # 确保结果不包含new_characters字段

            return check_result
        except Exception as e:
            print(f"解析人物冲突检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "description": f"解析人物冲突检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }],
                "character_updates": [],
                "appearing_character_names": []
            }

    def check_storyline_background(self, main_storyline: str, background: str) -> Dict[str, Any]:
        """
        检查故事主线是否与背景设定冲突

        Args:
            main_storyline: 故事主线概述
            background: 背景设定

        Returns:
            检查结果字典
        """
        prompt = f"""
        请严格检查以下小说故事主线是否与背景设定冲突：

        【故事主线概述】
        {main_storyline}

        【背景设定】
        {background}

        要求：
        1. 故事主线不应与背景设定冲突
        2. 故事主线中的世界观、规则、地理环境等应符合背景设定
        3. 故事主线中的组织、势力、种族等应符合背景设定
        4. 故事主线中不应出现背景设定中未提及的概念、地点、组织、物品、种族或能力
        5. 所有角色的能力和成长路径必须符合背景设定中的修炼体系和世界规则
        6. 故事发生的地点必须是背景设定中已经描述过的地点
        7. 故事中的冲突必须基于背景设定中已有的势力、种族或组织之间的关系

        请特别注意检查故事主线中是否出现了背景设定中未提及的概念，这是最严重的问题。

        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "issues": [
                {{
                    "type": "背景冲突",
                    "description": "问题详细描述",
                    "suggestion": "修改建议"
                }}
            ]
        }}
        ```

        如果没有发现背景冲突，请返回passed为true且issues为空数组。
        如果发现任何背景设定中未提及的概念，请务必标记为问题并提供具体的修改建议。
        """

        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "description": "故事主线与背景设定冲突检查失败",
                    "suggestion": "请重新检查"
                }]
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)
            return check_result
        except Exception as e:
            print(f"解析故事主线与背景设定冲突检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "description": f"解析故事主线与背景设定冲突检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }]
            }

    def check_storyline_style(self, main_storyline: str, style_guide: str) -> Dict[str, Any]:
        """
        检查故事主线是否与风格指南冲突

        Args:
            main_storyline: 故事主线概述
            style_guide: 风格指南

        Returns:
            检查结果字典
        """
        prompt = f"""
        请检查以下小说故事主线是否与风格指南冲突：

        【故事主线概述】
        {main_storyline}

        【风格指南】
        {style_guide}

        要求：
        1. 故事主线应与风格指南保持一致
        2. 故事主线中的文风特点应符合风格指南
        3. 故事主线中的情节发展模式应符合风格指南
        4. 故事主线中的角色塑造特点应符合风格指南
        5. 故事主线中的世界观设定要点应符合风格指南
        6. 故事主线应符合读者期望

        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "issues": [
                {{
                    "type": "风格冲突",
                    "description": "问题详细描述",
                    "suggestion": "修改建议"
                }}
            ]
        }}
        ```

        如果没有发现与风格指南的冲突，请返回passed为true且issues为空数组。
        """

        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "description": "故事主线与风格指南冲突检查失败",
                    "suggestion": "请重新检查"
                }]
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)
            return check_result
        except Exception as e:
            print(f"解析故事主线与风格指南冲突检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "description": f"解析故事主线与风格指南冲突检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }]
            }

    def check_chapter_outline(self, chapter_content: str, chapter_outline: str, foreshadowing: str) -> str:
        """
        检查章节内容是否符合大纲要求，以及伏笔的处理情况

        Args:
            chapter_content: 章节内容
            chapter_outline: 章节大纲
            foreshadowing: 所有未回收伏笔信息

        Returns:
            JSON字符串格式的检查结果，包含是否通过、问题列表、伏笔更新和新伏笔等信息
        """
        try:
            # 解析章节大纲
            outline_data = {}
            if isinstance(chapter_outline, str) and chapter_outline.strip().startswith('{'):
                try:
                    outline_data = json.loads(chapter_outline)
                except:
                    pass

            # 构建提示词
            prompt = """
            请检查以下小说章节内容是否符合大纲要求，以及伏笔的处理情况：

            【章节内容】
            ```
            {0}
            ```

            【章节大纲】
            ```
            {1}
            ```

            【伏笔信息】
            ```
            {2}
            ```

            请检查以下几个方面：
            1. 章节内容是否包含大纲中描述的所有关键情节点
            2. 章节内容是否保持了大纲中要求的开端、发展、高潮、结尾的结构
            3. 章节内容是否包含了大纲中提到的所有主要角色的行动和情感
            4. 章节内容是否正确地埋下了需要埋下的伏笔
            5. 章节内容是否正确地回收了需要回收的伏笔

            请以JSON格式返回检查结果，格式如下：
            ```json
            {{
                "passed": true/false,
                "issues": [
                    {{
                        "type": "问题类型",
                        "description": "问题描述",
                        "suggestion": "修改建议"
                    }}
                ],
                "foreshadowing_updates": [
                    {{
                        "id": "伏笔ID",
                        "status": "新状态"
                    }}
                ]
            }}
            ```

            注意：
            1. 如果没有发现问题，issues数组可以为空
            2. 如果没有伏笔更新，foreshadowing_updates数组可以为空
            3. 伏笔状态可以是：未埋下、已埋下未回收、已回收
            """.format(chapter_content, chapter_outline, foreshadowing)

            # 发送请求并获取响应
            response = self.generate_content(prompt, temperature=0.1, max_tokens=2000)
            if not response:
                error_result = {
                    "passed": False,
                    "issues": [{
                        "type": "生成失败",
                        "description": "无法获取检查结果",
                        "suggestion": "请重试"
                    }],
                    "foreshadowing_updates": [],
                }
                return json.dumps(error_result, ensure_ascii=False)

            # 提取JSON部分
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            json_str = json_match.group(1) if json_match else response

            # 尝试解析JSON
            try:
                result = json.loads(json_str)
            except json.JSONDecodeError:
                try:
                    # 尝试修复JSON格式
                    json_str = self._clean_json_string(json_str, True)
                    result = json.loads(json_str)
                except:
                    # 如果仍然失败，返回默认结果
                    error_result = {
                        "passed": False,
                        "issues": [{
                            "type": "解析错误",
                            "description": "无法解析检查结果",
                            "suggestion": "请重新检查"
                        }],
                        "foreshadowing_updates": []
                    }
                    return json.dumps(error_result, ensure_ascii=False)

            # 确保返回的结果包含所有必要字段
            if "passed" not in result:
                result["passed"] = False
            if "issues" not in result:
                result["issues"] = []
            if "foreshadowing_updates" not in result:
                result["foreshadowing_updates"] = []

            # 返回JSON字符串，而不是Python字典
            return json.dumps(result, ensure_ascii=False)
        except Exception as e:
            import traceback
            traceback.print_exc()
            error_result = {
                "passed": False,
                "issues": [{
                    "type": "检查错误",
                    "description": f"检查过程中出错: {str(e)}",
                    "suggestion": "请重新检查"
                }],
                "foreshadowing_updates": []
            }
            return json.dumps(error_result, ensure_ascii=False)


    def check_chapter_storyline(self, chapter_outline: str, main_storyline: str, current_chapter: int, total_chapters: int) -> Dict[str, Any]:
        """
        检查章节大纲是否与故事主线冲突

        Args:
            chapter_outline: 章节大纲
            main_storyline: 故事主线概述
            current_chapter: 当前章节号
            total_chapters: 总章节数

        Returns:
            检查结果字典
        """
        # 计算当前进度
        progress = current_chapter / total_chapters

        # 从主线中提取当前章节的内容指导
        chapter_guide = None
        try:
            if main_storyline and main_storyline != "无":
                chapter_guide = self._extract_chapter_from_storyline(main_storyline, current_chapter)
        except Exception as e:
            print(f"从主线中提取章节内容指导时出错: {str(e)}")

        # 构建提示词
        # 如果成功提取了章节指导，则使用更具体的检查提示词
        if chapter_guide:
            prompt = f"""
                请检查以下小说章节大纲是否与故事主线中指定的本章内容指导一致：

            【章节大纲】
            {chapter_outline}

                【主线中的本章内容指导】
                {chapter_guide}

            【当前进度】
            当前是第{current_chapter}章，总共{total_chapters}章，完成度约为{progress:.1%}

            要求：
                1. 章节大纲必须与主线中指定的本章内容完全一致
                2. 章节大纲必须包含主线中提到的所有关键事件
                3. 章节大纲必须包含主线中指定的所有出场人物
                4. 章节大纲必须按照主线要求埋下和回收相应的伏笔
                5. 虽然章节大纲可以在细节上有所扩展，但不能改变主线的基本方向

            请以JSON格式返回检查结果：
            ```json
            {{
                "passed": true/false,
                "issues": [
                    {{
                        "type": "主线偏离",
                        "description": "问题详细描述",
                        "suggestion": "修改建议"
                    }}
                ]
            }}
            ```

                如果没有发现与主线内容指导的冲突，请返回passed为true且issues为空数组。
            """
        else:
            # 使用常规检查提示词
            prompt = f"""
            请检查以下小说章节大纲是否与故事主线概述冲突：

            【章节大纲】
            {chapter_outline}

            【故事主线概述】
            {main_storyline}

            【当前进度】
            当前是第{current_chapter}章，总共{total_chapters}章，完成度约为{progress:.1%}

        要求：
            1. 章节大纲应与故事主线概述保持一致
            2. 章节大纲中的情节发展应符合当前进度的预期
            3. 章节大纲中的角色发展应符合故事主线中的角色发展轨迹
            4. 章节大纲中的伏笔埋设和回收应符合故事主线的安排
            5. 章节大纲中的冲突和转折应符合故事主线的整体节奏

        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "issues": [
                {{
                        "type": "主线偏离",
                    "description": "问题详细描述",
                    "suggestion": "修改建议"
                }}
            ]
        }}
        ```

            如果没有发现与故事主线的冲突，请返回passed为true且issues为空数组。
            """

        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "description": "大纲冲突检查失败",
                    "suggestion": "请重新检查"
                }],
                "foreshadowing_updates": []
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)

            # 确保结果包含必要的字段
            if "foreshadowing_updates" not in check_result:
                check_result["foreshadowing_updates"] = []

            return check_result
        except Exception as e:
            print(f"解析大纲冲突检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "description": f"解析大纲冲突检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }],
                "foreshadowing_updates": [],
            }

    def check_chapter(self, chapter_content: str, chapter_outline: str,
                      characters: str, background: str,
                      foreshadowing: str, appearing_characters: str = None) -> Optional[Dict[str, Any]]:
        """
        检查章节内容

        Args:
            chapter_content: 章节内容
            chapter_outline: 章节大纲
            characters: 人物卡片
            background: 背景设定
            foreshadowing: 伏笔管理
            appearing_characters: 本章出场人物（可选）

        Returns:
            检查结果字典，包含是否通过、问题列表和修改建议，如果出错则返回None
        """
        try:
            # 从小说信息中获取流派
            novel_info_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "output", "novel_info.json")
            genre = ""
            if os.path.exists(novel_info_path):
                try:
                    with open(novel_info_path, 'r', encoding='utf-8') as f:
                        novel_info = json.load(f)
                        genre = novel_info.get("genre", "")
                except:
                    pass

            # 1. 检查字数
            print("正在检查章节字数...")
            word_count_result = self.check_chapter_word_count(chapter_content)
            if not word_count_result.get("passed", False):
                print(f"字数检查未通过: {word_count_result.get('issues', [])}")
                return word_count_result

            # 2. 检查风格
            print("正在检查章节风格...")
            style_result = self.check_chapter_style(chapter_content, genre)
            if not style_result.get("passed", False):
                print(f"风格检查未通过: {style_result.get('issues', [])}")
                return style_result

            # 3. 跳过背景冲突检查
            print("跳过背景设定冲突检查，根据要求不检查背景设定...")
            # 创建一个模拟的通过检查结果
            background_result = {
                "passed": True,
                "issues": []
            }

            # 4. 检查人物冲突
            print("正在检查是否与人物设定冲突...")
            characters_result = self.check_chapter_characters(chapter_content, characters, appearing_characters)
            if not characters_result.get("passed", False):
                print(f"人物冲突检查未通过: {characters_result.get('issues', [])}")
                return characters_result

            # 5. 检查大纲冲突
            print("正在检查是否与大纲冲突...")
            outline_result_json = self.check_chapter_outline(chapter_content, chapter_outline, foreshadowing)
            
            try:
                # 解析返回的JSON字符串
                outline_result = json.loads(outline_result_json)
            except json.JSONDecodeError as e:
                print(f"解析大纲检查结果时出错: {str(e)}")
                return {
                    "passed": False,
                    "issues": [{
                        "type": "解析错误",
                        "description": f"无法解析大纲检查结果: {str(e)}",
                        "suggestion": "请重新检查"
                    }]
                }
                
            if not outline_result.get("passed", False):
                print(f"大纲冲突检查未通过: {outline_result.get('issues', [])}")
                return outline_result

            # 合并所有检查结果
            final_result = {
                "passed": True,
                "issues": [],
                "character_updates": characters_result.get("character_updates", []),
                "foreshadowing_updates": outline_result.get("foreshadowing_updates", []),
                "appearing_character_names": characters_result.get("appearing_character_names", [])
            }

            print("章节检查通过！")
            print(f"发现 {len(final_result['character_updates'])} 个人物状态更新")
            print(f"发现 {len(final_result['foreshadowing_updates'])} 个伏笔状态更新")
            print(f"本章出场人物: {', '.join(final_result['appearing_character_names'])}")

            return final_result

        except Exception as e:
            import traceback
            print(f"检查章节时出错: {str(e)}")
            traceback.print_exc()
            return {
                "passed": False,
                "issues": [{
                    "type": "检查错误",
                    "description": f"检查过程中出错: {str(e)}",
                    "location": "整篇",
                    "suggestion": "请重新检查"
                }],
                "character_updates": [],
                "foreshadowing_updates": [],
                "appearing_character_names": []
            }

    def generate_chapter_content(self, chapter_number: int, chapter_outline: str,
                                 background: str, characters: str, foreshadowing: str,
                                 previous_chapters_summary: Optional[str] = None) -> Optional[str]:
        """
        生成章节内容

        Args:
            chapter_number: 章节号
            chapter_outline: 章节大纲
            background: 背景设定
            characters: 人物卡片
            foreshadowing: 伏笔管理
            previous_chapters_summary: 前几章内容摘要（可选）

        Returns:
            章节内容，如果出错则返回None
        """
        previous_summary = previous_chapters_summary if previous_chapters_summary else "这是第一章，无前序内容。"

        # 检查并处理结构化JSON大纲
        chapter_outline_for_prompt = chapter_outline
        try:
            # 如果大纲是JSON格式，转换为更易读的文本格式
            if isinstance(chapter_outline, str) and chapter_outline.strip().startswith('{') and chapter_outline.strip().endswith('}'):
                import json
                outline_data = json.loads(chapter_outline)

                # 构建格式化的大纲文本
                formatted_outline = f"# 第{chapter_number}章 {outline_data.get('title', '').replace(f'第{chapter_number}章', '').strip()}\n\n"

                # 添加章节概要
                if 'chapter_summary' in outline_data and isinstance(outline_data['chapter_summary'], dict):
                    summary = outline_data['chapter_summary']
                    formatted_outline += "## 章节概要\n\n"

                    if summary.get('opening'):
                        formatted_outline += f"### 开端\n{summary['opening']}\n\n"

                    if summary.get('development'):
                        formatted_outline += f"### 发展\n{summary['development']}\n\n"

                    if summary.get('climax'):
                        formatted_outline += f"### 高潮\n{summary['climax']}\n\n"

                    if summary.get('ending'):
                        formatted_outline += f"### 结尾\n{summary['ending']}\n\n"

                # 添加出场人物
                if 'characters' in outline_data and outline_data['characters']:
                    formatted_outline += "## 出场人物\n"
                    for char in outline_data['characters']:
                        formatted_outline += f"- **{char.get('name', '')}**: {char.get('actions', '')}\n"
                    formatted_outline += "\n"

                # 添加关键情节点
                if 'key_points' in outline_data and outline_data['key_points']:
                    formatted_outline += "## 关键情节点\n"
                    for i, point in enumerate(outline_data['key_points'], 1):
                        formatted_outline += f"{i}. {point}\n"
                    formatted_outline += "\n"

                # 添加伏笔安排
                if 'foreshadowings' in outline_data and isinstance(outline_data['foreshadowings'], dict):
                    fs = outline_data['foreshadowings']
                    formatted_outline += "## 伏笔安排\n"

                    if 'planted' in fs and fs['planted']:
                        formatted_outline += "- **新埋下**：\n"
                        for plant in fs['planted']:
                            formatted_outline += f"  - {plant.get('id', '')}: {plant.get('content', '')}, 埋下方式: {plant.get('method', '')}\n"

                    if 'revealed' in fs and fs['revealed']:
                        formatted_outline += "- **回收的**：\n"
                        for reveal in fs['revealed']:
                            formatted_outline += f"  - {reveal.get('id', '')}: {reveal.get('content', '')}, 回收效果: {reveal.get('effect', '')}\n"

                # 使用格式化后的大纲
                chapter_outline_for_prompt = formatted_outline
                print(f"已将JSON大纲转换为可读文本格式，用于章节内容生成")
        except Exception as e:
            print(f"处理JSON大纲时出错: {e}，将使用原始大纲")
            # 出错时使用原始大纲
            chapter_outline_for_prompt = chapter_outline

        prompt = """
        请根据以下信息，创作一篇高质量的男频网络小说章节：

        【章节信息】
        章节号：第{0}章

        【章节大纲】
        {1}

        【背景设定】
        {2}

        【人物卡片】
        {3}

        【伏笔管理】
        {4}

        【前序内容摘要】
        {5}

        创作要求：
        1. 严格按照大纲内容创作，不要偏离大纲设定的情节和走向
        2. 恪守背景设定和人物设定，不要创造与已有设定冲突的内容
        3. 适当埋下和回收伏笔（参考伏笔管理）
        4. 合理展现人物性格和关系发展
        5. 文风要符合男频网络小说特点，富有代入感和爽感
        6. 章节长度约1万字，节奏要流畅，情节有起伏
        7. 使用第三人称视角，以主角为中心展开叙述
        8. 注意小说细节和场景描写，增强代入感
        9. 必须提供完整的章节标题，格式为"第{0}章 标题"
        10. 如果不是第一章或视角切换，必须与前一章结尾自然衔接
        11. 不要在正文中包含任何元数据标记，如"**高潮（星陨异变）**"、"**第五章 葬神寒渊劫（高潮部分）**"等
        12. 不要在正文中包含任何非小说内容的标记或注释
        13. 【重要】所有概念、地点、组织、物品、种族、能力等必须来自背景设定中已有的内容
        14. 【重要】不要创造背景设定中未提及的新概念、新地点、新组织、新物品、新种族或新能力
        15. 【重要】所有角色的能力和成长路径必须符合背景设定中的修炼体系和世界规则

        请直接输出小说章节内容，不要输出其他无关内容。
        """.format(chapter_number, chapter_outline_for_prompt, background, characters, foreshadowing, previous_summary)

        result = self.generate_content(prompt, temperature=0.8)
        if result:
            # 移除内容开头的元数据和说明
            # 移除类似 "（以下是严格按您要求扩充至10000字的完整章节内容，所有新增内容均用下划线标出）" 的说明
            result = re.sub(r'^（[^）]*）[\n\r]+', '', result)
            result = re.sub(r'^【[^】]*】[\n\r]+', '', result)
            result = re.sub(r'^---[\n\r]+', '', result)

            # 移除可能存在的元数据标记
            # 移除类似 "**高潮（星陨异变）**" 的标记
            result = re.sub(r'\*\*[^*]+\*\*', '', result)
            # 移除类似 "（高潮部分）" 的标记
            result = re.sub(r'（[^）]*部分[^）]*）', '', result)
            # 移除类似 "（完整扩充版）" 的标记
            result = re.sub(r'（[^）]*扩充[^）]*）', '', result)
            # 移除下划线标记
            result = re.sub(r'_(.+?)_', r'\1', result)

            # 尝试提取章节标题
            # 首先检查是否有Markdown格式的标题
            markdown_title_match = re.search(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*(.+?)[\n\r]', result)
            if markdown_title_match and markdown_title_match.group(1).strip():
                # 提取Markdown标题中的实际标题部分
                title = markdown_title_match.group(1).strip()
                full_title = f"第{chapter_number}章 {title}"
                # 移除Markdown标题行
                result = re.sub(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]', '', result, 1)
            else:
                # 检查是否有完整的"第X章 标题"格式
                full_title_match = re.search(r'第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*(.+?)[\n\r]', result)

                # 如果找到完整标题
                if full_title_match and full_title_match.group(1).strip():
                    title = full_title_match.group(1).strip()
                    full_title = f"第{chapter_number}章 {title}"
                else:
                    # 如果只找到"第X章"而没有具体标题
                    chapter_only_match = re.search(r'(第[一二三四五六七八九十百千万\d]+章)\s*[\n\r]', result)

                    # 从大纲中提取标题
                    title_from_outline = self._extract_title_from_outline(chapter_outline)

                    # 如果从大纲中提取到了标题，直接使用
                    if title_from_outline:
                        title = title_from_outline
                        print(f"从大纲中提取到标题：{title}")
                    else:
                        # 如果从大纲中提取不到标题，使用默认标题
                        title = f"未命名章节{chapter_number}"
                        print(f"未从大纲中提取到标题，使用默认标题：{title}")

                    full_title = f"第{chapter_number}章 {title}"

            # 检查内容是否完整（最后一个句子是否以标点符号结尾）
            last_sentence_incomplete = False
            if result.strip():
                last_char = result.strip()[-1]
                if last_char not in '。！？.!?"\'》）)':
                    last_sentence_incomplete = True
                    # 添加一个结束符号
                    result = result.strip() + "。"

            # 格式化章节内容
            # 检查是否已经包含章节标题
            if not result.strip().startswith(full_title) and not result.strip().startswith(f"第{chapter_number}章"):
                # 添加完整标题
                content = f"{full_title}\n\n{result}"
            else:
                # 替换可能不完整的标题
                if 'chapter_only_match' in locals() and chapter_only_match:
                    content = re.sub(r'第[一二三四五六七八九十百千万\d]+章\s*[\n\r]', f"{full_title}\n\n", result, 1)
                else:
                    content = result

            # 如果内容不完整，添加警告日志
            if last_sentence_incomplete:
                print(f"警告：第{chapter_number}章内容可能不完整，已自动添加结束符号")

            return content

    def generate_chapter_segment(self, segment_type: str, chapter_number: int,
                                 chapter_outline: str, background: str,
                                 characters: str, foreshadowing: str,
                                 previous_content: Optional[str] = None) -> Optional[str]:
        """
        生成章节片段

        Args:
            segment_type: 片段类型（开端/发展/高潮/结尾）
            chapter_number: 章节号
            chapter_outline: 章节大纲
            background: 背景设定
            characters: 人物卡片
            foreshadowing: 伏笔管理
            previous_content: 前面已生成的内容或前一章结尾内容（可选）

        Returns:
            章节片段，如果出错则返回None
        """
        prev_content = previous_content if previous_content else "（无前序内容）"

        # 判断是否是开头部分且有前一章内容
        is_opening_with_prev_chapter = segment_type == "开端" and previous_content and "（无前序内容）" not in previous_content

        # 从大纲中提取总章节数信息
        total_chapters = 0
        try:
            # 尝试从大纲中提取"当前章节：X/Y"格式的信息
            chapter_info_match = re.search(r'当前章节[：:]\s*\d+/(\d+)', chapter_outline)
            if chapter_info_match:
                total_chapters = int(chapter_info_match.group(1))
            else:
                # 默认设置为100章，避免误判为最终章
                total_chapters = 100
        except:
            total_chapters = 100

        # 检查并处理结构化JSON大纲
        chapter_outline_for_prompt = chapter_outline
        try:
            # 如果大纲是JSON格式，转换为更易读的文本格式
            if isinstance(chapter_outline, str) and chapter_outline.strip().startswith('{') and chapter_outline.strip().endswith('}'):
                import json
                outline_data = json.loads(chapter_outline)

                # 构建格式化的大纲文本，重点强调当前需要生成的段落部分
                formatted_outline = f"# 第{chapter_number}章 {outline_data.get('title', '').replace(f'第{chapter_number}章', '').strip()}\n\n"

                # 添加章节概要，突出当前段落
                if 'chapter_summary' in outline_data and isinstance(outline_data['chapter_summary'], dict):
                    summary = outline_data['chapter_summary']
                    formatted_outline += "## 章节概要\n\n"

                    # 根据当前生成的段落类型，添加特殊标记
                    if segment_type == "开端" and summary.get('opening'):
                        formatted_outline += f"### 开端 【当前需要生成部分】\n{summary['opening']}\n\n"
                    elif summary.get('opening'):
                        formatted_outline += f"### 开端\n{summary['opening']}\n\n"

                    if segment_type == "发展" and summary.get('development'):
                        formatted_outline += f"### 发展 【当前需要生成部分】\n{summary['development']}\n\n"
                    elif summary.get('development'):
                        formatted_outline += f"### 发展\n{summary['development']}\n\n"

                    if segment_type == "高潮" and summary.get('climax'):
                        formatted_outline += f"### 高潮 【当前需要生成部分】\n{summary['climax']}\n\n"
                    elif summary.get('climax'):
                        formatted_outline += f"### 高潮\n{summary['climax']}\n\n"

                    if segment_type == "结尾" and summary.get('ending'):
                        formatted_outline += f"### 结尾 【当前需要生成部分】\n{summary['ending']}\n\n"
                    elif summary.get('ending'):
                        formatted_outline += f"### 结尾\n{summary['ending']}\n\n"

                # 添加出场人物
                if 'characters' in outline_data and outline_data['characters']:
                    formatted_outline += "## 出场人物\n"
                    for char in outline_data['characters']:
                        formatted_outline += f"- **{char.get('name', '')}**: {char.get('actions', '')}\n"
                    formatted_outline += "\n"

                # 添加关键情节点
                if 'key_points' in outline_data and outline_data['key_points']:
                    formatted_outline += "## 关键情节点\n"
                    for i, point in enumerate(outline_data['key_points'], 1):
                        formatted_outline += f"{i}. {point}\n"
                    formatted_outline += "\n"

                # 添加伏笔安排
                if 'foreshadowings' in outline_data and isinstance(outline_data['foreshadowings'], dict):
                    fs = outline_data['foreshadowings']
                    formatted_outline += "## 伏笔安排\n"

                    if 'planted' in fs and fs['planted']:
                        formatted_outline += "- **新埋下**：\n"
                        for plant in fs['planted']:
                            formatted_outline += f"  - {plant.get('id', '')}: {plant.get('content', '')}, 埋下方式: {plant.get('method', '')}\n"

                    if 'revealed' in fs and fs['revealed']:
                        formatted_outline += "- **回收的**：\n"
                        for reveal in fs['revealed']:
                            formatted_outline += f"  - {reveal.get('id', '')}: {reveal.get('content', '')}, 回收效果: {reveal.get('effect', '')}\n"

                # 使用格式化后的大纲
                chapter_outline_for_prompt = formatted_outline
                print(f"已将JSON大纲转换为可读文本格式，用于生成{segment_type}段落")
        except Exception as e:
            print(f"处理JSON大纲时出错: {e}，将使用原始大纲")
            # 出错时使用原始大纲
            chapter_outline_for_prompt = chapter_outline

        prompt = """
        请根据以下信息，创作一篇高质量的男频网络小说章节的{0}部分：

        【章节信息】
        章节号：第{1}章

        【章节大纲】
        {2}

        【背景设定】
        {3}

        【人物卡片】
        {4}

        【伏笔管理】
        {5}

        【已生成的前序内容】
        {6}

        创作要求：
        1. 严格按照大纲内容创作，不要偏离大纲设定的情节和走向
        2. 专注于{0}部分的内容创作
        """.format(segment_type, chapter_number, chapter_outline_for_prompt, background, characters, foreshadowing, prev_content)

        # 根据片段类型添加特定要求
        if segment_type == "开端":
            # 检查是否有前一章结尾内容
            if previous_content and "（无前序内容）" not in previous_content and chapter_number > 1:
                prompt += f"""
        3. 开端部分需要：
            - 必须包含完整的章节标题（格式为"第{chapter_number}章 标题"）
            - 必须与前一章结尾自然衔接，前一章结尾内容如下：

            【前一章结尾内容】
            {previous_content}

            - 设置场景和氛围
            - 引入主要人物
            - 埋下本章核心冲突/问题的伏笔
            - 保持节奏流畅，引人入胜
            - 字数控制在2000-3000字左右
        """
            else:
                prompt += f"""
        3. 开端部分需要：
            - 必须包含完整的章节标题（格式为"第{chapter_number}章 标题"）
            - 如果不是第一章或视角切换，必须与前一章结尾自然衔接
            - 设置场景和氛围
            - 引入主要人物
            - 埋下本章核心冲突/问题的伏笔
            - 保持节奏流畅，引人入胜
            - 字数控制在2000-3000字左右
        """
        elif segment_type == "发展":
            prompt += """
        3. 发展部分需要：
            - 推动情节向前发展
            - 展现角色互动和冲突
            - 增加情节复杂度和张力
            - 设置次级高潮
            - 埋下新的伏笔或回收前文伏笔
            - 字数控制在3000-4000字左右
        """
        elif segment_type == "高潮":
            prompt += """
        3. 高潮部分需要：
            - 呈现章节的主要冲突和关键场景
            - 展现角色在面临重大挑战时的表现
            - 安排精彩的战斗/对决/突破场景
            - 制造情感共鸣点
            - 展现关键的伏笔回收
            - 字数控制在2000-3000字左右
        """
        elif segment_type == "结尾":
            # 检查是否是最后一章
            if chapter_number == total_chapters:
                prompt += f"""
        3. 结尾部分需要（最终章）：
            - 合理解决本章和整个故事的主要冲突
            - 展示角色的最终成长或变化
            - 提供明确的结局
            - 回收所有重要伏笔
            - 提供情感上的满足感和完整感
            - 字数控制在2000-3000字左右
        """
            else:
                prompt += f"""
        3. 结尾部分需要：
            - 合理解决本章的主要冲突
            - 展示角色的成长或变化
            - 埋下下一章的伏笔或悬念
            - 为下一章做好铺垫，创造自然过渡
            - 结尾场景和情绪应该能够与下一章开头形成连贯
            - 提供情感上的满足感
            - 保持与整体故事线的连贯性
            - 字数控制在1000-2000字左右
        """

        prompt += """
        4. 确保内容逻辑连贯，与前序内容无缝衔接
        5. 文风要符合男频网络小说特点，富有代入感和爽感
        6. 使用第三人称视角，以主角为中心展开叙述
        7. 注意小说细节和场景描写，增强代入感
        8. 不要在正文中包含任何元数据标记，如"**高潮（星陨异变）**"、"**第五章 葬神寒渊劫（高潮部分）**"等
        9. 不要在正文中包含任何非小说内容的标记或注释

        请直接输出小说章节{0}部分内容，不要输出其他无关内容。
        """.format(segment_type) # Added segment_type here for the final instruction

        result = self.generate_content(prompt, temperature=0.8)

        if result:
            # 移除可能存在的元数据标记
            # 移除类似 "**高潮（星陨异变）**" 的标记
            result = re.sub(r'\*\*[^*]+\*\*', '', result)
            # 移除类似 "（高潮部分）" 的标记
            result = re.sub(r'（[^）]*部分[^）]*）', '', result)
            # 移除类似 "（完整扩充版）" 的标记
            result = re.sub(r'（[^）]*扩充[^）]*）', '', result)

        return result

    def expand_chapter_content(self, chapter_content: str, expansion_suggestions: List[str],
                               chapter_outline: str, background: str, characters: str,
                               foreshadowing: str) -> Optional[str]:
        """
        根据建议扩充章节内容

        Args:
            chapter_content: 原章节内容
            expansion_suggestions: 扩充建议列表
            chapter_outline: 章节大纲
            background: 背景设定
            characters: 人物卡片
            foreshadowing: 伏笔管理

        Returns:
            扩充后的章节内容，如果出错则返回None
        """
        # 处理结构化JSON大纲
        chapter_outline_for_prompt = chapter_outline
        try:
            # 如果大纲是JSON格式，转换为更易读的文本格式
            if isinstance(chapter_outline, str) and chapter_outline.strip().startswith('{') and chapter_outline.strip().endswith('}'):
                import json
                outline_data = json.loads(chapter_outline)

                # 提取章节号
                chapter_number = 0
                title_match = re.search(r'第(\d+)章', outline_data.get('title', ''))
                if title_match:
                    try:
                        chapter_number = int(title_match.group(1))
                    except:
                        pass

                # 构建格式化的大纲文本
                formatted_outline = f"# {outline_data.get('title', '')}\n\n"

                # 添加章节概要
                if 'chapter_summary' in outline_data and isinstance(outline_data['chapter_summary'], dict):
                    summary = outline_data['chapter_summary']
                    formatted_outline += "## 章节概要\n\n"

                    if summary.get('opening'):
                        formatted_outline += f"### 开端\n{summary['opening']}\n\n"

                    if summary.get('development'):
                        formatted_outline += f"### 发展\n{summary['development']}\n\n"

                    if summary.get('climax'):
                        formatted_outline += f"### 高潮\n{summary['climax']}\n\n"

                    if summary.get('ending'):
                        formatted_outline += f"### 结尾\n{summary['ending']}\n\n"

                # 添加出场人物
                if 'characters' in outline_data and outline_data['characters']:
                    formatted_outline += "## 出场人物\n"
                    for char in outline_data['characters']:
                        formatted_outline += f"- **{char.get('name', '')}**: {char.get('actions', '')}\n"
                    formatted_outline += "\n"

                # 添加关键情节点
                if 'key_points' in outline_data and outline_data['key_points']:
                    formatted_outline += "## 关键情节点\n"
                    for i, point in enumerate(outline_data['key_points'], 1):
                        formatted_outline += f"{i}. {point}\n"
                    formatted_outline += "\n"

                # 添加伏笔安排
                if 'foreshadowings' in outline_data and isinstance(outline_data['foreshadowings'], dict):
                    fs = outline_data['foreshadowings']
                    formatted_outline += "## 伏笔安排\n"

                    if 'planted' in fs and fs['planted']:
                        formatted_outline += "- **新埋下**：\n"
                        for plant in fs['planted']:
                            formatted_outline += f"  - {plant.get('id', '')}: {plant.get('content', '')}, 埋下方式: {plant.get('method', '')}\n"

                    if 'revealed' in fs and fs['revealed']:
                        formatted_outline += "- **回收的**：\n"
                        for reveal in fs['revealed']:
                            formatted_outline += f"  - {reveal.get('id', '')}: {reveal.get('content', '')}, 回收效果: {reveal.get('effect', '')}\n"

                # 使用格式化后的大纲
                chapter_outline_for_prompt = formatted_outline
                print(f"已将JSON大纲转换为可读文本格式，用于章节内容扩展")
        except Exception as e:
            print(f"处理JSON大纲时出错: {e}，将使用原始大纲")
            # 出错时使用原始大纲
            chapter_outline_for_prompt = chapter_outline

        # 将扩充建议转换为文本
        suggestions_text = "\n".join([f"- {suggestion}" for suggestion in expansion_suggestions])

        prompt = """
        请根据以下信息，扩充男频网络小说章节内容，使其达到要求的字数（至少8000字）：

        【章节大纲】
        {0}

        【背景设定】
        {1}

        【人物卡片】
        {2}

        【伏笔管理】
        {3}

        【原章节内容】
        {4}

        【扩充建议】
        {5}

        扩充要求：
        1. 根据提供的扩充建议，有针对性地扩充内容
        2. 保持整体风格和流畅性，确保扩充内容与原内容无缝衔接
        3. 确保扩充后的内容符合大纲、背景设定和人物设定
        4. 不要改变原章节的基本情节走向
        5. 扩充后的总字数应达到至少8000字，理想字数为10000字
        6. 扩充内容应该是对原内容的丰富和补充，而不是简单的重复
        7. 可以适当增加细节描写、内心独白、场景渲染、战斗描写等
        8. 可以适当增加对世界观和人物关系的展开

        请直接输出扩充后的完整章节内容，不要输出其他无关内容或说明。
        """.format(chapter_outline_for_prompt, background, characters, foreshadowing, chapter_content, suggestions_text)

        return self.generate_content(prompt, temperature=0.7)

    def generate_chapter(self, chapter_number: int, chapter_outline: str,
                         background: str, characters: str, foreshadowing: str,
                         previous_chapters_summary: Optional[str] = None) -> Optional[str]:
        """
        一次性生成完整章节内容

        Args:
            chapter_number: 章节号
            chapter_outline: 章节大纲
            background: 背景设定
            characters: 人物卡片
            foreshadowing: 伏笔管理
            previous_chapters_summary: 前几章内容摘要（可选）

        Returns:
            完整的章节内容，如果出错则返回None
        """
        # 这个方法与generate_chapter_content基本相同，但名称不同以兼容现有代码
        return self.generate_chapter_content(
            chapter_number, chapter_outline, background, characters,
            foreshadowing, previous_chapters_summary
        )

    def _clean_json_string(self, json_str: str, aggressive: bool = False) -> str:
        """
        清理JSON字符串，修复常见的格式问题

        Args:
            json_str: 原始JSON字符串
            aggressive: 是否使用更激进的清理方法

        Returns:
            清理后的JSON字符串
        """
        # 记录原始JSON字符串（截断以避免日志过长）
        if len(json_str) > 200:
            print(f"原始JSON（前200字符）: {json_str[:200]}...")
        else:
            print(f"原始JSON: {json_str}")

        # 记录是否使用激进模式
        print(f"使用{'激进' if aggressive else '常规'}清理模式")
        
        # 移除Markdown代码块标记
        original = json_str
        # 移除整个代码块
        json_str = re.sub(r'```json\s*(.*?)\s*```', r'\1', json_str, flags=re.DOTALL)
        # 移除开头的标记
        json_str = re.sub(r'^```\w*\s*', '', json_str)
        # 移除结尾的标记
        json_str = re.sub(r'\s*```\s*$', '', json_str)
        if original != json_str:
            print("移除Markdown代码块标记")

        # 移除注释
        json_str = re.sub(r'//.*?(\n|$)', '', json_str)

        # 处理换行和缩进问题
        if aggressive:
            # 更激进的清理：移除所有换行和多余空格
            original = json_str
            json_str = re.sub(r'\s+', ' ', json_str)
            if original != json_str:
                print("应用激进清理: 移除所有换行和多余空格")

            # 在冒号后添加一个空格
            original = json_str
            json_str = re.sub(r':\s*', ': ', json_str)
            if original != json_str:
                print("应用激进清理: 规范化冒号后的空格")

            # 在逗号后添加一个空格
            original = json_str
            json_str = re.sub(r',\s*', ', ', json_str)
            if original != json_str:
                print("应用激进清理: 规范化逗号后的空格")
        else:
            # 常规清理：规范化换行和缩进
            original = json_str
            json_str = re.sub(r'\n\s*"', ' "', json_str)
            if original != json_str:
                print("应用常规清理: 规范化换行和缩进")

        # 修复引号问题
        original = json_str
        json_str = json_str.replace('\\"', '"')  # 移除转义的引号
        if original != json_str:
            print("修复: 移除转义的引号")

        original = json_str
        json_str = json_str.replace('""', '"')   # 修复双引号问题
        if original != json_str:
            print("修复: 修复双引号问题")

        # 修复属性名问题
        original = json_str
        json_str = re.sub(r'{\s*([^"{\s][^:]*?):', r'{ "\1":', json_str)  # 修复开头的属性名
        if original != json_str:
            print("修复: 修复开头的属性名")

        original = json_str
        json_str = re.sub(r',\s*([^"{\s][^:]*?):', r', "\1":', json_str)  # 修复中间的属性名
        if original != json_str:
            print("修复: 修复中间的属性名")

        # 修复多余的逗号
        original = json_str
        json_str = re.sub(r',\s*}', '}', json_str)  # 移除对象末尾多余的逗号
        if original != json_str:
            print("修复: 移除对象末尾多余的逗号")

        original = json_str
        json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组末尾多余的逗号
        if original != json_str:
            print("修复: 移除数组末尾多余的逗号")

        # 修复单引号问题
        original = json_str
        json_str = json_str.replace("'", '"')
        if original != json_str:
            print("修复: 将单引号替换为双引号")

        # 修复特殊问题：'\n          "title"' 这样的格式
        original = json_str
        json_str = re.sub(r'\\n\s+"', ' "', json_str)
        if original != json_str:
            print("修复: 处理特殊的换行+空格+引号格式")

        # 记录清理后的JSON字符串（截断以避免日志过长）
        if len(json_str) > 200:
            print(f"清理后JSON（前200字符）: {json_str[:200]}...")
        else:
            print(f"清理后JSON: {json_str}")

        return json_str

    def _extract_chapter_from_storyline(self, main_storyline: str, chapter_number: int) -> Optional[str]:
        """
        从故事主线中提取特定章节的内容

        Args:
            main_storyline: 故事主线(JSON格式或Python对象)
            chapter_number: 章节号

        Returns:
            章节内容，如果无法提取则返回None
        """
        try:
            print(f"提取第{chapter_number}章内容，主线类型为: {type(main_storyline)}")

            # 标准化数据类型
            storyline_data = None

            # 如果已经是字典或列表类型(已结构化)
            if isinstance(main_storyline, (dict, list)):
                storyline_data = self._normalize_storyline_data(main_storyline)
            else:
                # 处理可能是包装格式的情况
                if isinstance(main_storyline, str):
                    # 1. 首先尝试标准解析
                    try:
                        # 在解析前清理JSON字符串
                        cleaned_storyline = self._clean_json_string(main_storyline)
                        storyline_data = json.loads(cleaned_storyline)
                        storyline_data = self._normalize_storyline_data(storyline_data)
                        print("标准JSON解析成功")
                    except json.JSONDecodeError as e:
                        print(f"解析JSON失败: {e}，尝试修复")

                        # 2. 使用LLM修复（优先）
                        try:
                            from utils.json_helper import llm_fix_json
                            print("尝试使用LLM修复故事主线JSON...")
                            fixed_json_str = llm_fix_json(main_storyline, "故事主线", self)
                            if fixed_json_str:
                                try:
                                    storyline_data = json.loads(fixed_json_str)
                                    storyline_data = self._normalize_storyline_data(storyline_data)
                                    print("使用LLM成功修复故事主线JSON")
                                except json.JSONDecodeError:
                                    print("LLM修复后的JSON仍然无法解析，尝试其他方法")
                        except ImportError:
                            print("llm_fix_json不可用，尝试其他方法")
                        except Exception as e:
                            print(f"LLM修复失败: {e}，尝试其他方法")

                        # 3. 如果LLM修复失败，尝试使用专用修复工具
                        if storyline_data is None:
                            try:
                                from utils.fix_storyline import fix_storyline_json, get_chapter_from_storyline
                                print("尝试使用专用工具修复...")
                                # 解析故事主线JSON
                                storyline_data = fix_storyline_json(main_storyline)

                                # 使用专用函数获取章节
                                chapter = get_chapter_from_storyline(storyline_data, chapter_number)
                                if chapter:
                                    return chapter.get("content")
                            except ImportError:
                                print("未找到专用修复工具，尝试内置修复方法")
                                pass
                            except Exception as e:
                                print(f"专用工具修复失败: {e}，尝试内置修复方法")

                        # 4. 最后尝试使用内置修复方法
                        if storyline_data is None:
                            fixed_json = self._fix_json(main_storyline)
                            if fixed_json:
                                storyline_data = self._normalize_storyline_data(fixed_json)
                            else:
                                print("修复JSON失败")
                                return None
                else:
                    print(f"不支持的主线类型: {type(main_storyline)}")
                    return None

            # 如果前面的专用方法没有返回结果，使用标准方法提取章节
            outlines = storyline_data.get("outlines") or storyline_data.get("chapter_outlines") or []

            # 查找具有匹配chapter_number或index的章节
            for chapter in outlines:
                if isinstance(chapter, dict):
                    chapter_idx = chapter.get("chapter_number") or chapter.get("index")
                    if chapter_idx == chapter_number:
                        return chapter.get("content")

            print(f"未找到第{chapter_number}章的内容")
            return None

        except Exception as e:
            print(f"提取章节时出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _normalize_storyline_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        规范化故事主线数据结构，确保只使用outlines字段

        Args:
            data: 故事主线数据

        Returns:
            规范化后的数据
        """
        # 创建副本，避免修改原始数据
        normalized_data = data.copy() if isinstance(data, dict) else {"outlines": data} if isinstance(data, list) else {"outlines": []}

        # 确保有outlines字段
        if "chapter_outlines" in normalized_data and "outlines" not in normalized_data:
            normalized_data["outlines"] = normalized_data["chapter_outlines"]

        # 如果同时有两个字段，确保内容一致并删除chapter_outlines
        if "outlines" in normalized_data and "chapter_outlines" in normalized_data:
            # 如果outlines为空但chapter_outlines有值，使用chapter_outlines的值
            if not normalized_data["outlines"] and normalized_data["chapter_outlines"]:
                normalized_data["outlines"] = normalized_data["chapter_outlines"]

            # 删除chapter_outlines字段
            del normalized_data["chapter_outlines"]

        return normalized_data

    def _fix_json(self, json_str: str) -> Optional[Dict[str, Any]]:
        """
        修复API返回的JSON字符串

        Args:
            json_str: JSON字符串

        Returns:
            Dict: 修复后的JSON数据，失败则返回None
        """
        if not json_str:
            return None

        # 首先尝试使用LLM直接修复
        try:
            from utils.json_helper import llm_fix_json
            print("尝试使用LLM修复JSON...")
            fixed_json_str = llm_fix_json(json_str, "通用JSON", self)
            if fixed_json_str:
                try:
                    return json.loads(fixed_json_str)
                except json.JSONDecodeError:
                    print("LLM修复后的JSON仍然无法解析，尝试其他方法")
        except ImportError:
            print("llm_fix_json not available，尝试其他方法")
            pass
        except Exception as e:
            print(f"LLM修复JSON失败: {e}，尝试其他方法")

        # 如果LLM修复失败，尝试使用专门的修复工具
        try:
            from utils.fix_storyline import fix_storyline_json
            print("尝试使用fix_storyline_json修复...")
            return fix_storyline_json(json_str)
        except ImportError:
            print("fix_storyline_json not found，尝试其他方法")
            pass
        except Exception as e:
            print(f"使用fix_storyline_json修复失败: {e}，尝试其他方法")

        # 尝试从utils.json_helper导入fix_json
        try:
            from utils.json_helper import fix_json
            print("尝试使用fix_json修复...")
            fixed_json_str = fix_json(json_str)
            return json.loads(fixed_json_str)
        except ImportError:
            print("fix_json not found，尝试内置修复方法")
            pass
        except Exception as e:
            print(f"使用fix_json修复失败: {e}，尝试内置方法")

        # 使用内置修复方法
        try:
            # 简单修复一些常见问题
            print("尝试使用内置修复方法...")
            json_str = json_str.replace('\\', '\\\\')  # 处理反斜杠
            json_str = re.sub(r'(?<!\\)"([^"]*?)":\s*"([^"]*?)(?<!\\)"([,}\]])', r'"\1":"\2\"\3', json_str)  # 修复引号
            json_str = re.sub(r',\s*}', '}', json_str)  # 移除结尾多余的逗号
            json_str = re.sub(r',\s*]', ']', json_str)  # 移除结尾多余的逗号

            # 尝试解析
            return json.loads(json_str)
        except Exception as e:
            print(f"所有JSON修复方法均失败: {e}")
            return None

    def regenerate_chapter_outline(self, genre: str, style_guide: str, background: str,
                              current_chapter: int, total_chapters: int,
                              previous_chapters_summary: Optional[str] = None,
                              characters_info: Optional[str] = None,
                              foreshadowing_info: Optional[str] = None,
                              appearing_characters: Optional[str] = None,
                              main_storyline: Optional[str] = None,
                              original_outline: Optional[str] = None) -> Optional[str]:
        """
        重新生成章节大纲，基于原有内容进行修改
        
        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            current_chapter: 当前章节号
            total_chapters: 总章节数
            previous_chapters_summary: 前几章的摘要
            characters_info: 人物信息
            foreshadowing_info: 伏笔信息
            appearing_characters: 本章出场人物
            main_storyline: 主线剧情
            original_outline: 原有的章节大纲
            
        Returns:
            重新生成的章节大纲，如果失败返回None
        """
        try:
            # 提取当前章节在主线中的内容
            chapter_guide = None
            if main_storyline:
                chapter_guide = self._extract_chapter_from_storyline(main_storyline, current_chapter)
                
            # 构建提示词
            prompt = f"""
            【任务】重新生成网络小说第{current_chapter}章大纲，修改和完善现有内容

            【小说背景】
            流派：{genre}
            风格指南：{style_guide}
            世界观设定：{background}
            当前章节：第{current_chapter}章（共{total_chapters}章）
            """

            # 如果有章节指南，添加到提示词中
            if chapter_guide:
                prompt += f"""
                【章节主线指南】
                {chapter_guide}
                """

            # 如果有前面章节的摘要，添加到提示词中
            if previous_chapters_summary:
                prompt += f"""
                【前面章节摘要】
                {previous_chapters_summary}
                """

            # 如果有人物信息，添加到提示词中
            if characters_info:
                prompt += f"""
                【人物信息】
                {characters_info}
                """

            # 如果有伏笔信息，添加到提示词中
            if foreshadowing_info:
                prompt += f"""
                【伏笔设定】
                {foreshadowing_info}
                """

            # 如果有本章出场人物，添加到提示词中
            if appearing_characters:
                prompt += f"""
                【本章出场人物】
                {appearing_characters}
                """
                
            # 添加原有大纲内容
            if original_outline:
                prompt += f"""
                【原有大纲内容】
                {original_outline}
                
                请基于以上原有大纲内容进行修改和完善，不要完全重写，保留原有的核心情节和角色。
                主要对以下方面进行改进：
                1. 修复与人物设定、世界观的冲突
                2. 增强情节的连贯性和吸引力
                3. 适当调整对话和场景描写，使其更符合风格指南
                4. 确保合理的章节长度和内容密度
                """
            
            prompt += """
            请生成一个详细的章节大纲，包括以下内容：
            1. 章节标题
            2. 章节摘要（包括开篇、发展、高潮）
            3. 本章出场的重要人物及他们的行动
            4. 关键情节点和场景描写
            5. 本章涉及的伏笔安排

            输出格式要求为干净的JSON格式：
            ```json
            {
              "title": "第X章 章节标题",
              "chapter_summary": {
                "opening": "开篇内容描述",
                "development": "情节发展描述",
                "climax": "高潮部分描述"
              },
              "characters": [
                {
                  "name": "角色名",
                  "actions": ["行动1", "行动2"]
                }
              ],
              "key_points": [
                "关键情节点1",
                "关键情节点2"
              ],
              "scenes": [
                {
                  "description": "场景描述",
                  "characters": ["角色1", "角色2"],
                  "events": ["事件1", "事件2"]
                }
              ],
              "foreshadowings": [
                {
                  "type": "设置/揭示",
                  "description": "伏笔描述"
                }
              ]
            }
            ```

            注意：
            1. 确保JSON格式正确，特别是避免常见的格式错误（如多余的逗号、未闭合的引号等）
            2. 保持章节内容的连贯性，确保与前面章节的情节自然衔接
            3. 符合小说风格和流派特点，增强可读性和吸引力
            4. 确保所有字段内容丰富、具体，而不是简单的占位符
            """

            # 生成内容
            result = self.generate_content(prompt, temperature=0.7, max_tokens=4000)
            if not result:
                return None

            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            cleaned_result = json_match.group(1) if json_match else result

            # 使用专门的清理函数处理JSON
            cleaned_result = self._clean_json_string(cleaned_result)

            # 尝试解析JSON确保格式正确
            try:
                json.loads(cleaned_result)
                return cleaned_result
            except json.JSONDecodeError as e:
                print(f"重新生成章节大纲JSON解析错误: {e}")
                # 应用更激进的清理
                cleaned_result = self._clean_json_string(cleaned_result, aggressive=True)
                try:
                    json.loads(cleaned_result)
                    return cleaned_result
                except json.JSONDecodeError:
                    # 如果仍然失败，尝试使用_fix_json方法
                    try:
                        fixed_data = self._fix_json(cleaned_result)
                        if fixed_data:
                            return json.dumps(fixed_data, ensure_ascii=False)
                    except Exception:
                        pass
                    
                    print("尝试所有JSON修复方法均失败，返回原始结果")
                    return result  # 返回原始结果作为最后的尝试

        except Exception as e:
            print(f"重新生成章节大纲时出错: {e}")
            import traceback
            traceback.print_exc()
            return None
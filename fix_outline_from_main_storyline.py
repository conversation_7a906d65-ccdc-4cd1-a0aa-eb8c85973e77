#!/usr/bin/env python3
"""
修复大纲文件中未成功生成的章节大纲

检测大纲文件中未成功生成的章节（如内容为{"story_title": "未命名故事", "outlines": []}的章节），
从故事主线中生成对应的大纲，并放到大纲json中正确的位置。
"""

import os
import sys
import argparse
from novel.generator import NovelGenerator

def main():
    """
    修复大纲的主入口函数
    """
    parser = argparse.ArgumentParser(
        description="修复大纲文件中未成功生成的章节大纲"
    )
    
    parser.add_argument('--force', '-f', action='store_true',
                       help='强制重新生成所有章节大纲')
    
    parser.add_argument('--chapter', '-c', type=int,
                       help='指定要修复的章节号，不指定则修复所有章节')
    
    args = parser.parse_args()
    
    # 创建生成器实例
    generator = NovelGenerator()
    
    # 加载项目
    success = generator.load_project()
    if not success:
        print("项目加载失败，请先初始化项目")
        sys.exit(1)
    
    print(f"项目 '{generator.title}' 加载成功")
    print(f"当前共 {generator.total_chapters} 章节")
    
    if args.force:
        # 如果使用--force，删除所有章节大纲并重新生成
        print("强制重新生成所有章节大纲...")
        for chapter_number in range(1, generator.total_chapters + 1):
            if args.chapter and args.chapter != chapter_number:
                continue
                
            print(f"正在重新生成第 {chapter_number} 章大纲...")
            success = generator.regenerate_chapter_outline(chapter_number)
            
            if success:
                print(f"第 {chapter_number} 章大纲重新生成成功")
            else:
                print(f"第 {chapter_number} 章大纲重新生成失败")
    else:
        # 使用fix_outline方法检测和修复大纲
        if args.chapter:
            # 如果指定了章节号，只修复该章节
            chapter = generator.outline.get_chapter(args.chapter)
            if not chapter:
                print(f"第 {args.chapter} 章大纲不存在，将创建新的大纲")
                success = generator.generate_chapter_outline(args.chapter)
                if success:
                    print(f"第 {args.chapter} 章大纲创建成功")
                else:
                    print(f"第 {args.chapter} 章大纲创建失败")
            else:
                print(f"修复第 {args.chapter} 章大纲...")
                success = generator.regenerate_chapter_outline(args.chapter)
                if success:
                    print(f"第 {args.chapter} 章大纲修复成功")
                else:
                    print(f"第 {args.chapter} 章大纲修复失败")
        else:
            # 修复所有有问题的章节
            print("检测和修复所有章节大纲...")
            generator.fix_outline()
    
    print("大纲修复完成")
    return 0

if __name__ == "__main__":
    sys.exit(main()) 
#!/usr/bin/env python3
"""
测试从当前章节继续生成小说功能
"""

import os
import sys
import json
from generate_from_outline import continue_novel_generation

def create_test_environment():
    """创建测试环境，包括大纲文件和第一章文件"""
    # 创建测试目录
    test_dir = "test_output"
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建测试大纲
    test_outline = {
        "title": "测试小说",
        "author": "AI测试",
        "genre": "测试流派",
        "outlines": [
            {
                "index": 1,
                "title": "第一章",
                "content": "这是第一章的大纲内容",
                "keywords": ["测试", "关键词"]
            },
            {
                "index": 2,
                "title": "第二章",
                "content": "这是第二章的大纲内容",
                "keywords": ["测试", "关键词"]
            },
            {
                "index": 3,
                "title": "第三章",
                "content": "这是第三章的大纲内容",
                "keywords": ["测试", "关键词"]
            }
        ]
    }
    
    # 保存测试大纲
    outline_path = os.path.join(test_dir, "outline.json")
    with open(outline_path, "w", encoding="utf-8") as f:
        json.dump(test_outline, f, ensure_ascii=False, indent=2)
        
    print(f"已创建测试大纲: {outline_path}")
    
    # 创建第一章文件（模拟已经生成的章节）
    chapter1_path = os.path.join(test_dir, "chapter_1.txt")
    with open(chapter1_path, "w", encoding="utf-8") as f:
        f.write("这是测试的第一章内容。\n\n这只是一个测试。")
        
    print(f"已创建测试章节: {chapter1_path}")
    
    # 创建风格文件
    style_path = os.path.join(test_dir, "style_guide.txt")
    with open(style_path, "w", encoding="utf-8") as f:
        f.write("这是测试风格指南。\n使用简洁的语言。\n保持逻辑清晰。")
        
    print(f"已创建测试风格指南: {style_path}")
    
    return outline_path, test_dir

def test_continue_generation():
    """测试继续生成功能"""
    # 创建测试环境
    outline_path, output_dir = create_test_environment()
    
    print("\n=== 开始测试继续生成功能 ===")
    
    # 模拟API以避免实际调用
    # 在实际环境中，请删除下面的代码段，直接调用continue_novel_generation函数
    from unittest.mock import patch
    
    def mock_api(*args, **kwargs):
        return "这是模拟生成的章节内容。\n\n通过模拟API生成。"
    
    # 修补DeepSeekAllAPI类的方法
    import llm.deepseek_all
    original_init = llm.deepseek_all.DeepSeekAllAPI.__init__
    original_generate_chapter_method = llm.deepseek_all.DeepSeekAllAPI.generate_chapter
    
    def mock_init(self):
        # 不需要实际初始化API
        pass
    
    # 暂时替换方法以避免实际API调用
    llm.deepseek_all.DeepSeekAllAPI.__init__ = mock_init
    llm.deepseek_all.DeepSeekAllAPI.generate_chapter = mock_api
    
    try:
        # 调用继续生成功能
        continue_novel_generation(outline_path, output_dir)
        
        # 验证结果
        chapter2_path = os.path.join(output_dir, "chapter_2.txt")
        chapter3_path = os.path.join(output_dir, "chapter_3.txt")
        
        success = True
        if not os.path.exists(chapter2_path):
            print(f"测试失败: 未找到生成的第二章文件 {chapter2_path}")
            success = False
        else:
            print(f"测试通过: 成功生成第二章文件 {chapter2_path}")
            
        if not os.path.exists(chapter3_path):
            print(f"测试失败: 未找到生成的第三章文件 {chapter3_path}")
            success = False
        else:
            print(f"测试通过: 成功生成第三章文件 {chapter3_path}")
            
        if success:
            print("\n测试成功: 继续生成功能正常工作！")
        else:
            print("\n测试失败: 继续生成功能有问题。")
            
    finally:
        # 恢复原始方法
        llm.deepseek_all.DeepSeekAllAPI.__init__ = original_init
        # 由于generate_chapter已经替换为original_generate_chapter_method，这里不需要重复设置
        llm.deepseek_all.DeepSeekAllAPI.generate_chapter = original_generate_chapter_method

if __name__ == "__main__":
    test_continue_generation() 
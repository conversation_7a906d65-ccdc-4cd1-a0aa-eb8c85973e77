"""
火山方舟API调用模块（原Gemini模块）
"""

import json
import requests
import re
from typing import List, Dict, Any, Optional

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import VOLCANIC_API_URL, VOLCANIC_API_KEY, VOLCANIC_MODEL_NAME

class GeminiAPI:
    """火山方舟API调用封装（原Gemini API）"""
    
    def __init__(self):
        self.api_url = VOLCANIC_API_URL
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {VOLCANIC_API_KEY}"
        }
    
    def generate_content(self, prompt: str, temperature: float = 0.7) -> Optional[str]:
        """
        调用火山方舟API生成内容
        
        Args:
            prompt: 提示词
            temperature: 温度参数，控制随机性
            
        Returns:
            生成的内容，如果出错则返回None
        """
        try:
            payload = {
                "model": VOLCANIC_MODEL_NAME,
                "messages": [
                    {"role": "system", "content": "你是一个专业的小说创作助手，擅长生成高质量的男频网络小说内容。"},
                    {"role": "user", "content": prompt}
                ],
                "temperature": temperature,
                "max_tokens": 8192
            }
            
            # 禁用代理设置
            proxies = {
                "http": None,
                "https": None
            }
            
            print(f"正在调用火山方舟API...")
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                proxies=proxies,  # 添加代理设置
                timeout=60  # 设置超时时间
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'choices' in data and len(data['choices']) > 0:
                    if 'message' in data['choices'][0] and 'content' in data['choices'][0]['message']:
                        return data['choices'][0]['message']['content']
            
            print(f"API调用失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
        except requests.exceptions.ProxyError as e:
            print(f"代理服务器连接错误: {str(e)}")
            print("尝试禁用代理后重新连接...")
            try:
                # 如果代理错误，尝试直接连接
                response = requests.post(
                    self.api_url,
                    headers=self.headers,
                    json=payload,
                    proxies={"http": None, "https": None},
                    timeout=60
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if 'choices' in data and len(data['choices']) > 0:
                        if 'message' in data['choices'][0] and 'content' in data['choices'][0]['message']:
                            return data['choices'][0]['message']['content']
                
                print(f"直接连接API调用失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
            except Exception as inner_e:
                print(f"禁用代理后仍然出错: {str(inner_e)}")
                return None
                
        except Exception as e:
            print(f"调用火山方舟API时出错: {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            return None
    
    def generate_novel_style(self, genre: str) -> Optional[str]:
        """
        生成小说风格提示词
        
        Args:
            genre: 小说流派
            
        Returns:
            风格提示词，如果出错则返回None
        """
        prompt = f"""
        请为一部男频网络小说的{genre}流派生成详细的风格提示词。
        
        提示词应包含以下内容：
        1. 该流派的文风特点
        2. 常用写作手法和套路
        3. 情节发展模式
        4. 角色塑造特点
        5. 世界观设定要点
        6. 读者期望
        
        请以结构化方式输出，便于后续创作使用。
        """
        
        return self.generate_content(prompt, temperature=0.7)
    
    def generate_background(self, genre: str, style_guide: str) -> Optional[str]:
        """
        生成小说背景设定
        
        Args:
            genre: 小说流派
            style_guide: 风格指南
            
        Returns:
            背景设定，如果出错则返回None
        """
        prompt = f"""
        根据以下男频网络小说的流派和风格指南，生成详细的背景设定：
        
        流派：{genre}
        风格指南：{style_guide}
        
        请为这部小说创建完整的背景设定，包括但不限于：
        1. 世界观（时代背景、地理环境、特殊规则等）
        2. 社会体系（阶级、组织、势力划分等）
        3. 特殊设定（如修炼体系、职业划分、特殊能力等）
        4. 历史事件（对当前世界有重大影响的历史事件）
        5. 文化特点（风俗习惯、信仰、禁忌等）
        
        请以结构化方式输出，便于后续创作使用。
        """
        
        return self.generate_content(prompt, temperature=0.7)
    
    def generate_outline(self, genre: str, style_guide: str, background: str, 
                          current_chapter: int, total_chapters: int,
                          previous_chapters_summary: Optional[str] = None,
                          characters_info: Optional[str] = None,
                          foreshadowing_info: Optional[str] = None) -> Optional[str]:
        """
        生成章节大纲
        
        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            current_chapter: 当前章节号
            total_chapters: 总章节数
            previous_chapters_summary: 前几章内容摘要
            characters_info: 人物信息
            foreshadowing_info: 伏笔信息
            
        Returns:
            章节大纲，如果出错则返回None
        """
        previous_summary = previous_chapters_summary if previous_chapters_summary else "这是第一章，无前序内容。"
        characters = characters_info if characters_info else "尚未创建具体人物。"
        foreshadowing = foreshadowing_info if foreshadowing_info else "尚无伏笔。"
        
        prompt = f"""
        根据以下男频网络小说信息，为第{current_chapter}章生成详细大纲：
        
        流派：{genre}
        风格指南：{style_guide}
        背景设定：{background}
        当前章节：{current_chapter}/{total_chapters}
        前序内容：{previous_summary}
        现有人物：{characters}
        现有伏笔：{foreshadowing}
        
        请提供以下内容：
        1. 章节标题
        2. 主要情节发展（开端、发展、高潮、结尾）
        3. 出场人物及其行动
        4. 新增设定或世界观信息（如有）
        5. 新埋下的伏笔（如有）
        6. 回收的前文伏笔（如有）
        7. 角色关系变化（如有）
        8. 情感基调和节奏
        
        如果是第一章，需要设计好主角登场、初始冲突和吸引读者的开场；如果是中间章节，需要推动剧情发展并保持读者兴趣；如果是结尾章节，需要合理安排高潮和结局。
        
        请以结构化方式输出，便于后续创作使用。
        """
        
        return self.generate_content(prompt, temperature=0.8)
    
    def generate_characters(self, genre: str, style_guide: str, background: str) -> Optional[str]:
        """
        生成人物卡片
        
        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            
        Returns:
            人物卡片，如果出错则返回None
        """
        prompt = f"""
        根据以下男频网络小说信息，创建详细的人物卡片：
        
        流派：{genre}
        风格指南：{style_guide}
        背景设定：{background}
        
        请为这部小说创建主要人物卡片（至少包括主角、重要配角、主要反派），每个人物卡片应包含：
        1. 基本信息（姓名、年龄、性别、身份/职业等）
        2. 外貌特征
        3. 性格特点
        4. 能力/技能
        5. 背景故事
        6. 动机与目标
        7. 与其他角色的关系
        8. 成长轨迹预期
        9. 当前状态
        
        请以结构化JSON格式输出，便于后续程序处理和更新。格式参考：
        ```json
        {{
            "characters": [
                {{
                    "name": "角色名",
                    "role": "主角/女主/主要配角/次要配角/主要反派/次要反派",
                    "basic_info": {{...}},
                    "appearance": "...",
                    "personality": "...",
                    "abilities": [...],
                    "background": "...",
                    "motivation": "...",
                    "relationships": {{...}},
                    "growth_path": "...",
                    "current_status": "..."
                }},
                // 其他角色...
            ]
        }}
        ```
        
        确保人物设定符合男频网络小说特点，特别是主角应具备吸引力和成长空间。
        """
        
        return self.generate_content(prompt, temperature=0.7)
    
    def generate_foreshadowing(self, genre: str, style_guide: str, background: str, 
                               characters: str, outline: str) -> Optional[str]:
        """
        生成伏笔管理
        
        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            characters: 人物卡片
            outline: 大纲
            
        Returns:
            伏笔管理，如果出错则返回None
        """
        prompt = f"""
        根据以下男频网络小说信息，创建详细的伏笔管理计划：
        
        流派：{genre}
        风格指南：{style_guide}
        背景设定：{background}
        人物卡片：{characters}
        大纲：{outline}
        
        请规划整部小说中的伏笔安排，包括：
        1. 主线伏笔（影响主要剧情走向的关键线索）
        2. 角色伏笔（与角色成长、身世相关的线索）
        3. 世界观伏笔（与世界设定相关的隐藏信息）
        4. 感情线伏笔（与感情发展相关的铺垫）
        
        对每个伏笔，请提供：
        - 伏笔内容
        - 埋下伏笔的预计章节
        - 回收伏笔的预计章节
        - 伏笔的重要性（高/中/低）
        - 伏笔埋下方式
        - 伏笔回收效果
        
        请以结构化JSON格式输出，便于后续程序处理和更新。格式参考：
        ```json
        {{
            "foreshadowings": [
                {{
                    "id": "f1",
                    "type": "主线/角色/世界观/感情线",
                    "content": "伏笔内容描述",
                    "plant_chapter": 预计埋下章节,
                    "reveal_chapter": 预计回收章节,
                    "importance": "高/中/低",
                    "plant_method": "埋下方式描述",
                    "reveal_effect": "回收效果描述",
                    "status": "未埋下/已埋下未回收/已回收",
                    "related_characters": ["角色名1", "角色名2"]
                }},
                // 其他伏笔...
            ]
        }}
        ```
        
        确保伏笔安排合理，能够增强故事的连贯性和吸引力。
        """
        
        return self.generate_content(prompt, temperature=0.7)
    
    def check_chapter(self, chapter_content: str, chapter_outline: str, 
                      characters: str, background: str, 
                      foreshadowing: str) -> Optional[Dict[str, Any]]:
        """
        检查章节内容
        
        Args:
            chapter_content: 章节内容
            chapter_outline: 章节大纲
            characters: 人物卡片
            background: 背景设定
            foreshadowing: 伏笔管理
            
        Returns:
            检查结果字典，包含是否通过、问题列表和修改建议，如果出错则返回None
        """
        prompt = f"""
        请对以下小说章节内容进行全面审核，检查是否符合要求：
        
        【章节大纲】
        {chapter_outline}
        
        【人物卡片】
        {characters}
        
        【背景设定】
        {background}
        
        【伏笔管理】
        {foreshadowing}
        
        【章节内容】
        {chapter_content}
        
        请严格检查以下几点：
        1. 内容是否严格按照大纲生成
        2. 是否存在未经设定的内容或设定冲突
        3. 是否存在前后逻辑矛盾
        4. 人物言行是否符合人物设定
        5. 伏笔埋下/回收是否按计划执行
        6. 章节长度是否合适（约1万汉字）
        7. 是否存在其他问题
        
        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "issues": [
                {{
                    "type": "问题类型（大纲偏离/设定冲突/逻辑矛盾/人物不符/伏笔问题/其他）",
                    "description": "问题详细描述",
                    "location": "问题在文中的大致位置",
                    "suggestion": "修改建议"
                }},
                // 其他问题...
            ],
            "character_updates": [
                {{
                    "name": "角色名",
                    "updates": {{
                        "current_status": "更新后的状态描述",
                        // 其他需要更新的字段...
                    }}
                }},
                // 其他角色更新...
            ],
            "foreshadowing_updates": [
                {{
                    "id": "伏笔ID",
                    "status": "更新后的状态",
                    // 其他需要更新的字段...
                }},
                // 其他伏笔更新...
            ]
        }}
        ```
        
        如果没有发现任何问题，请返回passed为true且issues为空数组。否则，请详细列出所有问题并给出具体修改建议。
        """
        
        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return None
            
        try:
            # 可能返回的结果前后有额外文本，尝试提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)
            
            # 尝试多种方式解析JSON
            try:
                return json.loads(result)
            except:
                # 去除可能存在的注释
                result = re.sub(r'//.*?\n', '\n', result)
                return json.loads(result)
                
        except Exception as e:
            print(f"解析检查结果JSON时出错: {str(e)}")
            print(f"原始结果: {result}")
            # 返回一个基本结构，表示解析错误
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "description": f"无法解析API返回的JSON: {str(e)}",
                    "location": "整篇",
                    "suggestion": "请重新运行检查或手动检查内容"
                }],
                "character_updates": [],
                "foreshadowing_updates": []
            }
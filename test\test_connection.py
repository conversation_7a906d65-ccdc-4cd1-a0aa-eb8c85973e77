"""
测试网络连接
"""

import requests
import sys
from config import DEEPSEEK_API_URL, DEEPSEEK_API_KEY

def test_connection():
    """测试到DeepSeek API的连接"""
    print(f"正在测试连接到 {DEEPSEEK_API_URL}...")
    
    try:
        # 尝试不使用代理
        response = requests.get(
            "https://llm.chutes.ai/health",
            headers={"Authorization": f"Bearer {DEEPSEEK_API_KEY}"},
            timeout=10,
            proxies={"http": None, "https": None}
        )
        print(f"连接状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        return True
    except Exception as e:
        print(f"连接失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_connection()
    if success:
        print("连接测试通过！")
        sys.exit(0)
    else:
        print("连接测试失败！")
        sys.exit(1) 
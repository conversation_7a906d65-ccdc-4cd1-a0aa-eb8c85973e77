"""
测试主线故事JSON解析和保存功能
"""

import json
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.file_manager import save_main_storyline, load_main_storyline
from config import OUTPUT_DIR

def test_save_and_load_json():
    """测试保存和加载JSON格式的主线故事"""
    
    print(f"当前工作目录: {os.getcwd()}")
    print(f"输出目录: {OUTPUT_DIR}")
    output_file = os.path.join(OUTPUT_DIR, "main_storyline.json")
    print(f"输出文件路径: {output_file}")
    
    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 测试情况1：正常JSON格式
    test_json = {
        "story_title": "测试故事",
        "chapter_outlines": [
            {
                "chapter_number": 1,
                "title": "开始",
                "content": "这是第一章的内容"
            },
            {
                "chapter_number": 2,
                "title": "发展",
                "content": "这是第二章的内容"
            }
        ]
    }
    
    print("\n测试1：标准JSON对象")
    json_str = json.dumps(test_json, ensure_ascii=False)
    save_result = save_main_storyline(json_str)
    print(f"保存结果: {save_result}")
    
    # 检查文件是否存在
    if os.path.exists(output_file):
        print(f"文件已创建: {output_file}")
        print(f"文件大小: {os.path.getsize(output_file)} 字节")
        
        # 读取文件内容
        with open(output_file, 'r', encoding='utf-8') as f:
            file_content = f.read()
        print(f"文件内容前100个字符: {file_content[:100]}...")
    else:
        print(f"文件未创建: {output_file}")
    
    loaded_content = load_main_storyline()
    print(f"加载结果类型: {type(loaded_content)}")
    if loaded_content:
        print(f"加载结果前100个字符: {loaded_content[:100]}...")
    else:
        print("加载失败，返回None")
    
    # 测试情况2：代码块中的JSON
    test_code_block = """```json
{
  "story_title": "代码块中的故事",
  "chapter_outlines": [
    {
      "chapter_number": 1,
      "title": "开始",
      "content": "这是代码块中第一章的内容"
    }
  ]
}
```"""
    
    print("\n测试2：代码块中的JSON")
    save_result = save_main_storyline(test_code_block)
    print(f"保存结果: {save_result}")
    
    # 检查文件是否存在
    if os.path.exists(output_file):
        print(f"文件已创建: {output_file}")
        print(f"文件大小: {os.path.getsize(output_file)} 字节")
        
        # 读取文件内容
        with open(output_file, 'r', encoding='utf-8') as f:
            file_content = f.read()
        print(f"文件内容前100个字符: {file_content[:100]}...")
    else:
        print(f"文件未创建: {output_file}")
    
    loaded_content = load_main_storyline()
    print(f"加载结果类型: {type(loaded_content)}")
    if loaded_content:
        print(f"加载结果前100个字符: {loaded_content[:100]}...")
    else:
        print("加载失败，返回None")
    
    # 测试情况3：格式不正确的JSON
    test_bad_json = """
{
  story_title: "格式不正确的故事",
  "chapter_outlines": [
    {
      chapter_number: 1,
      'title': '开始',
      "content": "缺少引号的内容示例"
    }
  ]
}"""
    
    print("\n测试3：格式不正确的JSON")
    save_result = save_main_storyline(test_bad_json)
    print(f"保存结果: {save_result}")
    
    # 检查文件是否存在
    if os.path.exists(output_file):
        print(f"文件已创建: {output_file}")
        print(f"文件大小: {os.path.getsize(output_file)} 字节")
        
        # 读取文件内容
        with open(output_file, 'r', encoding='utf-8') as f:
            file_content = f.read()
        print(f"文件内容前100个字符: {file_content[:100]}...")
    else:
        print(f"文件未创建: {output_file}")
    
    loaded_content = load_main_storyline()
    print(f"加载结果类型: {type(loaded_content)}")
    if loaded_content:
        print(f"加载结果前100个字符: {loaded_content[:100]}...")
    else:
        print("加载失败，返回None")
    
    # 测试情况4：非JSON内容
    test_text = "这不是JSON格式，而是普通文本"
    
    print("\n测试4：非JSON内容")
    save_result = save_main_storyline(test_text)
    print(f"保存结果: {save_result}")
    
    # 检查文件是否存在
    if os.path.exists(output_file):
        print(f"文件已创建: {output_file}")
        print(f"文件大小: {os.path.getsize(output_file)} 字节")
        
        # 读取文件内容
        with open(output_file, 'r', encoding='utf-8') as f:
            file_content = f.read()
        print(f"文件内容前100个字符: {file_content[:100]}...")
    else:
        print(f"文件未创建: {output_file}")
    
    loaded_content = load_main_storyline()
    print(f"加载结果类型: {type(loaded_content)}")
    if loaded_content:
        print(f"加载结果前100个字符: {loaded_content[:100]}...")
    else:
        print("加载失败，返回None")

if __name__ == "__main__":
    test_save_and_load_json() 
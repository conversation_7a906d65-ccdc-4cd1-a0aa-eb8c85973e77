"""
测试修复后的 gemini.py 模块
"""

from llm.gemini import GeminiAPI

def main():
    print("开始测试 GeminiAPI...")
    try:
        # 尝试创建 GeminiAPI 实例
        api = GeminiAPI()
        print("GeminiAPI 实例创建成功，模块导入正常")
        
        # 模拟调用一个方法（不会实际调用 API，因为 API 密钥无效）
        prompt = "测试 f-string"
        print(f"测试生成模板字符串: {api.generate_novel_style.__name__}({prompt})")
        
        return 0
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return 1

if __name__ == "__main__":
    main() 
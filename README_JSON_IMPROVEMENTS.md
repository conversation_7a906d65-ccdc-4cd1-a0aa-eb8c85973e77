# JSON解析系统改进

## 改进摘要

本次重构优化了小说生成系统中的JSON解析功能，特别是对main_storyline.json文件的处理。主要改进包括：

1. **删除特殊处理代码**：移除了对特定章节（如第18章）和特定文本（如"天道枷锁"）的特殊处理代码
2. **通用解析方法**：使用更健壮的通用JSON解析方法，能够处理各种格式问题
3. **变量名缩短**：缩短变量名减少token消耗，如将chapter_outlines→outlines，chapter_number→index
4. **章节数验证**：添加验证功能确保生成的章节数与预期总章节数匹配
5. **错误处理**：当JSON解析失败或章节数不匹配时，不保存文件并返回错误状态，触发重新生成

## 改进详情

### 1. utils/fix_storyline.py

- 移除了针对"天道枷锁"和第18章的特殊处理代码
- 添加了expected_chapters参数，用于验证章节数量
- 确保同时支持outlines和chapter_outlines字段
- 确保每个章节同时具有index和chapter_number字段

### 2. utils/json_helper.py

- 重写了fix_json函数中处理未转义引号的逻辑，采用更通用的方法
- 移除了特定文本的特殊处理代码
- 改进了错误消息，显示更清晰的解析失败信息

### 3. utils/file_manager.py

- 更新了save_main_storyline函数，添加了expected_chapters参数
- 改进了错误处理，当JSON解析失败或章节数不匹配时返回False
- 更新了load_main_storyline函数，当章节数不匹配时删除文件并返回None

### 4. models/outline.py（已有改进）

- ChapterOutline类中已添加index字段，与chapter_number同值
- to_dict和from_dict方法已支持两种字段名

## 测试验证

创建了三个测试脚本验证改进效果：

1. **test_json_parsing.py**：测试通用JSON解析功能，包括处理常见格式问题
2. **test_chapter_validation.py**：测试章节数量验证功能
3. **test_integrated.py**：测试整个JSON解析流程，包括保存、加载和访问章节

测试结果表明，改进后的代码能够成功解析各种格式的JSON数据，包括处理引号未转义、属性名未加引号等常见问题，并能正确验证章节数量。

## 使用方法

### 解析JSON字符串

```python
from utils.json_helper import parse_json_safely

# 解析JSON字符串
data = parse_json_safely(json_str, "数据类型描述")
```

### 加载故事主线（验证章节数）

```python
from utils.fix_storyline import load_storyline

# 加载并验证故事主线
data = load_storyline(expected_chapters=30)  # 期望30章
```

### 获取特定章节

```python
from utils.fix_storyline import get_chapter_from_storyline

# 获取第10章
chapter = get_chapter_from_storyline(storyline_data, 10)
```

### 保存故事主线（验证章节数）

```python
from utils.file_manager import save_main_storyline

# 保存故事主线并验证章节数
success = save_main_storyline(content, expected_chapters=30)
``` 
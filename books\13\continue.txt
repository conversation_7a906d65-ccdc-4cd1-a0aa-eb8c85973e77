请根据以下男频网络小说信息，生成一个详细的结构化故事大纲：
【背景设定】

【输出要求】
总章节数：500
你是一位番茄小说平台的人气写手，有丰富的网文创作经验，单月获得稿费100W+。请严格遵守风格指南，提供一个完整的故事大纲规划，以JSON格式输出，必须包含以下内容：

    必须严格遵循以下JSON结构：
    ```json
    {{
      "story_title": "小说标题",
      "outlines": [
        {{
          "index": 1,
          "title": "第一章的建议标题",
          "chapter_summary": {
        "opening": "章节开始的场景和初始情况描述",
        "development": "情节如何推进，主要角色互动，关键场景描述",
        "climax": "本章的高潮部分，主要冲突或重要发现描述",
        "ending": "章节如何结束，为下一章做铺垫的描述，必须设置钩子"
      },
      "characters": [
        {
          "name": "人物名称",
          "actions": "此人物在本章的关键行动和变化",
          "emotions": "角色在本章的情感变化",
          "motivation": "角色在本章行动的动机"，
          "firstAppearance":"是否初次登场",
          "background":"背景介绍（仅在firstAppearance为true时有）"，
          "appearance": "外貌描述（仅在firstAppearance为true时有）",
          "personality": "性格特点（仅在firstAppearance为true时有）",
        }
        // 其他出场人物...
      ],
       "scenes": [
        {
          "location": "场景1的地点",
          "participants": ["人物名称1", "人物名称2"],
          "content": "场景1的具体内容描述"
        },
        {
          "location": "场景2的地点",
          "participants": ["人物名称1"],
          "content": "场景2的具体内容描述"
        },
        {
          "location": "场景3的地点",
          "participants": ["人物名称2", "人物名称3"],
          "content": "场景3的具体内容描述"
        }
        // 可以有更多场景...
      ],
      "key_points": [
        "关键情节点1",
        "关键情节点2",
        "关键情节点3"
        // 可以有更多关键点...
      ],
      "foreshadowings": {
        "planted": [
          {
            "id": "伏笔ID（如f1）",
            "content": "伏笔内容",
            "method": "埋下方式的描述"
          }.
        ],
        "revealed": [
          {
            "id": "伏笔ID（如f2）",
            "content": "伏笔内容",
            "effect": "回收效果的描述"
          }
        ]
      }
        }},
        // 第2章到第499章的内容（不要使用省略号，必须完整列出所有章节）
        {{
          "index": 500,
          "title": "最终章的建议标题",
          "chapter_summary": {
        "opening": "章节开始的场景和初始情况描述",
        "development": "情节如何推进，主要角色互动，关键场景描述",
        "climax": "本章的高潮部分，主要冲突或重要发现描述",
        "ending": "章节如何结束，为下一章做铺垫的描述"
      },
      "characters": [
        {
          "name": "人物名称",
          "actions": "此人物在本章的关键行动和变化",
          "emotions": "角色在本章的情感变化",
          "motivation": "角色在本章行动的动机"，
          "firstAppearance":"是否初次登场",
          "background":"背景介绍（仅在firstAppearance为true时有）"，
          "appearance": "外貌描述（仅在firstAppearance为true时有）",
          "personality": "性格特点（仅在firstAppearance为true时有）",
        }
        // 其他出场人物...
      ],
      "scenes": [
        {
          "location": "最终章场景1的地点",
          "participants": ["人物名称A", "人物名称B"],
          "content": "最终章场景1的具体内容描述"
        },
        {
          "location": "最终章场景2的地点",
          "participants": ["人物名称A", "人物名称C"],
          "content": "最终章场景2的具体内容描述"
        }
        // 可以有更多场景...
      ],,
      "key_points": [
        "关键情节点1",
        "关键情节点2",
        "关键情节点3"
        // 可以有更多关键点...
      ],
      "foreshadowings": {
        "planted": [
          {
            "id": "伏笔ID（如f1）",
            "content": "伏笔内容",
            "method": "埋下方式的描述"
          }.
        ],
        "revealed": [
          {
            "id": "伏笔ID（如f2）",
            "content": "伏笔内容",
            "effect": "回收效果的描述"
          }
        ]
      }
        }}
      ]
    }}
    ```

    【重要格式说明】：
    1. index必须是整数类型，不能是字符串，例如应该是"index": 1而不是"index": "1"
    2. outlines数组必须包含从第1章到第500章的所有章节，不能遗漏任何章节
    3. 每一章节内容在5000至7000字之间，需要十分合理的安排大纲的信息量，支撑5000至7000字的文章正文
    4. 生成大纲时严格遵守风格指南
    5. 不要有模凌两可的描述、假设和要作者自己定夺的地方，比如：如果......或是......可能......假设......不要让作者在写作时判断，而是做确切的描述，以便指导文章的生成
    6. 保证读完率，读完率是最重要的指标，每一章ending部分必须设置钩子并标记出来，格式为：“钩子：......”
    7. 爆点、爽点必须非常密集，尽量每章都安排爆点，爽点，以带动读者情绪
    8. 注意节奏控制，不要太快推进进度，严格遵守风格指南中的规划
    9. 剧情需要为情绪服务，要不断起伏，给读者期待感
    10.关键剧情需要详细描写，可安排多章，可以适当插入支线剧情，时刻记住所有剧情、描写都是为了带动情绪服务
    11.出场人物，即使是只出场一次的配角也需要给名字，不要用一位XX、某个XX等代替
    12.少用逗号、省略号,一个完整的意群或句子成分结束时才能使用标点。坚决禁止出现类似“他，看着那，远处的敌人，举起了，手中的刀”这种用逗号强行分割主谓宾、制造虚假停顿的句式。
    13.主角在比赛中拿到的个人数据，将给观众带来很大的爽感，即使大纲里没有，正文也要给出合理的主角个人数据
    【避免问题】
    1.你有剧情进展过快的问题，安排大纲内容时需要反思，将目前安排的单章内容拆分成多章描写，或者补充适当的支线情节
    2.你曾经多次犯错，让不该出现的角色出场。编写每一章大纲前，每次都要确认角色的状态，不该出现的角色（如已经阵亡，已离队，地理位置不符等）不能出现在剧情中
    3.你曾多次使用已经消耗掉的，或是不存在的物品。编写每一章大纲前，每次都要确认可用物品的状态，不能出现未知来源，或是已经消耗掉的物品
    4.大纲chapter_summary编写时禁止使用括号，当你想要使用括号时，你想要写的内容绝对会有逻辑错误，应当重新规划成不需要括号也能正确表达的内容

    【特定指出】
    1.不要写任何球场外的内容
    2.一场比赛要写4~8章
    3.获取小说对应时间点的准确的球员、球队信息
    4.剧情应该包含对所有的球队的比赛，无论强队还是弱队
    5.每场比赛后一定要有主角数据
    6.主角拳头很硬，对犯规要想办法报复回去，但是不算脏，不会主动废人
    7.主角身体对抗、运动能力很一般，以灵活性、投射、组织见长
    8.纽约主要阵容：尤因 查尔斯-史密斯 约翰-斯塔克斯 德里克-哈珀 小安东尼-梅森 道格-里弗斯 格雷格-安东尼 安东尼-邦纳
    9.不要写太多关于恶意犯规的桥段，可以有身体对抗，但还是把关注点聚焦在篮球上
    【接下来的剧情】
     1.主角凭借意志力硬撑，在体力几乎耗尽的情况下连中三分，夺得冠军
     3.fmvp没有让的说法，是评委评出来的！！！滑翔机整个系列赛数据更好，给了滑翔机！！！
     4.滑翔机跟腱断裂，要修养一年多，而且也不会有球队会想要一个伤员。下赛季注定无法夺冠
     5.因为滑翔机重伤，管理层想摆烂一个赛季，主角希望争冠。休赛期主角和多位巨星联系，一是和他们学习，而是考虑合作的机会。最后和管理层交流后，主角被交易到纽约，换成查尔斯-奥克利+休伯特-戴维斯和若干选秀权
     6.尤因和主角组成双塔，主角主动减重，改打大前
   

    重新给我265章到300章的json，紧接第264章大纲!!!不要给我完整的json，因为你会遇到网络错误而无法提供

264大纲为： {
      "index": 264,
      "title": "冠军巡游！风雨中的波特兰",
      "chapter_summary": {
        "opening": "开拓者队带着奥布莱恩杯，回到了波特兰。整个城市，都为这座20多年来的第一座总冠军奖杯而陷入了疯狂。盛大的冠军巡游，在城市的主干道上举行。",
        "development": "数十万球迷涌上街头，高呼着每一个球员的名字，尤其是杨涵森和德雷克斯勒。然而，巡游的花车上，德雷克斯勒却只能坐着轮椅，他的右脚打着厚厚的石膏。他的脸上，带着微笑，但眼神中，却难掩失落。",
        "climax": "在市政广场的庆祝仪式上，杨涵森作为球队代表发言。他高举着冠军奖杯，对着所有球迷大声承诺：“这只是一个开始！只要我还在这里，这座城市，就会有更多的冠军奖杯！我们，会为了克莱德，赢下下一个，下下个冠军！”",
        "ending": "杨涵森的宣言，将庆祝的气氛，推向了顶点。球迷们高呼着“MVP”，整个波特兰，都沉浸在这悲喜交加的狂欢之中。但杨涵森知道，卫冕之路，从德雷克斯勒倒下的那一刻起，就已经变得无比艰难。钩子：辉煌的顶点，亦是危机的开始。休赛期，开拓者队的管理层，会如何应对德雷克斯勒的重伤？他们是会选择保留阵容，还是会为了卫冕，而做出一些冷血的决定？"
      },
      "characters": [
        {
          "name": "杨涵森",
          "actions": "在冠军巡游的庆祝仪式上，向全城球迷，发表了卫冕的宣言。",
          "emotions": "兴奋、但保持着冷静和决心",
          "motivation": "他要建立一个属于自己的王朝。",
          "firstAppearance": false
        },
        {
          "name": "克莱德·德雷克斯勒",
          "actions": "坐着轮椅，参加了冠军巡游。",
          "emotions": "高兴、但内心充满失落和对未来的担忧",
          "motivation": "N/A",
          "firstAppearance": false
        },
        {
          "name": "开拓者队",
          "actions": "享受着夺冠后的荣誉和球迷的爱戴。",
          "emotions": "狂喜",
          "motivation": "N/A",
          "firstAppearance": false
        }
      ],
      "scenes": [
        {
          "location": "波特兰市中心",
          "participants": [
            "开拓者队全体",
            "波特兰球迷"
          ],
          "content": "开拓者队举行了盛大的冠军巡游，杨涵森在仪式上发表了卫冕宣言。"
        }
      ],
      "key_points": [
        "展现了夺冠后的辉煌，让主角和球队，享受应得的荣誉。",
        "通过德雷克斯勒的落寞，与狂欢的气氛，形成对比，增加了剧情的张力。",
        "主角的卫冕宣言，为下一季的剧情，设定了明确的目标。",
        "悬念钩子：将矛盾，引向了管理层的抉择，预示着休赛期将不会平静。"
      ],
      "foreshadowings": {
        "planted": [],
        "revealed": []
      }
    }
    please fix all the expected expression issues, starting from line 571 to 1006

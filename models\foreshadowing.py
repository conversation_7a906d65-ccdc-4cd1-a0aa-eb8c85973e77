"""
伏笔管理模型
"""

import json
import os
import re
from typing import Dict, Any, List, Optional

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import OUTPUT_DIR
from utils.file_manager import (
    FORESHADOWINGS_DIR, ensure_directory, save_foreshadowing_group,
    load_foreshadowing_group, migrate_foreshadowings_to_groups,
    save_all_foreshadowings, load_all_foreshadowings
)
from utils.json_helper import parse_json_safely

class Foreshadowing:
    """伏笔模型"""

    def __init__(self, id: str, type: str, content: str, plant_chapter: int,
                 reveal_chapter: int, importance: str, plant_method: str,
                 reveal_effect: str, status: str = "未埋下", related_characters: List[str] = None):
        """
        初始化伏笔模型

        Args:
            id: 伏笔ID
            type: 伏笔类型（主线/角色/世界观/感情线）
            content: 伏笔内容
            plant_chapter: 埋下伏笔的章节
            reveal_chapter: 回收伏笔的章节
            importance: 伏笔重要性（高/中/低）
            plant_method: 埋下方式
            reveal_effect: 回收效果
            status: 伏笔状态（未埋下/已埋下未回收/已回收）
            related_characters: 相关角色
        """
        self.id = id
        self.type = type
        self.content = content
        self.plant_chapter = plant_chapter
        self.reveal_chapter = reveal_chapter
        self.importance = importance
        self.plant_method = plant_method
        self.reveal_effect = reveal_effect
        self.status = status
        self.related_characters = related_characters or []

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            字典格式的伏笔
        """
        return {
            "id": self.id,
            "type": self.type,
            "content": self.content,
            "plant_chapter": self.plant_chapter,
            "reveal_chapter": self.reveal_chapter,
            "importance": self.importance,
            "plant_method": self.plant_method,
            "reveal_effect": self.reveal_effect,
            "status": self.status,
            "related_characters": self.related_characters
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Foreshadowing':
        """
        从字典创建伏笔模型

        Args:
            data: 字典格式的伏笔

        Returns:
            伏笔模型实例
        """
        # 获取内容字段
        content = data.get("content", "")

        # 检查内容字段是否包含嵌套的JSON结构
        if isinstance(content, str) and content.strip().startswith("{") and content.strip().endswith("}"):
            try:
                # 尝试解析JSON
                content_data = json.loads(content)

                # 如果解析成功且包含foreshadowings字段，说明这是一个嵌套的伏笔管理JSON
                if isinstance(content_data, dict) and "foreshadowings" in content_data and isinstance(content_data["foreshadowings"], list):
                    # 提取第一个伏笔的信息（如果有）
                    if content_data["foreshadowings"]:
                        # 使用第一个伏笔的内容作为当前伏笔的内容
                        fs_data = content_data["foreshadowings"][0]
                        content = fs_data.get("content", content)
            except Exception as e:
                # 如果解析失败，保留原始内容字段
                print(f"解析嵌套的伏笔管理JSON时出错: {str(e)}")

        # 确保ID字段有值
        id = data.get("id", "")
        if not id:
            id = f"f{hash(content) % 10000}"  # 使用内容的哈希值生成ID
            print(f"伏笔缺少ID，已自动生成: {id}")

        # 确保章节号为整数
        try:
            plant_chapter = int(data.get("plant_chapter", 0))
        except (ValueError, TypeError):
            plant_chapter = 0
            print(f"伏笔 {id} 的埋下章节号无效，已设为0")

        try:
            reveal_chapter = int(data.get("reveal_chapter", 0))
        except (ValueError, TypeError):
            reveal_chapter = 0
            print(f"伏笔 {id} 的回收章节号无效，已设为0")

        return cls(
            id=id,
            type=data.get("type", ""),
            content=content,
            plant_chapter=plant_chapter,
            reveal_chapter=reveal_chapter,
            importance=data.get("importance", ""),
            plant_method=data.get("plant_method", ""),
            reveal_effect=data.get("reveal_effect", ""),
            status=data.get("status", "未埋下"),
            related_characters=data.get("related_characters", [])
        )

    def update_status(self, new_status: str) -> None:
        """
        更新伏笔状态

        Args:
            new_status: 新状态
        """
        self.status = new_status


class ForeshadowingManager:
    """伏笔管理器"""

    def __init__(self, foreshadowings: List[Foreshadowing] = None):
        """
        初始化伏笔管理器

        Args:
            foreshadowings: 伏笔列表
        """
        self.foreshadowings = foreshadowings or []
        self.file_path = os.path.join(OUTPUT_DIR, "foreshadowings.json")

    def add_foreshadowing(self, foreshadowing: Foreshadowing, total_chapters: int = None) -> None:
        """
        添加伏笔

        Args:
            foreshadowing: 伏笔
            total_chapters: 总章节数，如果为None则尝试从NovelGenerator获取
        """
        # 验证章节号是否在有效范围内
        try:
            # 如果没有提供总章节数，尝试从NovelGenerator获取
            if total_chapters is None:
                try:
                    from novel.generator import NovelGenerator
                    generator = NovelGenerator()
                    generator.load_project()
                    total_chapters = generator.total_chapters
                except Exception as e:
                    print(f"获取总章节数失败，无法验证章节号: {str(e)}")
                    total_chapters = None

            # 如果有有效的总章节数，进行验证和修正
            if total_chapters is not None and total_chapters > 0:
                # 修正埋下章节号
                if foreshadowing.plant_chapter > total_chapters:
                    print(f"警告：伏笔 {foreshadowing.id} 的埋下章节 {foreshadowing.plant_chapter} 超出总章节数 {total_chapters}，已自动修正")
                    foreshadowing.plant_chapter = total_chapters

                # 修正回收章节号
                if foreshadowing.reveal_chapter > total_chapters:
                    print(f"警告：伏笔 {foreshadowing.id} 的回收章节 {foreshadowing.reveal_chapter} 超出总章节数 {total_chapters}，已自动修正")
                    foreshadowing.reveal_chapter = total_chapters

                # 确保回收章节不早于埋下章节
                if foreshadowing.reveal_chapter < foreshadowing.plant_chapter:
                    print(f"警告：伏笔 {foreshadowing.id} 的回收章节 {foreshadowing.reveal_chapter} 早于埋下章节 {foreshadowing.plant_chapter}，已自动修正")
                    foreshadowing.reveal_chapter = foreshadowing.plant_chapter
        except Exception as e:
            # 如果验证过程中出错，不进行修正
            print(f"验证章节号时出错: {str(e)}")

        # 检查伏笔ID是否已存在，如果存在则更新
        for i, fs in enumerate(self.foreshadowings):
            if fs.id == foreshadowing.id:
                self.foreshadowings[i] = foreshadowing
                return

        # 如果不存在则添加
        self.foreshadowings.append(foreshadowing)

    def get_foreshadowing(self, id: str) -> Optional[Foreshadowing]:
        """
        获取伏笔

        Args:
            id: 伏笔ID

        Returns:
            伏笔，如果不存在则返回None
        """
        for foreshadowing in self.foreshadowings:
            if foreshadowing.id == id:
                return foreshadowing
        return None

    def get_foreshadowings_for_chapter(self, chapter_number: int,
                                        status: str = None) -> List[Foreshadowing]:
        """
        获取指定章节的伏笔

        Args:
            chapter_number: 章节号
            status: 伏笔状态过滤

        Returns:
            伏笔列表
        """
        result = []

        # 找出在该章节埋下或回收的伏笔
        for foreshadowing in self.foreshadowings:
            if foreshadowing.plant_chapter == chapter_number:
                if status is None or foreshadowing.status == status:
                    result.append(foreshadowing)
            elif foreshadowing.reveal_chapter == chapter_number:
                if status is None or foreshadowing.status == status:
                    result.append(foreshadowing)

        return result

    def get_foreshadowings_to_plant(self, chapter_number: int) -> List[Foreshadowing]:
        """
        获取需要在指定章节埋下的伏笔

        Args:
            chapter_number: 章节号

        Returns:
            伏笔列表
        """
        return [f for f in self.foreshadowings
                if f.plant_chapter == chapter_number and f.status == "未埋下"]

    def get_foreshadowings_to_reveal(self, chapter_number: int) -> List[Foreshadowing]:
        """
        获取需要在指定章节回收的伏笔

        Args:
            chapter_number: 章节号

        Returns:
            伏笔列表
        """
        return [f for f in self.foreshadowings
                if f.reveal_chapter == chapter_number and f.status == "已埋下未回收"]

    def update_foreshadowing_status(self, id: str, status: str) -> bool:
        """
        更新伏笔状态

        Args:
            id: 伏笔ID
            status: 新状态

        Returns:
            更新是否成功
        """
        foreshadowing = self.get_foreshadowing(id)
        if foreshadowing:
            foreshadowing.update_status(status)
            return True
        return False

    def batch_update_foreshadowing_status(self, updates: List[Dict[str, str]]) -> int:
        """
        批量更新伏笔状态

        Args:
            updates: 更新列表，每项包含id和status

        Returns:
            成功更新的伏笔数量
        """
        success_count = 0
        for update in updates:
            id = update.get("id", "")
            status = update.get("status", "")
            if id and status:
                if self.update_foreshadowing_status(id, status):
                    success_count += 1

        # 如果有更新，保存到文件
        if success_count > 0:
            self.save()

        return success_count

    def add_foreshadowing_from_dict(self, fs_data: Dict[str, Any], total_chapters: int = None) -> None:
        """
        从字典创建并添加伏笔

        Args:
            fs_data: 字典格式的伏笔数据
            total_chapters: 总章节数，如果为None则尝试从NovelGenerator获取
        """
        # 使用传入的ID或生成新的伏笔ID
        if "id" in fs_data and fs_data["id"]:
            fs_id = fs_data["id"]
        else:
            existing_ids = [fs.id for fs in self.foreshadowings]
            next_id = f"f{len(existing_ids)}"
            fs_id = next_id

        # 创建新伏笔
        foreshadowing = Foreshadowing(
            id=fs_id,
            type=fs_data.get("type", "其他"),
            content=fs_data.get("content", ""),
            plant_chapter=fs_data.get("plant_chapter", 0),
            reveal_chapter=fs_data.get("reveal_chapter", 0),
            importance=fs_data.get("importance", "中"),
            plant_method=fs_data.get("plant_method", ""),
            reveal_effect=fs_data.get("reveal_effect", ""),
            status=fs_data.get("status", "未埋下"),
            related_characters=fs_data.get("related_characters", [])
        )

        # 添加到伏笔列表，传递总章节数参数
        self.add_foreshadowing(foreshadowing, total_chapters)

    def create_from_json(self, json_str: str, total_chapters: int = None) -> bool:
        """
        从JSON字符串创建伏笔管理器

        Args:
            json_str: JSON字符串
            total_chapters: 总章节数

        Returns:
            创建是否成功
        """
        try:
            # 使用安全的JSON解析函数
            foreshadowing_data = parse_json_safely(json_str, "foreshadowing")
            
            if not foreshadowing_data or "foreshadowings" not in foreshadowing_data:
                print("无法从JSON中解析伏笔数据")
                return False
            
            # 清空现有伏笔
            self.foreshadowings = []
            
            # 添加新伏笔
            for fs_data in foreshadowing_data.get("foreshadowings", []):
                self.add_foreshadowing_from_dict(fs_data, total_chapters)
            
            print(f"成功从JSON创建伏笔管理器，共{len(self.foreshadowings)}个伏笔")
            return True
        except Exception as e:
            print(f"从JSON创建伏笔管理器时出错: {str(e)}")
            return False

    def update_chapter_foreshadowings(self, chapter_number: int) -> None:
        """
        更新指定章节的伏笔状态
        
        Args:
            chapter_number: 章节号
        """
        # 更新需要在本章埋下的伏笔状态
        plant_foreshadowings = self.get_foreshadowings_to_plant(chapter_number)
        for fs in plant_foreshadowings:
            fs.status = "已埋下未回收"
            print(f"已更新伏笔 {fs.id} 的状态为'已埋下未回收'")
        
        # 更新需要在本章回收的伏笔状态
        reveal_foreshadowings = self.get_foreshadowings_to_reveal(chapter_number)
        for fs in reveal_foreshadowings:
            fs.status = "已回收"
            print(f"已更新伏笔 {fs.id} 的状态为'已回收'")
        
        # 如果有更新，保存到文件
        if plant_foreshadowings or reveal_foreshadowings:
            self.save()

    def plant_foreshadowing(self, id: str) -> bool:
        """
        标记伏笔为已埋下

        Args:
            id: 伏笔ID

        Returns:
            操作是否成功
        """
        return self.update_foreshadowing_status(id, "已埋下未回收")

    def reveal_foreshadowing(self, id: str) -> bool:
        """
        标记伏笔为已回收

        Args:
            id: 伏笔ID

        Returns:
            操作是否成功
        """
        return self.update_foreshadowing_status(id, "已回收")

    def get_unrevealed_foreshadowings(self) -> List[Foreshadowing]:
        """
        获取所有未回收的伏笔

        Returns:
            未回收的伏笔列表
        """
        return [f for f in self.foreshadowings
                if f.status == "已埋下未回收"]

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            字典格式的伏笔管理器
        """
        return {
            "foreshadowings": [foreshadowing.to_dict() for foreshadowing in self.foreshadowings]
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ForeshadowingManager':
        """
        从字典创建伏笔管理器

        Args:
            data: 字典格式的伏笔管理器

        Returns:
            伏笔管理器实例
        """
        foreshadowings = [Foreshadowing.from_dict(fs) for fs in data.get("foreshadowings", [])]
        return cls(foreshadowings=foreshadowings)

    def save(self) -> bool:
        """
        保存伏笔管理器到文件

        Returns:
            保存是否成功
        """
        try:
            # 确保目录存在
            ensure_directory(FORESHADOWINGS_DIR)

            # 为了向后兼容，也保存到原来的位置
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)

            # 将所有伏笔保存到单一文件
            foreshadowings_dict = [fs.to_dict() for fs in self.foreshadowings]
            save_all_foreshadowings(foreshadowings_dict)

            # 为了向后兼容，也保存分组文件
            migrate_foreshadowings_to_groups(foreshadowings_dict)

            return True
        except Exception as e:
            print(f"保存伏笔管理器时出错: {str(e)}")
            return False

    @classmethod
    def load(cls, file_path: Optional[str] = None) -> Optional['ForeshadowingManager']:
        """
        从文件加载伏笔管理器

        Args:
            file_path: 文件路径，如果为None则使用默认路径

        Returns:
            伏笔管理器实例，如果加载失败则返回None
        """
        try:
            # 首先尝试从单一文件加载
            all_foreshadowings = load_all_foreshadowings()

            if all_foreshadowings:
                foreshadowings = [Foreshadowing.from_dict(fs) for fs in all_foreshadowings]
                return cls(foreshadowings=foreshadowings)

            # 如果单一文件不存在，尝试从分组文件加载（向后兼容）
            all_foreshadowings = []

            # 加载已回收的伏笔
            try:
                revealed = load_foreshadowing_group("revealed")
                if revealed:
                    all_foreshadowings.extend(revealed)
            except Exception as e:
                print(f"加载已回收伏笔时出错: {str(e)}")

            # 加载未回收的伏笔
            try:
                unrevealed = load_foreshadowing_group("unrevealed")
                if unrevealed:
                    # 检查是否有嵌套的JSON结构
                    for fs_data in unrevealed:
                        content = fs_data.get("content", "")
                        if isinstance(content, str) and content.strip().startswith("{") and content.strip().endswith("}"):
                            try:
                                # 尝试解析JSON
                                content_data = json.loads(content)

                                # 如果解析成功且包含foreshadowings字段，说明这是一个嵌套的伏笔管理JSON
                                if isinstance(content_data, dict) and "foreshadowings" in content_data and isinstance(content_data["foreshadowings"], list):
                                    print("检测到嵌套的伏笔管理JSON结构，尝试修复...")
                                    # 提取嵌套的伏笔信息并替换当前组
                                    nested_foreshadowings = []
                                    for nested_fs in content_data["foreshadowings"]:
                                        if nested_fs.get("id") != "f0":
                                            nested_foreshadowings.append(nested_fs)

                                    if nested_foreshadowings:
                                        # 如果找到了嵌套的伏笔，替换当前组
                                        print(f"从嵌套JSON中提取了{len(nested_foreshadowings)}个伏笔")
                                        # 保存修复后的伏笔组
                                        save_foreshadowing_group("unrevealed", nested_foreshadowings)
                                        # 更新当前加载的伏笔
                                        unrevealed = nested_foreshadowings
                            except Exception as e:
                                print(f"解析嵌套的伏笔管理JSON时出错: {str(e)}")

                    all_foreshadowings.extend(unrevealed)
            except Exception as e:
                print(f"加载未回收伏笔时出错: {str(e)}")

            if all_foreshadowings:
                foreshadowings = [Foreshadowing.from_dict(fs) for fs in all_foreshadowings]
                manager = cls(foreshadowings=foreshadowings)
                # 保存到单一文件
                manager.save()
                return manager

            # 如果分组文件不存在或为空，尝试从旧文件加载
            file_path = file_path or os.path.join(OUTPUT_DIR, "foreshadowings.json")
            if not os.path.exists(file_path):
                return None

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    file_content = f.read()

                # 使用安全的JSON解析函数
                data = parse_json_safely(file_content, "foreshadowing")
                if not data:
                    print("无法解析伏笔文件")
                    return None

                # 检查是否有嵌套的JSON结构
                if "foreshadowings" in data:
                    for fs_data in data["foreshadowings"]:
                        content = fs_data.get("content", "")
                        if isinstance(content, str) and content.strip().startswith("{") and content.strip().endswith("}"):
                            try:
                                # 尝试解析JSON
                                content_data = json.loads(content)

                                # 如果解析成功且包含foreshadowings字段，说明这是一个嵌套的伏笔管理JSON
                                if isinstance(content_data, dict) and "foreshadowings" in content_data and isinstance(content_data["foreshadowings"], list):
                                    print("检测到嵌套的伏笔管理JSON结构，尝试修复...")
                                    # 提取嵌套的伏笔信息并替换当前数据
                                    nested_foreshadowings = []
                                    for nested_fs in content_data["foreshadowings"]:
                                        if nested_fs.get("id") != "f0":
                                            nested_foreshadowings.append(nested_fs)

                                    if nested_foreshadowings:
                                        # 如果找到了嵌套的伏笔，替换当前数据
                                        print(f"从嵌套JSON中提取了{len(nested_foreshadowings)}个伏笔")
                                        data["foreshadowings"] = nested_foreshadowings
                                        break
                            except Exception as e:
                                print(f"解析嵌套的伏笔管理JSON时出错: {str(e)}")

                # 加载后，自动迁移到单一文件
                manager = cls.from_dict(data)
                manager.save()  # 这会触发迁移

                return manager
            except Exception as e:
                print(f"加载伏笔管理器时出错: {str(e)}")
                return None
        except Exception as e:
            print(f"加载伏笔管理器时出错: {str(e)}")
            return None

    def __str__(self) -> str:
        """
        字符串表示

        Returns:
            伏笔管理器的字符串表示
        """
        result = []
        for foreshadowing in self.foreshadowings:
            result.append(f"[{foreshadowing.id}] {foreshadowing.content} - 状态: {foreshadowing.status}")
        return "\n".join(result)
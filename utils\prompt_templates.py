"""
提示词模板
"""

# 风格提示词生成模板
STYLE_PROMPT_TEMPLATE = """
请为一部男频网络小说的{genre}流派生成详细的风格提示词。

提示词应包含以下内容：
1. 该流派的文风特点
2. 常用写作手法和套路
3. 情节发展模式
4. 角色塑造特点
5. 世界观设定要点
6. 读者期望

请以结构化方式输出，便于后续创作使用。
"""

# 背景设定生成模板
BACKGROUND_PROMPT_TEMPLATE = """
根据以下男频网络小说的流派和风格指南，生成详细的背景设定：

流派：{genre}
风格指南：{style_guide}

请为这部小说创建完整的背景设定，包括但不限于：
1. 世界观（时代背景、地理环境、特殊规则等）
2. 社会体系（阶级、组织、势力划分等）
3. 特殊设定（如修炼体系、职业划分、特殊能力等）
4. 历史事件（对当前世界有重大影响的历史事件）
5. 文化特点（风俗习惯、信仰、禁忌等）

请以结构化方式输出，便于后续创作使用。
"""

# 章节大纲生成模板
OUTLINE_PROMPT_TEMPLATE = """
根据以下男频网络小说信息，为第{chapter_number}章生成详细大纲：

流派：{genre}
风格指南：{style_guide}
背景设定：{background}
当前章节：{chapter_number}/{total_chapters}
前序内容：{previous_summary}
现有人物：{characters}
现有伏笔：{foreshadowing}

{recent_scenes}
{already_planted_foreshadowings}

请提供以下内容：
1. 章节标题
2. 主要情节发展（开端、发展、高潮、结尾）
3. 出场人物及其行动
4. 新增设定或世界观信息（如有）
5. 新埋下的伏笔（如有）
6. 回收的前文伏笔（如有）
7. 角色关系变化（如有）
8. 情感基调和节奏

如果是第一章，需要设计好主角登场、初始冲突和吸引读者的开场；如果是中间章节，需要推动剧情发展并保持读者兴趣；如果是结尾章节，需要合理安排高潮和结局。

【重要注意事项】：
1. 不要重复使用最近章节已经出现过的场景。每个章节的场景应该有明显变化，除非剧情需要在同一场景持续发展。
2. 同一个伏笔只能埋下一次，不要重复埋下已经在前面章节埋下的伏笔。
3. 如果需要使用上一章的结束场景作为本章开端，应该很快转换到新场景。
4. 确保每个章节都能推动故事向前发展，不要出现情节停滞不前的情况。
5. 伏笔的埋下和回收必须明确，在大纲中要清楚标注哪些伏笔被埋下，哪些被回收。
6. 相邻章节的内容和主题应有明显区别，避免读者感到重复和拖沓。

请以结构化方式输出，便于后续创作使用。
"""

# 人物卡片生成模板
CHARACTER_PROMPT_TEMPLATE = """
根据以下男频网络小说信息，创建详细的人物卡片：

流派：{genre}
风格指南：{style_guide}
背景设定：{background}

请为这部小说创建主要人物卡片（至少包括主角、重要配角、主要反派），每个人物卡片应包含：
1. 基本信息（姓名、年龄、性别、身份/职业等）
2. 外貌特征
3. 性格特点
4. 能力/技能
5. 背景故事
6. 动机与目标
7. 与其他角色的关系
8. 成长轨迹预期
9. 当前状态

请以结构化JSON格式输出，便于后续程序处理和更新。格式参考：
```json
{{
    "characters": [
        {{
            "name": "角色名",
            "role": "主角/女主/主要配角/次要配角/主要反派/次要反派",
            "basic_info": {{...}},
            "appearance": "...",
            "personality": "...",
            "abilities": [...],
            "background": "...",
            "motivation": "...",
            "relationships": {{...}},
            "growth_path": "...",
            "current_status": "..."
        }},
        // 其他角色...
    ]
}}
```

确保人物设定符合男频网络小说特点，特别是主角应具备吸引力和成长空间。
"""

# 伏笔管理生成模板
FORESHADOWING_PROMPT_TEMPLATE = """
根据以下男频网络小说信息，创建详细的伏笔管理计划：

流派：{genre}
风格指南：{style_guide}
背景设定：{background}
人物卡片：{characters}
大纲：{outline}

请规划整部小说中的伏笔安排，包括：
1. 主线伏笔（影响主要剧情走向的关键线索）
2. 角色伏笔（与角色成长、身世相关的线索）
3. 世界观伏笔（与世界设定相关的隐藏信息）
4. 感情线伏笔（与感情发展相关的铺垫）

对每个伏笔，请提供：
- 伏笔内容
- 埋下伏笔的预计章节
- 回收伏笔的预计章节
- 伏笔的重要性（高/中/低）
- 伏笔埋下方式
- 伏笔回收效果

请以结构化JSON格式输出，便于后续程序处理和更新。格式参考：
```json
{{
    "foreshadowings": [
        {{
            "id": "f1",
            "type": "主线/角色/世界观/感情线",
            "content": "伏笔内容描述",
            "plant_chapter": 预计埋下章节,
            "reveal_chapter": 预计回收章节,
            "importance": "高/中/低",
            "plant_method": "埋下方式描述",
            "reveal_effect": "回收效果描述",
            "status": "未埋下/已埋下未回收/已回收",
            "related_characters": ["角色名1", "角色名2"]
        }},
        // 其他伏笔...
    ]
}}
```

确保伏笔安排合理，能够增强故事的连贯性和吸引力。
"""

# 章节内容生成模板
CHAPTER_PROMPT_TEMPLATE = """
请根据以下信息，创作一篇男频网络小说章节，字数在{target_length}汉字左右：

【背景设定】
{background}

【人物卡片】
{characters}

【伏笔管理】
{foreshadowing}

【前文概要】
{previous_summary}

【本章大纲】
{chapter_outline}

请严格按照大纲内容进行创作，注意以下要点：
1. 确保内容严格遵循大纲，大纲中的每个关键情节点都必须在正文中展开，不能遗漏
2. 保持角色言行与人物设定一致
3. 按计划埋下和回收伏笔
4. 保持情节连贯，逻辑合理
5. 确保文风符合男频网络小说特点，节奏紧凑
6. 使用恰当的细节描写，增强代入感
7. 保持章节长度约为{target_length}汉字
8. 根据大纲设置合理的章节标题

直接输出章节内容，无需额外说明。以章节标题开始，正文结束即可。
"""

# 章节检查模板
CHAPTER_CHECK_PROMPT_TEMPLATE = """
请对以下小说章节内容进行全面审核，检查是否符合要求：

【章节大纲】
{chapter_outline}

【人物卡片】
{characters}

【背景设定】
{background}

【伏笔管理】
{foreshadowing}

【章节内容】
{chapter_content}

请严格检查以下几点：
1. 内容是否严格按照大纲生成
2. 是否存在未经设定的内容或设定冲突
3. 是否存在前后逻辑矛盾
4. 人物言行是否符合人物设定
5. 伏笔埋下/回收是否按计划执行
6. 章节长度是否合适（约1万汉字）
7. 是否存在其他问题

请以JSON格式返回检查结果：
```json
{{
    "passed": true/false,
    "issues": [
        {{
            "type": "问题类型（大纲偏离/设定冲突/逻辑矛盾/人物不符/伏笔问题/其他）",
            "description": "问题详细描述",
            "location": "问题在文中的大致位置",
            "suggestion": "修改建议"
        }},
        // 其他问题...
    ],
    "character_updates": [
        {{
            "name": "角色名",
            "updates": {{
                "current_status": "更新后的状态描述",
                // 其他需要更新的字段...
            }}
        }},
        // 其他角色更新...
    ],
    "foreshadowing_updates": [
        {{
            "id": "伏笔ID",
            "status": "更新后的状态",
            // 其他需要更新的字段...
        }},
        // 其他伏笔更新...
    ]
}}
```

如果没有发现任何问题，请返回passed为true且issues为空数组。否则，请详细列出所有问题并给出具体修改建议。
""" 
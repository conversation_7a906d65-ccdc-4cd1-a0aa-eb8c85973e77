from novel.generator import NovelGenerator

def test_traditional_generation():
    try:
        print("初始化NovelGenerator...")
        generator = NovelGenerator()
        
        print("加载项目数据...")
        # 尝试加载项目，如果失败则初始化必要的组件
        if not generator.load_project():
            print("加载项目失败，进行基本初始化...")
            # 设置必要的属性
            generator.total_chapters = 10
            # 初始化API
            generator.init_apis()
            
            # 初始化outline
            from models.outline import NovelOutline
            generator.outline = NovelOutline()
            generator.outline.total_chapters = 10
            
            # 确保第一章大纲存在
            from models.outline import ChapterOutline
            chapter_outline = ChapterOutline(1)
            chapter_outline.title = "第1章 测试章节"
            chapter_outline.content = "这是一个测试章节大纲。"
            generator.outline.add_chapter(chapter_outline)
            
            # 初始化其他必要组件
            from models.character import CharacterManager
            generator.character_manager = CharacterManager()
            
            from models.foreshadowing import ForeshadowingManager
            generator.foreshadowing_manager = ForeshadowingManager()
            
            from models.background import Background
            generator.background = Background()
            
            print("基本初始化完成")
        
        # 获取章节号
        chapter_number = 1
        print(f"测试传统生成方法，生成第{chapter_number}章内容...")
        
        # 调用传统生成方法
        success = generator._generate_chapter_content_traditional(chapter_number)
        
        print(f"传统生成方法结果: {'成功' if success else '失败'}")
        
        if success:
            # 检查章节状态
            chapter_outline = generator.outline.get_chapter(chapter_number)
            if chapter_outline:
                print(f"章节完成状态: is_completed={chapter_outline.is_completed}")
                print(f"章节生成状态: is_generated={chapter_outline.is_generated}")
        
    except Exception as e:
        import traceback
        print(f"测试过程中出错: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    test_traditional_generation() 
#!/usr/bin/env python3
"""
从大纲文件生成小说的命令行脚本。
"""

import argparse
import os
import sys
from utils.file_manager import export_novel_as_txt
from generate_from_outline import generate_chapters_from_outline, generate_novel_style_and_background, continue_novel_generation_entry

def main():
    """小说生成器的主入口点"""
    parser = argparse.ArgumentParser(description='从大纲文件生成小说')
    
    # 添加子命令支持
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 生成章节的子命令
    chapters_parser = subparsers.add_parser('generate_chapters', help='从大纲生成章节')
    chapters_parser.add_argument('--outline', '-o', type=str, default='output_new/outline.json',
                       help='大纲文件的路径 (默认: output_new/outline.json)')
    chapters_parser.add_argument('--output-dir', '-d', type=str, default='output_new',
                       help='保存生成章节的目录 (默认: output_new)')
    
    # 生成风格和背景的子命令
    style_parser = subparsers.add_parser('generate_style', help='生成小说风格和背景设定')
    style_parser.add_argument('--genre', '-g', type=str, default='玄幻',
                       help='小说流派 (默认: 玄幻)')
    style_parser.add_argument('--output-dir', '-d', type=str, default='output_new',
                       help='保存生成内容的目录 (默认: output_new)')
    
    # 从当前章节继续生成的子命令
    continue_parser = subparsers.add_parser('continue', help='从当前已生成的章节继续生成到结尾')
    continue_parser.add_argument('--outline', '-o', type=str, default='output_new/outline.json',
                       help='大纲文件的路径 (默认: output_new/outline.json)')
    continue_parser.add_argument('--output-dir', '-d', type=str, default='output_new',
                       help='保存生成章节的目录 (默认: output_new)')
    
    # 导出小说的子命令
    export_parser = subparsers.add_parser('export', help='将生成的章节导出为单个txt文件')
    export_parser.add_argument('--output-file', '-f', type=str, default=None,
                       help='输出文件的路径 (默认: 使用小说标题)')
    export_parser.add_argument('--output-dir', '-d', type=str, default='output_new',
                       help='章节所在的目录 (默认: output_new)')
    
    # 兼容旧版：如果没有子命令，默认为生成章节
    parser.add_argument('--outline', '-o', type=str, default='output_new/outline.json',
                       help='大纲文件的路径 (默认: output_new/outline.json)')
    parser.add_argument('--output-dir', '-d', type=str, default='output_new',
                       help='保存生成章节的目录 (默认: output_new)')
    
    args = parser.parse_args()
    
    # 根据命令执行不同操作
    if args.command == 'generate_style':
        # 生成风格和背景
        try:
            print(f"正在生成{args.genre}流派小说的风格和背景设定...")
            print(f"输出目录: {args.output_dir}")
            
            success = generate_novel_style_and_background(args.genre, args.output_dir)
            
            if success:
                print(f"\n风格和背景设定生成成功完成！")
                print(f"输出文件位于: {os.path.abspath(args.output_dir)}")
            else:
                print("生成风格和背景设定失败。")
                sys.exit(1)
                
        except Exception as e:
            print(f"生成风格和背景设定时出错: {str(e)}")
            sys.exit(1)
    elif args.command == 'continue':
        # 从当前章节继续生成
        try:
            print(f"从当前章节继续生成小说: {args.outline}")
            print(f"输出目录: {args.output_dir}")
            
            # 检查大纲文件是否存在
            if not os.path.exists(args.outline):
                print(f"错误: 未找到大纲文件 '{args.outline}'")
                sys.exit(1)
            
            continue_novel_generation_entry(args.outline, args.output_dir)
            
            print(f"\n小说生成成功完成！")
            print(f"输出文件位于: {os.path.abspath(args.output_dir)}")
                
        except Exception as e:
            print(f"继续生成小说时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    elif args.command == 'export':
        # 导出小说为单个txt文件
        try:
            print(f"正在导出小说...")
            print(f"章节目录: {args.output_dir}")
            
            success = export_novel_as_txt(args.output_file, args.output_dir)
            
            if success:
                print(f"小说导出成功！")
            else:
                print("小说导出失败。")
                sys.exit(1)
                
        except Exception as e:
            print(f"导出小说时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    else:
        # 默认行为或明确的generate_chapters命令：生成章节
        outline_path = args.outline
        output_dir = args.output_dir
        
        try:
            # 检查大纲文件是否存在
            if not os.path.exists(outline_path):
                print(f"错误: 未找到大纲文件 '{outline_path}'")
                sys.exit(1)
            
            # 生成章节
            print(f"正在从大纲生成小说: {outline_path}")
            print(f"输出目录: {output_dir}")
        
            generate_chapters_from_outline(outline_path, output_dir)
        
            print(f"\n小说生成成功完成！")
            print(f"输出文件位于: {os.path.abspath(output_dir)}")
        
        except Exception as e:
            print(f"生成小说时出错: {str(e)}")
            sys.exit(1)

if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
"""
生成小说大纲工具

直接从生成大纲步骤开始，跳过前面的步骤。
用于测试和调试大纲生成过程中的JSON解析错误。
"""

import os
import sys
import json
import logging
import re
import traceback
from typing import Dict, Any, Optional, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from novel.generator import NovelGenerator
from utils.json_helper import extract_nested_json, parse_json_safely
from utils.fix_storyline import fix_storyline_json
from config import OUTPUT_DIR

# 配置日志输出
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('outline_debug.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def visualize_special_chars(text: str) -> str:
    """
    可视化字符串中的特殊字符（如换行符、制表符等）
    
    Args:
        text: 要处理的字符串
    
    Returns:
        处理后的字符串，特殊字符被替换为可视形式
    """
    # 替换常见的不可见字符为可视形式
    replacements = {
        '\n': '\\n',
        '\r': '\\r',
        '\t': '\\t',
        ' ': '␣',  # 用特殊符号表示空格
    }
    
    result = ""
    for char in text:
        if char in replacements:
            result += replacements[char]
        else:
            result += char
    
    return result

def fix_newline_before_title(json_str: str) -> str:
    """
    修复JSON中换行符+title的问题和其他常见格式错误
    
    Args:
        json_str: 要修复的JSON字符串
    
    Returns:
        修复后的JSON字符串
    """
    # 0. 移除Markdown代码块标记
    # 移除开头的```json或```等标记
    json_str = re.sub(r'^```\w*\s*', '', json_str)
    # 移除结尾的```标记
    json_str = re.sub(r'\s*```\s*$', '', json_str)
    
    # 1. 先移除所有实际换行，转为一行文本，便于处理
    flattened = re.sub(r'[\r\n]+', ' ', json_str)
    
    # 2. 移除多余的空格，保持每个标记之间只有一个空格
    flattened = re.sub(r'\s+', ' ', flattened)
    
    # 3. 特殊处理属性名前缺失逗号的情况（如title前缺少逗号）
    flattened = re.sub(r'(\}|\]|"[^"]*")\s+"([^"]+)":', r'\1, "\2":', flattened)
    
    # 4. 尝试使用json模块的loads方法解析
    try:
        data = json.loads(flattened)
        return flattened
    except json.JSONDecodeError:
        pass  # 继续修复
    
    # 5. 针对常见错误的更具体修复
    # 5.1 处理对象/数组结尾后没有逗号直接跟属性名的情况
    # 例如: {"items": [1, 2, 3]} "next": 4 -> {"items": [1, 2, 3]}, "next": 4
    flattened = re.sub(r'(\}|\])\s+"([^"]+)":', r'\1, "\2":', flattened)
    
    # 5.2 处理值后面没有逗号直接跟属性名的情况
    # 例如: {"name": "value" "next": 123} -> {"name": "value", "next": 123}
    flattened = re.sub(r'"[^"]*"\s+"([^"]+)":', r'", "\1":', flattened)
    
    # 5.3 处理数值、布尔值、null后面没有逗号直接跟属性名的情况
    flattened = re.sub(r'(true|false|null|[0-9]+)\s+"([^"]+)":', r'\1, "\2":', flattened)
    
    # 5.4 处理数组元素间缺少逗号的情况
    flattened = re.sub(r'("[^"]*"|[0-9]+|true|false|null)\s+("[^"]*")', r'\1, \2', flattened)
    flattened = re.sub(r'(\}|\])\s+(\{|\[)', r'\1, \2', flattened)
    
    # 5.5 修复多余的逗号（数组或对象末尾的逗号）
    flattened = re.sub(r',\s*(\}|\])', r'\1', flattened)
    
    # 5.6 确保括号匹配
    open_braces = flattened.count('{')
    close_braces = flattened.count('}')
    open_brackets = flattened.count('[')
    close_brackets = flattened.count(']')
    
    if open_braces > close_braces:
        flattened += '}' * (open_braces - close_braces)
    if open_brackets > close_brackets:
        flattened += ']' * (open_brackets - close_brackets)
    
    # 6. 最终规范化格式
    flattened = flattened.strip()
    
    return flattened

def debug_json_parsing(json_str: str) -> Dict[str, Any]:
    """
    调试JSON解析过程，识别可能的问题并尝试多种修复方法
    
    Args:
        json_str: 需要解析的JSON字符串
    
    Returns:
        解析后的JSON数据或错误信息
    """
    # 尝试检测和修复编码问题
    try:
        # 如果传入的是字节串，尝试解码
        if isinstance(json_str, bytes):
            for encoding in ['utf-8', 'utf-16', 'gbk', 'gb2312', 'iso-8859-1']:
                try:
                    json_str = json_str.decode(encoding)
                    logger.info(f"成功使用{encoding}编码解码JSON")
                    break
                except UnicodeDecodeError:
                    continue
    except Exception as e:
        logger.warning(f"处理编码时出错: {e}")
    
    # 记录原始JSON
    logger.info("=== 原始JSON字符串 ===")
    if len(json_str) > 1000:
        logger.info(f"JSON前500字符: {json_str[:500]}...")
        logger.info(f"JSON后500字符: {json_str[-500:]}...")
    else:
        logger.info(json_str)
    
    # 预分析阶段：检查常见JSON错误模式
    error_patterns = {
        r'\n\s+"([^"]+)"': "发现换行+属性名错误模式",
        r'"\s+"([^"]+)"': "发现缺少逗号错误模式",
        r'(\}|\])\s+"([^"]+)"': "发现对象/数组后缺少逗号错误模式",
        r'([^\\])""': "发现重复引号错误模式",
        r'"[^"]*\\"[^"]*"': "发现未转义引号错误模式",
        r',\s*(\}|\])': "发现尾部多余逗号错误模式"
    }
    
    detected_errors = []
    for pattern, message in error_patterns.items():
        if re.search(pattern, json_str):
            detected_errors.append(message)
    
    if detected_errors:
        logger.warning("预分析发现以下可能的错误模式:")
        for err in detected_errors:
            logger.warning(f"- {err}")
    
    # 尝试直接解析
    try:
        data = json.loads(json_str)
        logger.info("直接解析JSON成功")
        return data
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {e}")
        
        # 记录错误位置附近的内容
        error_pos = e.pos
        start_pos = max(0, error_pos - 50)
        end_pos = min(len(json_str), error_pos + 50)
        error_context = json_str[start_pos:end_pos]
        
        # 显示可视化的错误上下文（显示不可见字符）
        vis_context = visualize_special_chars(error_context)
        logger.error(f"错误位置附近的内容（可视化）: {vis_context}")
        logger.error(f"错误位置指示: {' ' * (min(50, error_pos - start_pos))}^")
        
        # 尝试根据错误类型提供针对性修复
        if "Expecting ',' delimiter" in str(e):
            logger.info("检测到缺少逗号错误，尝试修复...")
            # 在错误位置附近添加逗号
            fixed_json = json_str[:error_pos] + "," + json_str[error_pos:]
            try:
                fixed_data = json.loads(fixed_json)
                logger.info("添加逗号修复成功")
                return fixed_data
            except json.JSONDecodeError:
                logger.info("简单添加逗号修复失败，尝试更复杂的修复")
        elif "Expecting property name" in str(e):
            logger.info("检测到期望属性名错误，可能是格式问题...")
        elif "Unterminated string" in str(e):
            logger.info("检测到未闭合的字符串，尝试修复...")
        
        # 尝试修复换行符+title问题
        logger.info("尝试修复换行符+title问题...")
        fixed_json_str = fix_newline_before_title(json_str)
        
        # 检查是否发生了修改
        if fixed_json_str != json_str:
            logger.info("应用了换行符+title修复")
            
            # 尝试解析修复后的JSON
            try:
                data = json.loads(fixed_json_str)
                logger.info("修复换行符+title问题后解析成功")
                
                # 保存修复后的JSON到文件
                fixed_path = os.path.join(OUTPUT_DIR, "fixed_newline_title.json")
                with open(fixed_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                logger.info(f"修复后的JSON已保存到: {fixed_path}")
                
                # 正确地将修复后的内容添加到outline.json的chapters字段中
                try:
                    logger.info("尝试将修复后的内容添加到outline.json...")
                    from models.outline import ChapterOutline, ChapterOutlineManager
                    
                    # 尝试从文件名中提取章节号
                    chapter_number = 1  # 默认为第1章
                    
                    # 从JSON数据中提取标题
                    title = data.get("title", f"第{chapter_number}章")
                    
                    # 创建章节大纲对象
                    chapter_outline = ChapterOutline(
                        chapter_number=chapter_number,
                        title=title,
                        content=json.dumps(data, ensure_ascii=False),  # 将修复后的数据转为字符串
                        is_generated=False,
                        is_completed=False
                    )
                    
                    # 如果data中有结构化的数据，添加到章节大纲对象
                    if isinstance(data, dict):
                        # 提取章节概要
                        if "chapter_summary" in data:
                            chapter_outline.chapter_summary = data["chapter_summary"]
                        
                        # 提取关键情节点
                        if "key_points" in data:
                            chapter_outline.key_points = data["key_points"]
                        
                        # 提取出场人物
                        if "characters" in data:
                            chapter_outline.characters = data["characters"]
                        
                        # 提取伏笔安排
                        if "foreshadowings" in data:
                            chapter_outline.foreshadowings = data["foreshadowings"]
                    
                    # 尝试读取现有的outline管理器
                    outline_manager = ChapterOutlineManager.load(total_chapters=79)  # 假设总章节数为79
                    
                    if not outline_manager:
                        logger.info("创建新的outline管理器...")
                        outline_manager = ChapterOutlineManager(total_chapters=79)
                    
                    # 添加章节到outline管理器并保存
                    outline_manager.add_chapter(chapter_outline)
                    if outline_manager.save():
                        logger.info(f"成功将修复后的大纲保存到outline.json")
                    else:
                        logger.error("保存到outline.json失败")
                except Exception as e:
                    logger.error(f"将修复后的内容添加到outline.json时出错: {e}")
                    logger.error(traceback.format_exc())
                
                return data
            except json.JSONDecodeError as e2:
                logger.error(f"修复换行符+title问题后仍然解析失败: {e2}")
                # 继续尝试其他修复方法
        
        # 尝试使用更多重修复方法
        logger.info("尝试使用多种修复方法...")
        
        # 1. 尝试使用fix_storyline_json修复
        try:
            logger.info("尝试使用fix_storyline_json修复...")
            from utils.fix_storyline import fix_storyline_json
            fixed_data = fix_storyline_json(json_str)
            
            if fixed_data:
                logger.info("使用fix_storyline_json修复成功")
                # 将修复后的数据写入文件
                fixed_json_path = os.path.join(OUTPUT_DIR, "fixed_outline.json")
                with open(fixed_json_path, 'w', encoding='utf-8') as f:
                    json.dump(fixed_data, f, ensure_ascii=False, indent=2)
                logger.info(f"修复后的JSON已保存到: {fixed_json_path}")
                
                # 正确地将修复后的内容添加到outline.json的chapters字段中
                try:
                    logger.info("尝试将修复后的内容添加到outline.json...")
                    from models.outline import ChapterOutline, ChapterOutlineManager
                    
                    # 尝试从文件名中提取章节号
                    chapter_number = 1  # 默认为第1章
                    
                    # 从JSON数据中提取标题
                    title = fixed_data.get("title", f"第{chapter_number}章")
                    
                    # 创建章节大纲对象
                    chapter_outline = ChapterOutline(
                        chapter_number=chapter_number,
                        title=title,
                        content=json.dumps(fixed_data, ensure_ascii=False),  # 将修复后的数据转为字符串
                        is_generated=False,
                        is_completed=False
                    )
                    
                    # 如果fixed_data中有结构化的数据，添加到章节大纲对象
                    if isinstance(fixed_data, dict):
                        # 提取章节概要
                        if "chapter_summary" in fixed_data:
                            chapter_outline.chapter_summary = fixed_data["chapter_summary"]
                        
                        # 提取关键情节点
                        if "key_points" in fixed_data:
                            chapter_outline.key_points = fixed_data["key_points"]
                        
                        # 提取出场人物
                        if "characters" in fixed_data:
                            chapter_outline.characters = fixed_data["characters"]
                        
                        # 提取伏笔安排
                        if "foreshadowings" in fixed_data:
                            chapter_outline.foreshadowings = fixed_data["foreshadowings"]
                    
                    # 尝试读取现有的outline管理器
                    outline_manager = ChapterOutlineManager.load(total_chapters=79)  # 假设总章节数为79
                    
                    if not outline_manager:
                        logger.info("创建新的outline管理器...")
                        outline_manager = ChapterOutlineManager(total_chapters=79)
                    
                    # 添加章节到outline管理器并保存
                    outline_manager.add_chapter(chapter_outline)
                    if outline_manager.save():
                        logger.info(f"成功将修复后的大纲保存到outline.json")
                    else:
                        logger.error("保存到outline.json失败")
                except Exception as e:
                    logger.error(f"将修复后的内容添加到outline.json时出错: {e}")
                    logger.error(traceback.format_exc())
                
                return fixed_data
        except Exception as e3:
            logger.error(f"使用fix_storyline_json修复时出错: {e3}")
        
        # 2. 尝试使用utils.json_helper中的方法
        try:
            logger.info("尝试使用json_helper中的方法修复...")
            from utils.json_helper import fix_json
            fixed_json = fix_json(json_str)
            try:
                data = json.loads(fixed_json)
                logger.info("使用fix_json修复成功")
                
                # 保存修复后的JSON
                fixed_path = os.path.join(OUTPUT_DIR, "fix_json_fixed.json")
                with open(fixed_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                logger.info(f"fix_json修复的结果已保存到: {fixed_path}")
                
                return data
            except json.JSONDecodeError:
                logger.info("使用fix_json修复失败")
        except ImportError:
            logger.warning("无法导入fix_json函数")
        except Exception as e4:
            logger.error(f"使用fix_json修复时出错: {e4}")
        
        # 3. 尝试使用extract_nested_json作为最后的手段
        logger.info("尝试使用extract_nested_json提取嵌套JSON...")
        try:
            from utils.json_helper import extract_nested_json
            extracted_json = extract_nested_json(json_str)
            if extracted_json:
                try:
                    data = json.loads(extracted_json)
                    logger.info("使用extract_nested_json成功提取JSON")
                    
                    # 保存提取的JSON
                    extracted_path = os.path.join(OUTPUT_DIR, "extracted_outline.json")
                    with open(extracted_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    logger.info(f"提取的JSON已保存到: {extracted_path}")
                    
                    return data
                except json.JSONDecodeError:
                    logger.info("提取的嵌套JSON仍无法解析")
        except ImportError:
            logger.warning("无法导入extract_nested_json函数")
        except Exception as e5:
            logger.error(f"使用extract_nested_json时出错: {e5}")
            
        logger.error("所有修复方法都失败")
        return {"error": str(e), "context": error_context, "detected_errors": detected_errors}

def get_outline_directly(generator, chapter_number: int = 1) -> bool:
    """
    直接从API获取大纲并应用我们的修复函数
    
    Args:
        generator: NovelGenerator实例
        chapter_number: 章节号
        
    Returns:
        是否成功获取并解析大纲
    """
    logger.info(f"直接从API获取第{chapter_number}章大纲...")
    
    try:
        # 从主线中提取当前章节内容
        from utils.file_manager import load_main_storyline
        main_storyline_data = load_main_storyline(generator.total_chapters)
        
        # 如果返回的是字典，转换为JSON字符串
        if isinstance(main_storyline_data, dict):
            import json
            main_storyline = json.dumps(main_storyline_data, ensure_ascii=False)
        else:
            main_storyline = main_storyline_data
        
        # 调用API生成大纲
        try:
            outline_content = generator.api.generate_outline(
                generator.genre,
                generator.style_guide,
                str(generator.background),
                chapter_number,
                generator.total_chapters,
                "",  # 空的previous_summary
                "",  # 空的characters_info
                "",  # 空的foreshadowing_info
                main_storyline=main_storyline
            )
        except KeyError as e:
            # 专门处理换行+title错误
            if str(e) == "'\\n          \"title\"'" or "title" in str(e):
                logger.error(f"捕获到换行+title错误: {e}，尝试修复...")
                # 尝试使用简化参数重新调用API
                outline_content = generator.api.generate_outline(
                    generator.genre,
                    generator.style_guide,
                    str(generator.background),
                    chapter_number,
                    generator.total_chapters
                )
            else:
                # 其他类型的KeyError，继续抛出
                raise
        
        if not outline_content:
            logger.error("API返回空大纲内容")
            return False
        
        # 保存原始JSON到debug文件
        debug_path = os.path.join(OUTPUT_DIR, "debug.json")
        with open(debug_path, 'w', encoding='utf-8') as f:
            f.write(outline_content)
        logger.info(f"原始JSON已保存到: {debug_path}")
        
        # 应用全面的JSON修复
        logger.info("应用JSON修复...")
        fixed_content = fix_newline_before_title(outline_content)
        
        # 保存修复后的JSON (仅用于调试)
        fixed_path = os.path.join(OUTPUT_DIR, "fixed_debug.json")
        with open(fixed_path, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        logger.info(f"修复后的JSON已保存到: {fixed_path} (仅用于调试)")
        
        # 尝试解析修复后的JSON
        try:
            outline_data = json.loads(fixed_content)
            logger.info("修复后的JSON解析成功")
            
            # 保存格式化后的JSON (仅用于调试)
            formatted_path = os.path.join(OUTPUT_DIR, "formatted_debug.json")
            with open(formatted_path, 'w', encoding='utf-8') as f:
                json.dump(outline_data, f, ensure_ascii=False, indent=2)
            logger.info(f"格式化后的JSON已保存到: {formatted_path} (仅用于调试)")
            
            # 创建章节大纲对象并保存到outline.json
            # 从解析后的数据提取章节信息
            try:
                logger.info("创建章节大纲对象...")
                from models.outline import ChapterOutline
                
                # 提取标题
                title = outline_data.get("title", f"第{chapter_number}章")
                
                # 创建章节大纲对象
                chapter_outline = ChapterOutline(
                    chapter_number=chapter_number,
                    title=title,
                    content=fixed_content,  # 保存整个内容
                    is_generated=False,  # 这里只生成了大纲，章节内容尚未生成
                    is_completed=False
                )
                
                # 如果outline_data中有结构化的数据，添加到章节大纲对象
                if isinstance(outline_data, dict):
                    # 提取章节概要
                    if "chapter_summary" in outline_data:
                        chapter_outline.chapter_summary = outline_data["chapter_summary"]
                    
                    # 提取关键情节点
                    if "key_points" in outline_data:
                        chapter_outline.key_points = outline_data["key_points"]
                    
                    # 提取出场人物
                    if "characters" in outline_data:
                        chapter_outline.characters = outline_data["characters"]
                    
                    # 提取伏笔安排
                    if "foreshadowings" in outline_data:
                        chapter_outline.foreshadowings = outline_data["foreshadowings"]
                
                # 将章节大纲添加到管理器并保存
                logger.info(f"添加第{chapter_number}章大纲到outline.json...")
                if generator.outline:
                    generator.outline.add_chapter(chapter_outline)
                    success = generator.outline.save()
                    if success:
                        logger.info(f"成功保存第{chapter_number}章大纲到outline.json")
                    else:
                        logger.error("保存大纲到outline.json失败")
                else:
                    logger.error("找不到outline管理器，无法保存")
            except Exception as e:
                logger.error(f"创建或保存章节大纲时出错: {e}")
                logger.error(traceback.format_exc())
            
            return True
        except json.JSONDecodeError as e:
            logger.error(f"修复后JSON解析错误: {e}")
            
            # 尝试使用更全面的调试功能
            logger.info("尝试使用debug_json_parsing进行深度分析...")
            parsed_data = debug_json_parsing(outline_content)
            
            if isinstance(parsed_data, dict) and "error" not in parsed_data:
                logger.info("使用debug_json_parsing成功解析JSON")
                # 保存修复后的数据 (仅用于调试)
                fixed_path = os.path.join(OUTPUT_DIR, "deep_fixed.json")
                with open(fixed_path, 'w', encoding='utf-8') as f:
                    json.dump(parsed_data, f, ensure_ascii=False, indent=2)
                logger.info(f"深度修复后的JSON已保存到: {fixed_path} (仅用于调试)")
                
                # 创建章节大纲对象并保存到outline.json
                try:
                    logger.info("创建章节大纲对象(使用深度修复数据)...")
                    from models.outline import ChapterOutline
                    
                    # 提取标题
                    title = parsed_data.get("title", f"第{chapter_number}章")
                    
                    # 创建章节大纲对象
                    chapter_outline = ChapterOutline(
                        chapter_number=chapter_number,
                        title=title,
                        content=json.dumps(parsed_data, ensure_ascii=False),  # 将修复后的数据转为字符串
                        is_generated=False,
                        is_completed=False
                    )
                    
                    # 如果parsed_data中有结构化的数据，添加到章节大纲对象
                    if isinstance(parsed_data, dict):
                        # 提取章节概要
                        if "chapter_summary" in parsed_data:
                            chapter_outline.chapter_summary = parsed_data["chapter_summary"]
                        
                        # 提取关键情节点
                        if "key_points" in parsed_data:
                            chapter_outline.key_points = parsed_data["key_points"]
                        
                        # 提取出场人物
                        if "characters" in parsed_data:
                            chapter_outline.characters = parsed_data["characters"]
                        
                        # 提取伏笔安排
                        if "foreshadowings" in parsed_data:
                            chapter_outline.foreshadowings = parsed_data["foreshadowings"]
                    
                    # 将章节大纲添加到管理器并保存
                    logger.info(f"添加第{chapter_number}章大纲到outline.json...")
                    if generator.outline:
                        generator.outline.add_chapter(chapter_outline)
                        success = generator.outline.save()
                        if success:
                            logger.info(f"成功保存第{chapter_number}章大纲到outline.json")
                        else:
                            logger.error("保存大纲到outline.json失败")
                    else:
                        logger.error("找不到outline管理器，无法保存")
                except Exception as e:
                    logger.error(f"创建或保存章节大纲时出错: {e}")
                    logger.error(traceback.format_exc())
                
                return True
            else:
                logger.error("debug_json_parsing分析失败")
                
                # 最后尝试使用fix_storyline_json修复
                logger.info("尝试使用fix_storyline_json修复...")
                try:
                    from utils.fix_storyline import fix_storyline_json
                    fixed_data = fix_storyline_json(outline_content)
                    
                    if fixed_data:
                        logger.info("使用fix_storyline_json修复成功")
                        
                        # 保存修复后的数据 (仅用于调试)
                        fixed_path = os.path.join(OUTPUT_DIR, "fixed_storyline.json")
                        with open(fixed_path, 'w', encoding='utf-8') as f:
                            json.dump(fixed_data, f, ensure_ascii=False, indent=2)
                        logger.info(f"storyline修复后的JSON已保存到: {fixed_path} (仅用于调试)")
                        
                        # 创建章节大纲对象并保存到outline.json
                        try:
                            logger.info("创建章节大纲对象(使用fix_storyline_json数据)...")
                            from models.outline import ChapterOutline
                            
                            # 提取标题
                            title = fixed_data.get("title", f"第{chapter_number}章")
                            
                            # 创建章节大纲对象
                            chapter_outline = ChapterOutline(
                                chapter_number=chapter_number,
                                title=title,
                                content=json.dumps(fixed_data, ensure_ascii=False),  # 将修复后的数据转为字符串
                                is_generated=False,
                                is_completed=False
                            )
                            
                            # 如果fixed_data中有结构化的数据，添加到章节大纲对象
                            if isinstance(fixed_data, dict):
                                # 提取章节概要
                                if "chapter_summary" in fixed_data:
                                    chapter_outline.chapter_summary = fixed_data["chapter_summary"]
                                
                                # 提取关键情节点
                                if "key_points" in fixed_data:
                                    chapter_outline.key_points = fixed_data["key_points"]
                                
                                # 提取出场人物
                                if "characters" in fixed_data:
                                    chapter_outline.characters = fixed_data["characters"]
                                
                                # 提取伏笔安排
                                if "foreshadowings" in fixed_data:
                                    chapter_outline.foreshadowings = fixed_data["foreshadowings"]
                            
                            # 将章节大纲添加到管理器并保存
                            logger.info(f"添加第{chapter_number}章大纲到outline.json...")
                            if generator.outline:
                                generator.outline.add_chapter(chapter_outline)
                                success = generator.outline.save()
                                if success:
                                    logger.info(f"成功保存第{chapter_number}章大纲到outline.json")
                                else:
                                    logger.error("保存大纲到outline.json失败")
                            else:
                                logger.error("找不到outline管理器，无法保存")
                        except Exception as e:
                            logger.error(f"创建或保存章节大纲时出错: {e}")
                            logger.error(traceback.format_exc())
                        
                        return True
                    else:
                        logger.error("使用fix_storyline_json修复失败")
                        return False
                except Exception as e2:
                    logger.error(f"尝试使用fix_storyline_json修复时出错: {e2}")
                    return False
    except Exception as e:
        logger.error(f"获取大纲时出错: {e}")
        logger.error(traceback.format_exc())
        return False

def test_generate_outline(chapter_number: int = None) -> None:
    """
    测试生成大纲功能

    Args:
        chapter_number: 要生成的章节号，如果为None则生成所有章节的大纲
    """
    logger.info(f"===== 开始测试生成{('第'+str(chapter_number)+'章') if chapter_number else '所有章节'}大纲 =====")
    
    try:
        # 初始化小说生成器
        generator = NovelGenerator()
        
        # 检查是否有必要的项目文件
        if os.path.exists(os.path.join(OUTPUT_DIR, "background.json")) and \
           os.path.exists(os.path.join(OUTPUT_DIR, "style_guide.txt")) and \
           os.path.exists(os.path.join(OUTPUT_DIR, "main_storyline.json")):
            
            logger.info("找到必要的项目文件，尝试初始化...")
            
            # 手动初始化必要的属性
            generator.genre = "修真"  # 设置一个默认的流派
            
            # 读取风格指南
            with open(os.path.join(OUTPUT_DIR, "style_guide.txt"), 'r', encoding='utf-8') as f:
                generator.style_guide = f.read()
                
            # 读取背景设定
            with open(os.path.join(OUTPUT_DIR, "background.json"), 'r', encoding='utf-8') as f:
                background_data = json.load(f)
                generator.background = background_data
                
            # 初始化章节总数
            generator.total_chapters = 79  # 设置一个合理的默认值
            
            # 初始化API
            generator.init_apis()
            
            if chapter_number is not None:
                # 生成单个章节的大纲
                logger.info(f"开始生成第{chapter_number}章大纲...")
                success = get_outline_directly(generator, chapter_number)
                
                if success:
                    logger.info(f"第{chapter_number}章大纲生成成功")
                else:
                    logger.error(f"第{chapter_number}章大纲生成失败")
                    
                    # 查看是否有生成的原始JSON
                    debug_json_path = os.path.join(OUTPUT_DIR, "debug.json")
                    if os.path.exists(debug_json_path):
                        try:
                            with open(debug_json_path, 'r', encoding='utf-8') as f:
                                json_str = f.read()
                            
                            # 调试JSON解析
                            logger.info("分析debug.json文件...")
                            debug_json_parsing(json_str)
                        except Exception as e:
                            logger.error(f"读取debug.json时出错: {e}")
                            # 尝试使用不同的编码再次读取
                            encodings = ['utf-8', 'utf-16', 'gbk', 'gb2312', 'iso-8859-1']
                            for encoding in encodings:
                                try:
                                    logger.info(f"尝试使用{encoding}编码读取debug.json...")
                                    with open(debug_json_path, 'r', encoding=encoding) as f:
                                        json_str = f.read()
                                    # 调试JSON解析
                                    debug_json_parsing(json_str)
                                    break
                                except Exception as e2:
                                    logger.error(f"使用{encoding}编码读取debug.json失败: {e2}")
                                    continue
            else:
                # 生成所有章节的大纲
                logger.info(f"开始生成所有章节的大纲...")
                success_count = 0
                
                # 首先尝试加载已有的outline.json，确保我们不会覆盖它
                from models.outline import ChapterOutlineManager
                outline_manager = ChapterOutlineManager.load(total_chapters=generator.total_chapters)
                
                # 如果加载失败，创建一个新的
                if not outline_manager:
                    logger.info("创建新的outline管理器...")
                    outline_manager = ChapterOutlineManager(total_chapters=generator.total_chapters)
                    
                # 将outline管理器赋值给generator
                generator.outline = outline_manager
                
                # 遍历所有章节
                for chapter_num in range(1, generator.total_chapters + 1):
                    logger.info(f"正在生成第{chapter_num}/{generator.total_chapters}章大纲...")
                    
                    # 检查章节大纲是否已存在
                    chapter_outline = outline_manager.get_chapter(chapter_num)
                    if chapter_outline:
                        logger.info(f"第{chapter_num}章大纲已存在，跳过")
                        success_count += 1
                        continue
                    
                    # 生成大纲
                    success = get_outline_directly(generator, chapter_num)
                    
                    if success:
                        logger.info(f"第{chapter_num}章大纲生成成功")
                        success_count += 1
                    else:
                        logger.error(f"第{chapter_num}章大纲生成失败")
                        # 继续尝试生成其他章节，不中断
                    
                    # 为了避免API速率限制，每章之间暂停5秒
                    if chapter_num < generator.total_chapters:
                        import time
                        logger.info("等待5秒后继续...")
                        time.sleep(5)
                
                logger.info(f"所有章节大纲生成完成，成功: {success_count}/{generator.total_chapters}")
                
        else:
            logger.error("找不到必要的项目文件，请确保项目已经初始化")
    except Exception as e:
        logger.error(f"测试生成大纲时出错: {e}")
        logger.error(traceback.format_exc())

def simulate_title_error():
    """
    模拟并调试"\n title"类型的错误
    """
    logger.info("===== 模拟调试JSON中的\\n title错误 =====")
    
    # 创建一个模拟的错误JSON字符串
    problematic_json = '''
    {
      "title": "第1章 觉醒",
      "chapter_summary": {
        "opening": "章节开始的场景描述",
        "development": "情节如何推进",
        "climax": "本章的高潮部分"
      },
      "characters": [
        {
          "name": "主角名字"
        },
        {
          "name": "配角名字"
        }
      ]
      
          "title": "这里有错误的title字段",
      "key_points": [
        "关键点1",
        "关键点2"
      ]
    }
    '''
    
    # 保存模拟的错误JSON到文件
    test_file = os.path.join(OUTPUT_DIR, "test_error.json")
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(problematic_json)
    
    logger.info(f"已创建模拟错误的JSON文件: {test_file}")
    
    # 添加一个更复杂的错误JSON示例
    complex_error_json = '''
    {
        "character_info": {
            "name": "张三", 
            "age": 25
            "occupation": "学生"
        },
        "story_outline": [
            {"chapter": 1, "title": "开始"}
            {"chapter": 2, "title": "发展"}
            {"chapter": 3, "title": "高潮"}
        ]
        
        "settings": {
            "location": "大城市"
            "time": "现代"
        }
    }
    '''
    
    # 保存复杂错误示例
    complex_file = os.path.join(OUTPUT_DIR, "complex_error.json")
    with open(complex_file, 'w', encoding='utf-8') as f:
        f.write(complex_error_json)
    
    logger.info(f"已创建复杂错误的JSON文件: {complex_file}")
    
    # 测试修复函数
    logger.info("\n===== 测试修复基本错误 =====")
    fixed_json = fix_newline_before_title(problematic_json)
    logger.info("修复后的JSON:")
    logger.info(fixed_json)
    
    try:
        data = json.loads(fixed_json)
        logger.info("基本错误修复成功! 能够正确解析JSON")
    except json.JSONDecodeError as e:
        logger.error(f"基本错误修复后仍无法解析: {e}")
    
    # 测试修复复杂错误
    logger.info("\n===== 测试修复复杂错误 =====")
    fixed_complex = fix_newline_before_title(complex_error_json)
    logger.info("修复后的JSON:")
    logger.info(fixed_complex)
    
    try:
        data = json.loads(fixed_complex)
        logger.info("复杂错误修复成功! 能够正确解析JSON")
        return True
    except json.JSONDecodeError as e:
        logger.error(f"复杂错误修复后仍无法解析: {e}")
        return False

def test_fix_outline_json() -> None:
    """
    直接从output/outline.json中读取并尝试修复格式
    """
    logger.info("===== 开始测试从outline.json中修复格式 =====")
    
    # 检查outline.json是否存在
    outline_path = os.path.join(OUTPUT_DIR, "outline.json")
    if not os.path.exists(outline_path):
        logger.error(f"文件不存在: {outline_path}")
        return
    
    try:
        # 读取outline.json
        with open(outline_path, 'r', encoding='utf-8') as f:
            outline_content = f.read()
        
        logger.info(f"成功读取outline.json，长度: {len(outline_content)}字符")
        
        # 应用修复函数
        logger.info("应用JSON修复...")
        fixed_content = fix_newline_before_title(outline_content)
        
        # 保存修复后的JSON文件（仅用于调试）
        fixed_path = os.path.join(OUTPUT_DIR, "fixed_outline.json")
        with open(fixed_path, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        logger.info(f"修复后的JSON已保存到: {fixed_path} (仅用于调试)")
        
        # 尝试解析修复后的JSON
        try:
            json_data = json.loads(fixed_content)
            logger.info("修复后的JSON解析成功")
            
            # 保存格式化后的JSON（仅用于调试）
            formatted_path = os.path.join(OUTPUT_DIR, "formatted_outline.json")
            with open(formatted_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            logger.info(f"格式化后的JSON已保存到: {formatted_path} (仅用于调试)")
            
            # 如果是用于修复的特定文件，将内容更新回outline.json
            try:
                # 检查是否是章节大纲格式的文件
                if isinstance(json_data, dict) and "title" in json_data and "chapter_summary" in json_data:
                    logger.info("检测到章节大纲格式，尝试更新到outline.json...")
                    from models.outline import ChapterOutline, ChapterOutlineManager
                    
                    # 尝试从文件名中提取章节号
                    chapter_number = 1  # 默认为第1章
                    
                    # 从JSON数据中提取标题
                    title = json_data.get("title", f"第{chapter_number}章")
                    
                    # 创建章节大纲对象
                    chapter_outline = ChapterOutline(
                        chapter_number=chapter_number,
                        title=title,
                        content=fixed_content,  # 保存整个内容
                        is_generated=False,
                        is_completed=False
                    )
                    
                    # 如果json_data中有结构化的数据，添加到章节大纲对象
                    if "chapter_summary" in json_data:
                        chapter_outline.chapter_summary = json_data["chapter_summary"]
                    
                    if "key_points" in json_data:
                        chapter_outline.key_points = json_data["key_points"]
                    
                    if "characters" in json_data:
                        chapter_outline.characters = json_data["characters"]
                    
                    if "foreshadowings" in json_data:
                        chapter_outline.foreshadowings = json_data["foreshadowings"]
                    
                    # 尝试读取现有的outline管理器
                    outline_manager = ChapterOutlineManager.load(total_chapters=79)  # 假设总章节数为79
                    
                    if not outline_manager:
                        logger.info("创建新的outline管理器...")
                        outline_manager = ChapterOutlineManager(total_chapters=79)
                    
                    # 添加章节到outline管理器并保存
                    outline_manager.add_chapter(chapter_outline)
                    if outline_manager.save():
                        logger.info(f"成功将修复后的大纲保存到outline.json")
                    else:
                        logger.error("保存到outline.json失败")
                else:
                    logger.info("修复的内容不是标准章节大纲格式，仅作为调试文件保存")
            except Exception as e:
                logger.error(f"更新outline.json时出错: {e}")
                logger.error(traceback.format_exc())
            
            return True
        except json.JSONDecodeError as e:
            logger.error(f"修复后的JSON解析失败: {e}")
            return False
    except Exception as e:
        logger.error(f"测试修复outline.json时出错: {e}")
        logger.error(traceback.format_exc())
        return False

def save_debug_to_outline(chapter_number: int = 2) -> bool:
    """
    将debug.json文件修复并保存到outline.json的chapters字段
    
    Args:
        chapter_number: 章节号
        
    Returns:
        是否成功
    """
    logger.info(f"===== 开始将debug.json修复并保存到outline.json (章节{chapter_number}) =====")
    
    # 检查debug.json是否存在
    debug_path = os.path.join(OUTPUT_DIR, "debug.json")
    if not os.path.exists(debug_path):
        logger.error(f"文件不存在: {debug_path}")
        return False
    
    try:
        # 读取debug.json
        with open(debug_path, 'r', encoding='utf-8') as f:
            outline_content = f.read()
        
        logger.info(f"成功读取debug.json，长度: {len(outline_content)}字符")
        
        # 应用修复函数
        logger.info("应用JSON修复...")
        fixed_content = fix_newline_before_title(outline_content)
        
        # 保存修复后的JSON文件（仅用于调试）
        fixed_path = os.path.join(OUTPUT_DIR, "fixed_debug.json")
        with open(fixed_path, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        logger.info(f"修复后的JSON已保存到: {fixed_path} (仅用于调试)")
        
        # 尝试解析修复后的JSON
        try:
            json_data = json.loads(fixed_content)
            logger.info("修复后的JSON解析成功")
            
            # 保存格式化后的JSON（仅用于调试）
            formatted_path = os.path.join(OUTPUT_DIR, "formatted_debug.json")
            with open(formatted_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            logger.info(f"格式化后的JSON已保存到: {formatted_path} (仅用于调试)")
            
            # 将修复后的内容保存到outline.json
            try:
                logger.info("创建章节大纲对象...")
                from models.outline import ChapterOutline, ChapterOutlineManager
                
                # 从JSON数据中提取标题
                title = json_data.get("title", f"第{chapter_number}章")
                
                # 创建章节大纲对象
                chapter_outline = ChapterOutline(
                    chapter_number=chapter_number,
                    title=title,
                    content=fixed_content,  # 保存整个内容
                    is_generated=False,
                    is_completed=False
                )
                
                # 如果json_data中有结构化的数据，添加到章节大纲对象
                if isinstance(json_data, dict):
                    # 提取章节概要
                    if "chapter_summary" in json_data:
                        chapter_outline.chapter_summary = json_data["chapter_summary"]
                    
                    # 提取关键情节点
                    if "key_points" in json_data:
                        chapter_outline.key_points = json_data["key_points"]
                    
                    # 提取出场人物
                    if "characters" in json_data:
                        chapter_outline.characters = json_data["characters"]
                    
                    # 提取伏笔安排
                    if "foreshadowings" in json_data:
                        chapter_outline.foreshadowings = json_data["foreshadowings"]
                
                # 尝试读取现有的outline管理器
                outline_manager = ChapterOutlineManager.load(total_chapters=79)  # 假设总章节数为79
                
                if not outline_manager:
                    logger.info("创建新的outline管理器...")
                    outline_manager = ChapterOutlineManager(total_chapters=79)
                
                # 添加章节到outline管理器并保存
                outline_manager.add_chapter(chapter_outline)
                if outline_manager.save():
                    logger.info(f"成功将第{chapter_number}章大纲保存到outline.json")
                else:
                    logger.error("保存到outline.json失败")
                    return False
            except Exception as e:
                logger.error(f"创建或保存章节大纲时出错: {e}")
                logger.error(traceback.format_exc())
                return False
            
            return True
        except json.JSONDecodeError as e:
            logger.error(f"修复后的JSON解析失败: {e}")
            return False
    except Exception as e:
        logger.error(f"处理debug.json时出错: {e}")
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='小说大纲生成工具')
    parser.add_argument('--chapter', type=int, default=None, help='要生成的章节号，不指定则生成所有章节')
    parser.add_argument('--debug-file', help='直接分析指定的JSON文件而不生成新内容')
    parser.add_argument('--simulate-error', action='store_true', help='模拟并调试\\n title错误')
    parser.add_argument('--fix-outline', action='store_true', help='修复现有的outline.json文件')
    parser.add_argument('--test-direct', action='store_true', help='直接测试大纲生成（使用NovelGenerator.test_generate_outline_directly）')
    parser.add_argument('--save-debug-to-outline', action='store_true', help='将debug.json文件修复并保存到outline.json的chapters字段中')
    parser.add_argument('--all', action='store_true', help='生成所有章节的大纲')
    
    args = parser.parse_args()
    
    # 将debug.json修复并保存到outline.json
    if args.save_debug_to_outline:
        save_debug_to_outline(args.chapter)
        return
    
    # 修复outline.json文件
    if args.fix_outline:
        test_fix_outline_json()
        return
    
    # 如果要模拟错误
    if args.simulate_error:
        simulate_title_error()
        return
    
    # 直接测试大纲生成
    if args.test_direct:
        logger.info(f"===== 开始直接测试{('第'+str(args.chapter)+'章' if args.chapter is not None else '所有章节')}大纲生成 =====")
        try:
            # 初始化小说生成器
            from novel.generator import NovelGenerator
            generator = NovelGenerator()
            
            # 检查是否有必要的项目文件
            if os.path.exists(os.path.join(OUTPUT_DIR, "background.json")) and \
               os.path.exists(os.path.join(OUTPUT_DIR, "style_guide.txt")):
                
                logger.info("找到必要的项目文件，尝试加载...")
                success = generator.load_project()
                
                if not success:
                    logger.warning("加载项目失败，尝试手动设置必要属性...")
                    
                    # 手动初始化必要的属性
                    generator.genre = "修真"  # 设置一个默认的流派
                    generator.total_chapters = 79  # 设置一个默认的章节总数
                    
                    # 读取风格指南
                    with open(os.path.join(OUTPUT_DIR, "style_guide.txt"), 'r', encoding='utf-8') as f:
                        generator.style_guide = f.read()
                        
                    # 读取背景设定
                    with open(os.path.join(OUTPUT_DIR, "background.json"), 'r', encoding='utf-8') as f:
                        background_data = json.load(f)
                        generator.background = background_data
                
                # 初始化API
                generator.init_apis()
                
                if args.chapter is not None:
                    # 直接使用test_generate_outline_directly测试单一章节
                    success = generator.test_generate_outline_directly(args.chapter)
                    
                    if success:
                        logger.info(f"第{args.chapter}章大纲直接生成成功")
                    else:
                        logger.error(f"第{args.chapter}章大纲直接生成失败")
                else:
                    # 生成所有章节的大纲
                    logger.info("开始生成所有章节的大纲...")
                    success_count = 0
                    
                    for chapter_num in range(1, generator.total_chapters + 1):
                        logger.info(f"正在生成第{chapter_num}/{generator.total_chapters}章大纲...")
                        
                        # 检查章节大纲是否已存在
                        chapter_outline = generator.outline.get_chapter(chapter_num)
                        if chapter_outline:
                            logger.info(f"第{chapter_num}章大纲已存在，跳过")
                            success_count += 1
                            continue
                        
                        # 生成大纲
                        success = generator.test_generate_outline_directly(chapter_num)
                        
                        if success:
                            logger.info(f"第{chapter_num}章大纲生成成功")
                            success_count += 1
                        else:
                            logger.error(f"第{chapter_num}章大纲生成失败")
                            # 继续尝试生成其他章节，不中断
                        
                        # 为了避免API速率限制，每章之间暂停5秒
                        if chapter_num < generator.total_chapters:
                            import time
                            logger.info("等待5秒后继续...")
                            time.sleep(5)
                    
                    logger.info(f"所有章节大纲生成完成，成功: {success_count}/{generator.total_chapters}")
            else:
                logger.error("找不到必要的项目文件，请确保项目已经初始化")
        except Exception as e:
            logger.error(f"直接测试大纲生成时出错: {e}")
            logger.error(traceback.format_exc())
        return
        
    # 如果提供了debug-file参数，直接分析指定文件
    if args.debug_file and os.path.exists(args.debug_file):
        logger.info(f"直接分析文件: {args.debug_file}")
        try:
            with open(args.debug_file, 'r', encoding='utf-8') as f:
                json_str = f.read()
            debug_json_parsing(json_str)
        except Exception as e:
            logger.error(f"读取{args.debug_file}时出错: {e}")
            # 尝试使用不同的编码
            encodings = ['utf-8', 'utf-16', 'gbk', 'gb2312', 'iso-8859-1']
            for encoding in encodings:
                try:
                    logger.info(f"尝试使用{encoding}编码读取{args.debug_file}...")
                    with open(args.debug_file, 'r', encoding=encoding) as f:
                        json_str = f.read()
                    debug_json_parsing(json_str)
                    break
                except Exception as e2:
                    logger.error(f"使用{encoding}编码读取{args.debug_file}失败: {e2}")
                    continue
    else:
        # 如果指定了--all参数或者未指定chapter，则生成所有章节
        chapter_to_generate = None if args.all or args.chapter is None else args.chapter
        test_generate_outline(chapter_to_generate)

if __name__ == "__main__":
    main() 
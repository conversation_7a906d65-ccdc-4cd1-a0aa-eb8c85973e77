"""
JSON处理工具模块，用于修复和安全解析JSON数据
特别处理DeepSeek API返回的JSON结构问题
"""

import json
import re
from typing import Any, Dict, List, Union, Optional
import time

def fix_json(json_str: str, timeout_seconds: int = 5) -> str:
    """
    修复常见的JSON格式问题，采用通用方法
    
    Args:
        json_str: 可能不规范的JSON字符串
        timeout_seconds: 处理超时时间（秒），避免过长处理时间
        
    Returns:
        修复后的JSON字符串
    """
    if not json_str:
        return "{}"
    
    start_time = time.time()
    
    # 预处理：移除注释和多余的空白
    json_str = re.sub(r'//.*?\n', '\n', json_str)
    json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
    
    # 快速修复路径：针对缺少逗号的情况进行快速修复
    # 这是最常见的错误类型，所以我们先尝试只修复这个问题
    fast_fixed = quick_fix_missing_commas(json_str)
    try:
        # 尝试快速修复后的解析
        json.loads(fast_fixed)
        print("快速修复成功！")
        return fast_fixed
    except json.JSONDecodeError as e:
        print(f"快速修复不足，继续详细修复: {str(e)}")
        # 继续详细修复
        
    # 1. 修复引号问题
    # 替换单引号为双引号(除非单引号在双引号内部)
    in_string = False
    new_str = []
    i = 0
    while i < len(json_str):
        char = json_str[i]
        if char == '"' and (i == 0 or json_str[i-1] != '\\'):
            in_string = not in_string
            new_str.append(char)
        elif char == "'" and not in_string:
            new_str.append('"')
        else:
            new_str.append(char)
        i += 1
    json_str = ''.join(new_str)
    
    # 检查是否超时
    if time.time() - start_time > timeout_seconds:
        print(f"修复JSON已超时({timeout_seconds}秒)，返回快速修复结果")
        return fast_fixed
    
    # 2. 修复字符串内未转义引号的问题 (通用方法，增强版)
    try:
        in_string = False
        escaped_str = []
        i = 0
        while i < len(json_str):
            char = json_str[i]
            # 检测字符串开始/结束
            if char == '"' and (i == 0 or json_str[i-1] != '\\'):
                in_string = not in_string
                escaped_str.append(char)
            # 处理字符串内部的引号
            elif in_string and char == '"' and i > 0 and json_str[i-1] != '\\':
                # 字符串内部未转义的引号
                escaped_str.append('\\')
                escaped_str.append(char)
            else:
                escaped_str.append(char)
            i += 1
        json_str = ''.join(escaped_str)
    except Exception as e:
        print(f"引号转义处理出错: {str(e)}")
    
    # 检查是否超时
    if time.time() - start_time > timeout_seconds:
        print(f"修复JSON已超时({timeout_seconds}秒)，返回快速修复结果")
        return fast_fixed
    
    # 3. 修复属性名没有引号的问题
    json_str = re.sub(r'([{,])\s*([a-zA-Z0-9_]+)\s*:', r'\1"\2":', json_str)
    
    # 4. 修复多余的逗号
    json_str = re.sub(r',\s*([}\]])', r'\1', json_str)
    
    # 5. 修复缺少的逗号
    # 5.1 基本修复：在对象/数组元素之间添加逗号
    json_str = re.sub(r'([}\]"])\s*([{["a-zA-Z0-9_])', r'\1,\2', json_str)
    
    # 检查是否超时
    if time.time() - start_time > timeout_seconds:
        print(f"修复JSON已超时({timeout_seconds}秒)，返回快速修复结果")
        return fast_fixed
    
    # 5.2 处理嵌套结构中的逗号问题
    # 进行有限次数的迭代，避免无限循环
    max_iterations = 3
    iteration = 0
    old_json = ""
    
    while old_json != json_str and iteration < max_iterations:
        iteration += 1
        old_json = json_str
        
        # 5.2.1 处理对象内键值对之间缺少逗号
        json_str = re.sub(r'("(?:\\"|[^"])*?"|\d+|true|false|null|\{[^{}]*\}|\[[^\[\]]*\])\s*"', r'\1,"', json_str, flags=re.IGNORECASE)
        
        # 5.2.2 处理数组内元素之间缺少逗号
        json_str = re.sub(r'(("(?:\\"|[^"])*?"|\d+|true|false|null|\{[^{}]*\}|\[[^\[\]]*\]))\s+(("(?:\\"|[^"])*?"|\d+|true|false|null|\{|\[))', r'\1,\3', json_str, flags=re.IGNORECASE)
        
        # 5.3 处理多层嵌套对象字段之间的逗号缺失
        json_str = re.sub(r'"\s*:\s*("[^"]*(?:"|$)|\d+|true|false|null|\{(?:[^{}]*|\{[^{}]*\})*\}|\[(?:[^\[\]]*|\[[^\[\]]*\])*\])\s+"', r'": \1, "', json_str, flags=re.IGNORECASE)
        
        # 检查是否超时
        if time.time() - start_time > timeout_seconds:
            print(f"修复JSON已超时({timeout_seconds}秒)，返回快速修复结果")
            return fast_fixed
    
    # 6. 修复布尔值和null值
    json_str = re.sub(r':\s*True\b', r':true', json_str)
    json_str = re.sub(r':\s*False\b', r':false', json_str)
    json_str = re.sub(r':\s*None\b', r':null', json_str)
    
    # 7. 修复不兼容的Unicode转义序列
    json_str = re.sub(r'\\u([0-9a-fA-F]{2})\\u([0-9a-fA-F]{2})', r'\\u00\1\\u00\2', json_str)
    
    # 8. 修复换行符在字符串中的问题
    json_str = re.sub(r'([^\\])"[\n\r]+', r'\1" ', json_str)
    
    # 9. 尝试处理反斜杠问题 (但要小心处理)
    try:
        # 修复多余的反斜杠，但保留转义字符
        json_str = re.sub(r'\\\\(?=[^"ntrbf/\\])', r'\\', json_str)
    except Exception as e:
        print(f"反斜杠修复出错: {str(e)}")
    
    # 10. 最终清理：确保对象和数组结构完整 (完整性检查)
    try:
        open_brackets = {'{': 0, '[': 0}
        close_brackets = {'}': 0, ']': 0}
        
        for char in json_str:
            if char in open_brackets:
                open_brackets[char] += 1
            elif char in close_brackets:
                close_brackets[char] += 1
        
        # 检查括号是否平衡
        if open_brackets['{'] > close_brackets['}']: 
            json_str += '}' * (open_brackets['{'] - close_brackets['}'])
        if open_brackets['['] > close_brackets[']']:
            json_str += ']' * (open_brackets['['] - close_brackets[']'])
    except Exception as e:
        print(f"括号平衡检查出错: {str(e)}")
    
    # 检查处理总时间
    total_time = time.time() - start_time
    print(f"JSON修复耗时: {total_time:.2f}秒")
    
    return json_str

def quick_fix_missing_commas(json_str: str) -> str:
    """
    快速修复JSON中缺少逗号的问题，主要是针对对象和数组内元素之间的情况
    
    Args:
        json_str: 可能缺少逗号的JSON字符串
        
    Returns:
        修复后的JSON字符串
    """
    # 修复对象内键值对之间缺少的逗号
    # 例如: {"key1":"value1" "key2":"value2"} -> {"key1":"value1", "key2":"value2"}
    fixed = re.sub(r'"\s*:\s*("[^"]*(?:"|$)|\d+|true|false|null|\{(?:[^{}]*|\{[^{}]*\})*\}|\[(?:[^\[\]]*|\[[^\[\]]*\])*\])\s+"', r'": \1, "', json_str)
    
    # 修复数组内元素之间缺少的逗号
    # 例如: [1 2 3] -> [1, 2, 3]
    fixed = re.sub(r'(["}\]\d])\s+([{\["0-9])', r'\1, \2', fixed)
    
    # 修复对象/数组后缺少逗号的情况
    # 例如: {"a":{} "b":{}} -> {"a":{}, "b":{}}
    fixed = re.sub(r'([}\]"])\s*([{["a-zA-Z0-9_])', r'\1, \2', fixed)
    
    return fixed

def extract_nested_json(text: str) -> str:
    """
    从文本中提取嵌套的JSON结构
    
    Args:
        text: 可能包含JSON的文本
        
    Returns:
        提取出的JSON字符串
    """
    # 尝试提取代码块中的JSON
    json_match = re.search(r'```(?:json)?\s*(.*?)\s*```', text, re.DOTALL)
    if json_match:
        return json_match.group(1)
    
    # 尝试提取方括号或花括号包围的JSON
    bracket_match = re.search(r'(\[.*\]|\{.*\})', text, re.DOTALL)
    if bracket_match:
        return bracket_match.group(1)
    
    # 如果没有找到明确的JSON结构，返回原文本
    return text

def manual_parse_json(text: str) -> Union[Dict, List, Any]:
    """
    手动解析简单的JSON结构，用于标准方法失败时
    
    Args:
        text: JSON文本
        
    Returns:
        解析后的对象
    """
    text = text.strip()
    
    # 空对象
    if text == '{}':
        return {}
    
    # 空数组
    if text == '[]':
        return []
    
    # 数字
    if re.match(r'^-?\d+(\.\d+)?$', text):
        try:
            if '.' in text:
                return float(text)
            else:
                return int(text)
        except:
            return text
    
    # 布尔值和null
    if text.lower() == 'true':
        return True
    if text.lower() == 'false':
        return False
    if text.lower() == 'null':
        return None
    
    # 字符串
    if (text.startswith('"') and text.endswith('"')) or (text.startswith("'") and text.endswith("'")):
        return text[1:-1]
    
    # 尝试检测是否是对象或数组
    if text.startswith('{') and text.endswith('}'):
        # 简单对象解析
        result = {}
        content = text[1:-1].strip()
        if not content:
            return {}
            
        # 分割键值对
        pairs = []
        current = ""
        level = 0
        in_string = False
        for char in content + ',':
            if char == '"' and (not current or current[-1] != '\\'):
                in_string = not in_string
            elif not in_string:
                if char == '{' or char == '[':
                    level += 1
                elif char == '}' or char == ']':
                    level -= 1
                elif char == ',' and level == 0:
                    pairs.append(current)
                    current = ""
                    continue
            current += char
        
        # 解析每个键值对
        for pair in pairs:
            if ':' in pair:
                key, value = pair.split(':', 1)
                key = key.strip()
                # 移除键的引号
                if (key.startswith('"') and key.endswith('"')) or (key.startswith("'") and key.endswith("'")):
                    key = key[1:-1]
                    
                result[key] = manual_parse_json(value.strip())
                
        return result
        
    if text.startswith('[') and text.endswith(']'):
        # 简单数组解析
        result = []
        content = text[1:-1].strip()
        if not content:
            return []
            
        # 分割数组元素
        items = []
        current = ""
        level = 0
        in_string = False
        for char in content + ',':
            if char == '"' and (not current or current[-1] != '\\'):
                in_string = not in_string
            elif not in_string:
                if char == '{' or char == '[':
                    level += 1
                elif char == '}' or char == ']':
                    level -= 1
                elif char == ',' and level == 0:
                    items.append(current)
                    current = ""
                    continue
            current += char
            
        # 解析每个元素
        for item in items:
            result.append(manual_parse_json(item.strip()))
            
        return result
        
    # 默认返回文本本身
    return text

def parse_json_safely(json_str: str, data_type: str = "unknown") -> Any:
    """
    安全解析JSON，优先使用大语言模型处理格式问题
    
    Args:
        json_str: JSON字符串
        data_type: 数据类型描述，用于调试信息
        
    Returns:
        解析后的对象，如果解析失败则返回None
    """
    if not json_str:
        print(f"警告：空JSON字符串")
        return None
    
    # 方法1：标准JSON解析
    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        print(f"解析{data_type} JSON - 标准解析失败: {str(e)}")
    
    # 方法2：使用大语言模型修复
    try:
        fixed_json = llm_fix_json(json_str, context=data_type)
        return json.loads(fixed_json)
    except json.JSONDecodeError as e:
        print(f"解析{data_type} JSON - 大语言模型修复后解析仍然失败: {str(e)}")
    
    # 方法3：尝试提取嵌套JSON并使用传统方法修复格式问题
    try:
        extracted = extract_nested_json(json_str)
        fixed = fix_json(extracted)
        return json.loads(fixed)
    except json.JSONDecodeError as e:
        print(f"解析{data_type} JSON - 传统修复后解析失败: {str(e)}")
        
        # 方法3.5：尝试根据错误信息进一步修复 (新增)
        try:
            specific_fixed = fix_json_errors(fixed, str(e))
            return json.loads(specific_fixed)
        except json.JSONDecodeError as e2:
            print(f"解析{data_type} JSON - 针对性修复后仍然失败: {str(e2)}")
    
    # 方法4：尝试手动解析
    try:
        print(f"尝试手动解析{data_type} JSON...")
        return manual_parse_json(json_str)
    except Exception as e:
        print(f"解析{data_type} JSON - 手动解析失败: {str(e)}")
    
    print(f"所有解析方法均失败，无法解析{data_type} JSON")
    return None

def serialize_json(obj: Any, indent: int = 2) -> str:
    """
    安全序列化对象为JSON字符串
    
    Args:
        obj: 要序列化的对象
        indent: 缩进空格数
        
    Returns:
        JSON字符串
    """
    try:
        return json.dumps(obj, ensure_ascii=False, indent=indent)
    except Exception as e:
        print(f"序列化JSON时出错: {str(e)}")
        # 尝试使用更简单的方式
        return str(obj)

def fix_json_errors(json_str: str, error_msg: str) -> str:
    """
    根据特定的JSON错误信息尝试修复问题
    
    Args:
        json_str: JSON字符串
        error_msg: JSON解析错误信息
        
    Returns:
        修复后的JSON字符串
    """
    # 处理缺少逗号分隔符错误
    if "Expecting ',' delimiter" in error_msg:
        # 提取错误位置
        match = re.search(r'line (\d+) column (\d+)', error_msg)
        if match:
            line_num = int(match.group(1))
            col_num = int(match.group(2))
            
            # 将内容分割成行
            lines = json_str.split('\n')
            if 1 <= line_num <= len(lines):
                line = lines[line_num - 1]
                
                # 智能判断是否应该插入逗号
                # 检查错误位置前后的字符
                if col_num > 0 and col_num < len(line):
                    before = line[:col_num]
                    after = line[col_num:]
                    
                    # 检查是否是对象中的字段之间缺少逗号
                    # 常见模式: "field":"value" "nextField":"nextValue"
                    if re.search(r'"\s*:\s*("[^"]*"|[0-9]+|true|false|null|\{[^}]*\}|\[[^\]]*\])\s*"', before + after):
                        fixed_line = line[:col_num] + ',' + line[col_num:]
                        lines[line_num - 1] = fixed_line
                        print(f"在第{line_num}行第{col_num}列插入逗号：对象字段之间缺少逗号")
                    # 常见模式: }{ 或 ][ 或 }"field"
                    elif re.search(r'[}\]]["a-zA-Z{[]', before[-1:] + after[:1]):
                        fixed_line = line[:col_num] + ',' + line[col_num:]
                        lines[line_num - 1] = fixed_line
                        print(f"在第{line_num}行第{col_num}列插入逗号：数组或对象元素之间缺少逗号")
                    else:
                        # 默认在错误位置插入逗号
                        fixed_line = line[:col_num] + ',' + line[col_num:]
                        lines[line_num - 1] = fixed_line
                        print(f"在第{line_num}行第{col_num}列插入逗号：通用修复")
                    
                    return '\n'.join(lines)
    
    # 其他类型的错误处理可以在这里添加
    
    # 如果没有特定处理，返回原始字符串
    return json_str 

def parse_foreshadowing_json(json_str: str, json_type: str) -> Optional[Dict[str, Any]]:
    """
    专门针对伏笔JSON的解析函数，带有额外的错误处理
    
    Args:
        json_str: JSON字符串
        json_type: JSON类型标识（用于日志）
        
    Returns:
        解析成功返回字典，失败返回None
    """
    # 如果字符串为空，返回一个默认的空伏笔结构
    if not json_str or not json_str.strip():
        print(f"解析{json_type} JSON - 输入为空，返回默认结构")
        return {"foreshadowings": []}
    
    # 标准解析
    try:
        data = json.loads(json_str)
        # 验证基本结构
        if not isinstance(data, dict):
            print(f"解析{json_type} JSON - 标准解析成功，但结构无效（不是对象）")
            return {"foreshadowings": []}
            
        # 确保存在foreshadowings字段
        if "foreshadowings" not in data:
            print(f"解析{json_type} JSON - 标准解析成功，但缺少foreshadowings字段")
            data["foreshadowings"] = []
            
        if not isinstance(data["foreshadowings"], list):
            print(f"解析{json_type} JSON - 标准解析成功，但foreshadowings不是数组")
            data["foreshadowings"] = []
            
        return data
    except json.JSONDecodeError as e:
        print(f"解析{json_type} JSON - 标准解析失败: {str(e)}")
    
    # 尝试使用大语言模型修复
    try:
        fixed_json = llm_fix_json(json_str, context=json_type)
        data = json.loads(fixed_json)
        print(f"解析{json_type} JSON - 大语言模型修复后解析成功")
        
        # 验证基本结构
        if not isinstance(data, dict):
            print(f"解析{json_type} JSON - 修复后结构无效（不是对象）")
            return {"foreshadowings": []}
            
        # 确保存在foreshadowings字段
        if "foreshadowings" not in data:
            print(f"解析{json_type} JSON - 修复后缺少foreshadowings字段")
            data["foreshadowings"] = []
            
        if not isinstance(data["foreshadowings"], list):
            print(f"解析{json_type} JSON - 修复后foreshadowings不是数组")
            data["foreshadowings"] = []
            
        return data
    except json.JSONDecodeError as e:
        print(f"解析{json_type} JSON - 大语言模型修复后解析失败: {str(e)}")
    
    # 如果大语言模型修复失败，尝试传统方法
    fixed_json = fix_json(json_str)
    try:
        data = json.loads(fixed_json)
        print(f"解析{json_type} JSON - 传统方法修复后解析成功")
        
        # 验证基本结构
        if not isinstance(data, dict):
            print(f"解析{json_type} JSON - 修复后结构无效（不是对象）")
            return {"foreshadowings": []}
            
        # 确保存在foreshadowings字段
        if "foreshadowings" not in data:
            print(f"解析{json_type} JSON - 修复后缺少foreshadowings字段")
            data["foreshadowings"] = []
            
        if not isinstance(data["foreshadowings"], list):
            print(f"解析{json_type} JSON - 修复后foreshadowings不是数组")
            data["foreshadowings"] = []
            
        return data
    except json.JSONDecodeError as e:
        print(f"解析{json_type} JSON - 修复后解析失败: {str(e)}")
    
    # 如果所有尝试都失败，返回一个默认的空伏笔结构
    print(f"解析{json_type} JSON - 所有解析尝试均失败，返回默认结构")
    return {"foreshadowings": []}

# JSON修复策略优先级
# 1. 优先使用LLM修复 - 通过llm_fix_json函数使用大语言模型修复JSON，最灵活但需要API调用
# 2. 其次使用专门工具 - 如fix_storyline_json等针对特定类型JSON的修复工具
# 3. 最后使用通用方法 - 如fix_json函数通过正则表达式等规则进行修复
# 注意：在使用修复工具时，应该遵循上述优先级，先尝试LLM修复，失败后再尝试代码修复

def llm_fix_json(json_str: str, context: str = "通用JSON", model_api = None) -> str:
    """
    使用大语言模型修复JSON格式问题
    
    Args:
        json_str: 需要修复的JSON字符串
        context: JSON的上下文描述，帮助模型理解数据结构
        model_api: 大语言模型API对象，如果为None，则尝试创建新的API对象
        
    Returns:
        修复后的JSON字符串
    """
    try:
        # 如果未提供API对象，创建一个
        if model_api is None:
            from llm.deepseek_all import DeepSeekAllAPI
            model_api = DeepSeekAllAPI()
        
        # 准备提示词
        prompt = f"""
        请帮我修复以下JSON格式问题。这是一个{context}的JSON数据，但存在格式错误导致无法解析。
        请修复所有格式问题并返回一个有效的、格式正确的JSON。只返回修复后的JSON，不要有任何额外说明。
        
        有问题的JSON:
        ```
        {json_str}
        ```
        
        要求：
        1. 修复所有格式错误，如缺少逗号、引号不匹配、括号不平衡等
        2. 保持原始数据的结构和内容不变
        3. 确保输出是标准有效的JSON格式
        4. 不要添加额外的注释或说明
        5. 直接输出修复后的JSON，不要包含markdown代码块标记
        """
        
        # 调用API修复JSON
        print(f"正在使用大语言模型修复{context} JSON...")
        fixed_json = model_api.generate_content(prompt, temperature=0.1)
        
        if not fixed_json:
            print("大语言模型未返回任何内容，修复失败")
            return json_str
            
        # 尝试解析修复后的JSON，确认有效性
        import json
        try:
            # 提取可能被包裹在代码块中的JSON
            import re
            code_block_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', fixed_json)
            if code_block_match:
                fixed_json = code_block_match.group(1)
                
            # 验证JSON有效性
            json.loads(fixed_json)
            print(f"大语言模型成功修复{context} JSON")
            return fixed_json
        except json.JSONDecodeError as e:
            print(f"大语言模型修复的JSON仍然存在问题: {str(e)}")
            return json_str
            
    except Exception as e:
        print(f"使用大语言模型修复JSON时出错: {str(e)}")
        return json_str 
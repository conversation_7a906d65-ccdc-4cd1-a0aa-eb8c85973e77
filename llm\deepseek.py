"""
DeepSeek API调用模块
"""

import json
import requests
from typing import List, Dict, Any, Optional

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DEEPSEEK_API_URL, DEEPSEEK_API_KEY, DEFAULT_TEMPERATURE, CREATIVE_TEMPERATURE

class DeepSeekAPI:
    """DeepSeek API调用封装"""

    def __init__(self):
        """初始化DeepSeek API调用封装"""
        self.api_url = DEEPSEEK_API_URL
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
        }
        # 禁用代理设置
        self.proxies = {"http": None, "https": None}

    def generate_content(self, prompt: str, temperature: float = DEFAULT_TEMPERATURE, max_tokens: int = 8192, stream: bool = False) -> Optional[str]:
        """
        调用DeepSeek API生成内容

        Args:
            prompt: 提示词
            temperature: 温度参数，控制随机性（默认使用config中的DEFAULT_TEMPERATURE）
            max_tokens: 最大生成token数
            stream: 是否使用流式响应

        Returns:
            生成的内容，如果出错则返回None
        """
        # 从配置文件导入重试次数和超时设置
        from config import MAX_RETRIES, TIMEOUT

        # 打印调试信息
        print(f"正在调用DeepSeek API，URL: {self.api_url}")

        # 确保payload格式与curl命令一致
        payload = {
            "model": "deepseek-ai/DeepSeek-R1-0528",
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": stream
        }

        # 打印请求头和payload（不包含敏感信息）
        print(f"请求头: Content-Type: {self.headers.get('Content-Type')}")
        print(f"Payload: model={payload['model']}, temperature={temperature}, max_tokens={max_tokens}, stream={stream}")

        # 添加重试机制
        for attempt in range(MAX_RETRIES):
            try:
                print(f"尝试调用API (尝试 {attempt+1}/{MAX_RETRIES})...")

                response = requests.post(
                    self.api_url,
                    headers=self.headers,
                    json=payload,
                    proxies=self.proxies,
                    timeout=TIMEOUT  # 使用配置文件中的超时设置
                )

                if response.status_code == 200:
                    if stream:
                        # 处理流式响应
                        return self._handle_stream_response(response)
                    else:
                        # 处理普通响应
                        data = response.json()
                        if 'choices' in data and len(data['choices']) > 0:
                            if 'message' in data['choices'][0] and 'content' in data['choices'][0]['message']:
                                return data['choices'][0]['message']['content']
                        else:
                            print(f"API响应格式异常: {data}")

                print(f"API调用失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")

                # 如果是404错误，提供更详细的错误信息
                if response.status_code == 404:
                    print("404错误可能表示API端点不存在或路径错误，请检查API URL配置")
                    # 404错误通常是配置问题，不需要重试
                    break

                # 如果是超时或连接错误，等待一段时间后重试
                if attempt < MAX_RETRIES - 1:
                    import time
                    wait_time = 5 * (attempt + 1)  # 递增等待时间
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

            except requests.exceptions.Timeout:
                print(f"API调用超时 (尝试 {attempt+1}/{MAX_RETRIES})")
                if attempt < MAX_RETRIES - 1:
                    import time
                    wait_time = 5 * (attempt + 1)
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
            except Exception as e:
                print(f"调用DeepSeek API时出错: {str(e)} (尝试 {attempt+1}/{MAX_RETRIES})")
                if attempt < MAX_RETRIES - 1:
                    import time
                    wait_time = 5 * (attempt + 1)
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

        print(f"在 {MAX_RETRIES} 次尝试后仍未成功，放弃请求")
        return None

    def _handle_stream_response(self, response) -> str:
        """
        处理流式响应

        Args:
            response: 响应对象

        Returns:
            合并后的完整内容
        """
        full_content = ""
        for line in response.iter_lines():
            if line:
                # 移除 "data: " 前缀并解析JSON
                line_text = line.decode('utf-8')
                if line_text.startswith("data: "):
                    json_str = line_text[6:]
                    if json_str.strip() == "[DONE]":
                        break
                    try:
                        chunk = json.loads(json_str)
                        if ('choices' in chunk and len(chunk['choices']) > 0 and
                            'delta' in chunk['choices'][0] and 'content' in chunk['choices'][0]['delta']):
                            content_chunk = chunk['choices'][0]['delta']['content']
                            if content_chunk is not None:  # 检查content_chunk是否为None
                                full_content += content_chunk
                    except json.JSONDecodeError:
                        pass
        return full_content

    def generate_chapter_segment(self, segment_type: str, chapter_outline: str, background: str,
                               characters: str, foreshadowing: str,
                               previous_chapters_summary: Optional[str] = None,
                               previous_segments: Optional[str] = None,
                               target_length: int = 3000) -> Optional[str]:
        """
        生成小说章节的特定段落（开端、发展、高潮、结尾）

        Args:
            segment_type: 段落类型（'opening'、'development'、'climax'、'ending'）
            chapter_outline: 章节大纲
            background: 背景设定
            characters: 人物卡片
            foreshadowing: 伏笔管理
            previous_chapters_summary: 前几章内容摘要
            previous_segments: 本章前面已生成的段落内容
            target_length: 目标段落长度（汉字数）

        Returns:
            段落内容，如果出错则返回None
        """
        # 确定上下文信息
        previous_summary = previous_chapters_summary if previous_chapters_summary else "这是第一章，无前序内容。"
        prev_segments = previous_segments if previous_segments else ""

        # 根据段落类型确定提示词
        segment_descriptions = {
            'opening': "章节开头部分，需要设置场景，引入人物，提出初始冲突",
            'development': "章节发展部分，需要推动情节发展，展现人物行动与反应",
            'climax': "章节高潮部分，需要呈现关键冲突，人物面临挑战与抉择",
            'ending': "章节结尾部分，需要解决当前冲突或铺设悬念，为下一章做铺垫"
        }

        segment_prompts = {
            'opening': """
                请根据大纲创作章节开头部分，需要做到：
                - 设定场景并营造气氛
                - 引入主要人物
                - 建立初始情境或冲突
                - 保持读者兴趣
                - 与前文保持连贯
            """,
            'development': """
                请根据大纲和已有开头创作章节发展部分，需要做到：
                - 推动剧情向前发展
                - 展现人物互动和对话
                - 逐步增加剧情紧张感
                - 埋下可能的伏笔
                - 与已有内容保持连贯
            """,
            'climax': """
                请根据大纲和已有内容创作章节高潮部分，需要做到：
                - 呈现关键冲突或对抗
                - 展现人物面临的重大挑战
                - 可能出现意外转折
                - 情节达到最高潮
                - 与已有内容保持连贯
            """,
            'ending': """
                请根据大纲和已有内容创作章节结尾部分，需要做到：
                - 暂时解决当前冲突
                - 为人物提供短暂休整或成长
                - 可能埋下新的伏笔或悬念
                - 为下一章节做好铺垫
                - 给读者留下期待感
                - 与已有内容保持连贯
            """
        }

        # 获取对应段落类型的提示词
        segment_desc = segment_descriptions.get(segment_type, "章节部分内容")
        segment_detail = segment_prompts.get(segment_type, "")

        # 构建提示词
        context_section = ""
        if prev_segments:
            context_section = f"""
            【已生成的章节内容】
            {prev_segments}

            【接下来要创作的{segment_desc}】
            """

        prompt = f"""
        请根据以下信息，创作一篇男频网络小说章节的{segment_desc}，字数在{target_length}汉字左右：

        【背景设定】
        {background}

        【人物卡片】
        {characters}

        【伏笔管理】
        {foreshadowing}

        【前文概要】
        {previous_summary}

        【本章大纲】
        {chapter_outline}
        {context_section}
        {segment_detail}

        请严格按照大纲内容进行创作，注意以下要点：
        1. 确保内容严格遵循大纲
        2. 保持角色言行与人物设定一致
        3. 按计划埋下和回收伏笔
        4. 保持情节连贯，逻辑合理
        5. 确保文风符合男频网络小说特点，节奏紧凑
        6. 使用恰当的细节描写，增强代入感
        7. 保持段落长度约为{target_length}汉字

        直接输出内容，无需添加章节标题或额外说明。如果这是章节开头部分，则需要包含章节标题。
        """

        # 使用非流式响应避免连接问题
        return self.generate_content(prompt, temperature=CREATIVE_TEMPERATURE, max_tokens=8000, stream=False)

    def generate_chapter(self, chapter_outline: str, background: str, characters: str,
                          foreshadowing: str, previous_chapters_summary: Optional[str] = None,
                          target_length: int = 10000) -> Optional[str]:
        """
        生成小说章节内容

        Args:
            chapter_outline: 章节大纲
            background: 背景设定
            characters: 人物卡片
            foreshadowing: 伏笔管理
            previous_chapters_summary: 前几章内容摘要
            target_length: 目标长度（汉字数）

        Returns:
            章节内容，如果出错则返回None
        """
        previous_summary = previous_chapters_summary if previous_chapters_summary else "这是第一章，无前序内容。"

        prompt = f"""
        请根据以下信息，创作一篇男频网络小说章节，字数在{target_length}汉字左右：

        【背景设定】
        {background}

        【人物卡片】
        {characters}

        【伏笔管理】
        {foreshadowing}

        【前文概要】
        {previous_summary}

        【本章大纲】
        {chapter_outline}

        请严格按照大纲内容进行创作，注意以下要点：
        1. 确保内容严格遵循大纲
        2. 保持角色言行与人物设定一致
        3. 按计划埋下和回收伏笔
        4. 保持情节连贯，逻辑合理
        5. 确保文风符合男频网络小说特点，节奏紧凑
        6. 使用恰当的细节描写，增强代入感
        7. 保持章节长度约为{target_length}汉字
        8. 根据大纲设置合理的章节标题

        直接输出章节内容，无需额外说明。以章节标题开始，正文结束即可。
        """

        # 使用非流式响应避免连接问题
        return self.generate_content(prompt, temperature=CREATIVE_TEMPERATURE, max_tokens=12000, stream=False)
#!/usr/bin/env python
"""
修复generate_from_outline.py中的缩进问题
"""

with open('generate_from_outline.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 替换缩进错误的代码块
bad_code = """            try:
                            # 解析JSON字符串结果
            outline_result = json.loads(outline_result_json)
        except (json.JSONDecodeError, TypeError) as e:
            print(f"解析大纲检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "description": f"无法解析大纲检查结果: {str(e)}",
                    "suggestion": "请重新检查"
                }]
            }"""

good_code = """            try:
                # 解析JSON字符串结果
                outline_result = json.loads(outline_result_json)
            except (json.JSONDecodeError, TypeError) as e:
                print(f"解析大纲检查结果时出错: {str(e)}")
                return {
                    "passed": False,
                    "issues": [{
                        "type": "解析错误",
                        "description": f"无法解析大纲检查结果: {str(e)}",
                        "suggestion": "请重新检查"
                    }]
                }"""

fixed_content = content.replace(bad_code, good_code)

# 保存修复后的文件
with open('generate_from_outline.py', 'w', encoding='utf-8') as f:
    f.write(fixed_content)

print("已修复generate_from_outline.py中的缩进问题") 
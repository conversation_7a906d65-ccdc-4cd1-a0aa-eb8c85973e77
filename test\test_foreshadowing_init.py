"""
测试初始化伏笔管理的JSON解析功能
"""

import json
import os
import sys
from typing import Dict, Any, Optional

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
from utils.json_helper import parse_json_safely
from models.foreshadowing import Foreshadowing, ForeshadowingManager

def test_init_foreshadowing():
    """测试初始化伏笔管理"""
    print("=== 测试初始化伏笔管理 ===")
    
    # 示例JSON数据，模拟从DeepSeek API返回的伏笔管理数据
    foreshadowing_json = """
    {
        "foreshadowings": [
            {
                "id": "f1",
                "type": "主线",
                "content": "主角无意中发现一个神秘的玉佩",
                "plant_chapter": 2,
                "reveal_chapter": 15,
                "importance": "高",
                "plant_method": "在一次意外中被主角捡到",
                "reveal_effect": "揭示玉佩与主角身世的关联",
                "status": "未埋下",
                "related_characters": ["李逍遥", "神秘老者"]
            },
            {
                "id": "f2",
                "type": "角色",
                "content": "女主角背后的家族秘密",
                "plant_chapter": 5,
                "reveal_chapter": 20,
                "importance": "中",
                "plant_method": "女主角偶然流露出对某些秘术的了解",
                "reveal_effect": "揭示女主角家族与仙门的恩怨",
                "status": "未埋下",
                "related_characters": ["林灵儿", "仙门长老"]
            }
        ]
    }
    """
    
    # 使用安全的JSON解析函数
    try:
        foreshadowing_data = parse_json_safely(foreshadowing_json, "foreshadowing")
        if not foreshadowing_data:
            print("× 伏笔JSON解析失败")
            return False
        
        print(f"✓ 伏笔JSON解析成功")
        fs_count = len(foreshadowing_data.get("foreshadowings", []))
        print(f"解析到 {fs_count} 个伏笔")
        
        # 创建伏笔管理器
        manager = ForeshadowingManager()
        for fs_data in foreshadowing_data.get("foreshadowings", []):
            foreshadowing = Foreshadowing(
                id=fs_data.get("id", ""),
                type=fs_data.get("type", ""),
                content=fs_data.get("content", ""),
                plant_chapter=fs_data.get("plant_chapter", 0),
                reveal_chapter=fs_data.get("reveal_chapter", 0),
                importance=fs_data.get("importance", ""),
                plant_method=fs_data.get("plant_method", ""),
                reveal_effect=fs_data.get("reveal_effect", ""),
                status=fs_data.get("status", "未埋下"),
                related_characters=fs_data.get("related_characters", [])
            )
            manager.add_foreshadowing(foreshadowing)
        
        print(f"✓ 成功创建伏笔管理器，包含 {len(manager.foreshadowings)} 个伏笔")
        
        # 转换回JSON格式，验证完整性
        manager_dict = manager.to_dict()
        json_str = json.dumps(manager_dict, ensure_ascii=False, indent=2)
        print(f"伏笔管理器导出为JSON成功")
        print("导出的JSON数据前100个字符:")
        print(json_str[:100] + "...")
        
        # 测试再次解析
        foreshadowing_data2 = parse_json_safely(json_str, "foreshadowing")
        if not foreshadowing_data2:
            print("× 重新解析导出的伏笔JSON失败")
            return False
        
        print(f"✓ 重新解析导出的伏笔JSON成功")
        fs_count2 = len(foreshadowing_data2.get("foreshadowings", []))
        print(f"重新解析到 {fs_count2} 个伏笔")
        
        return True
    except Exception as e:
        print(f"× 测试失败: {str(e)}")
        return False

def test_problematic_json():
    """测试问题JSON解析"""
    print("\n=== 测试问题JSON解析 ===")
    
    # 常见问题JSON格式
    problem_json = """
    { 
        ""foreshadowings"": [ 
            { 
                ""id"": "f1",
                ""type"": "主线",
                ""content"": "主角无意中发现一本古书",
                ""plant_chapter"": 3,
                ""reveal_chapter"": 15,
                ""importance"": "高",
                ""plant_method"": "意外发现",
                ""reveal_effect"": "揭示秘密",
                ""status"": "未埋下",
                ""related_characters"": ["主角", "老者"]
            }
        ] 
    }
    """
    
    # 使用安全的JSON解析函数
    try:
        foreshadowing_data = parse_json_safely(problem_json, "foreshadowing")
        if not foreshadowing_data:
            print("× 问题伏笔JSON解析失败")
            return False
        
        print(f"✓ 问题伏笔JSON解析成功")
        fs_count = len(foreshadowing_data.get("foreshadowings", []))
        print(f"解析到 {fs_count} 个伏笔")
        
        return True
    except Exception as e:
        print(f"× 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("开始测试伏笔初始化功能...\n")
    
    results = []
    
    # 测试正常伏笔初始化
    results.append(test_init_foreshadowing())
    
    # 测试问题JSON解析
    results.append(test_problematic_json())
    
    # 统计结果
    success_count = sum(1 for r in results if r)
    print(f"\n测试结果: {success_count}/{len(results)} 成功")
    
    if success_count == len(results):
        print("✓ 所有测试通过！伏笔初始化功能正常工作")
    else:
        print(f"× {len(results) - success_count} 个测试失败")
    
    return 0 if success_count == len(results) else 1

if __name__ == "__main__":
    sys.exit(main()) 
请根据以下男频网络小说信息，生成一个详细的结构化故事大纲：
【背景设定】

【输出要求】
总章节数：500
你是一位番茄小说平台的人气写手，有丰富的网文创作经验，单月获得稿费100W+。请严格遵守背景设定和风格指南，提供一个完整的故事大纲规划，以JSON格式输出，必须包含以下内容：

    必须严格遵循以下JSON结构：
    ```json
    {{
      "story_title": "小说标题",
      "outlines": [
        {{
          "index": 1,
          "title": "第一章的建议标题",
          "chapter_summary": {
        "opening": "章节开始的场景和初始情况描述",
        "development": "情节如何推进，主要角色互动，关键场景描述",
        "climax": "本章的高潮部分，主要冲突或重要发现描述",
        "ending": "章节如何结束，为下一章做铺垫的描述，必须设置钩子"
      },
      "characters": [
        {
          "name": "人物名称",
          "actions": "此人物在本章的关键行动和变化",
          "emotions": "角色在本章的情感变化",
          "motivation": "角色在本章行动的动机"，
          "firstAppearance":"是否初次登场",
          "background":"背景介绍（仅在firstAppearance为true时有）"，
          "appearance": "外貌描述（仅在firstAppearance为true时有）",
          "personality": "性格特点（仅在firstAppearance为true时有）",
        }
        // 其他出场人物...
      ],
       "scenes": [
        {
          "location": "场景1的地点",
          "participants": ["人物名称1", "人物名称2"],
          "content": "场景1的具体内容描述"
        },
        {
          "location": "场景2的地点",
          "participants": ["人物名称1"],
          "content": "场景2的具体内容描述"
        },
        {
          "location": "场景3的地点",
          "participants": ["人物名称2", "人物名称3"],
          "content": "场景3的具体内容描述"
        }
        // 可以有更多场景...
      ],
      "key_points": [
        "关键情节点1",
        "关键情节点2",
        "关键情节点3"
        // 可以有更多关键点...
      ],
      "foreshadowings": {
        "planted": [
          {
            "id": "伏笔ID（如f1）",
            "content": "伏笔内容",
            "method": "埋下方式的描述"
          }.
        ],
        "revealed": [
          {
            "id": "伏笔ID（如f2）",
            "content": "伏笔内容",
            "effect": "回收效果的描述"
          }
        ]
      }
        }},
        // 第2章到第499章的内容（不要使用省略号，必须完整列出所有章节）
        {{
          "index": 500,
          "title": "最终章的建议标题",
          "chapter_summary": {
        "opening": "章节开始的场景和初始情况描述",
        "development": "情节如何推进，主要角色互动，关键场景描述",
        "climax": "本章的高潮部分，主要冲突或重要发现描述",
        "ending": "章节如何结束，为下一章做铺垫的描述"
      },
      "characters": [
        {
          "name": "人物名称",
          "actions": "此人物在本章的关键行动和变化",
          "emotions": "角色在本章的情感变化",
          "motivation": "角色在本章行动的动机"，
          "firstAppearance":"是否初次登场",
          "background":"背景介绍（仅在firstAppearance为true时有）"，
          "appearance": "外貌描述（仅在firstAppearance为true时有）",
          "personality": "性格特点（仅在firstAppearance为true时有）",
        }
        // 其他出场人物...
      ],
      "scenes": [
        {
          "location": "最终章场景1的地点",
          "participants": ["人物名称A", "人物名称B"],
          "content": "最终章场景1的具体内容描述"
        },
        {
          "location": "最终章场景2的地点",
          "participants": ["人物名称A", "人物名称C"],
          "content": "最终章场景2的具体内容描述"
        }
        // 可以有更多场景...
      ],,
      "key_points": [
        "关键情节点1",
        "关键情节点2",
        "关键情节点3"
        // 可以有更多关键点...
      ],
      "foreshadowings": {
        "planted": [
          {
            "id": "伏笔ID（如f1）",
            "content": "伏笔内容",
            "method": "埋下方式的描述"
          }.
        ],
        "revealed": [
          {
            "id": "伏笔ID（如f2）",
            "content": "伏笔内容",
            "effect": "回收效果的描述"
          }
        ]
      }
        }}
      ]
    }}
    ```

    【重要格式说明】：
    1. index必须是整数类型，不能是字符串，例如应该是"index": 1而不是"index": "1"
    2. outlines数组必须包含从第1章到第500章的所有章节，不能遗漏任何章节
    3. 每一章节内容在5000至7000字之间，需要十分合理的安排大纲的信息量，支撑5000至7000字的文章正文
    4. 严格按照背景设定，一切概念都来自背景设定，不要违背背景设定，也不要出现背景设定中没有的概念，概念第一次被引用时，应该有所介绍
    5. 生成大纲时严格遵守风格指南
    6. 不要有模凌两可的描述、假设和要作者自己定夺的地方，比如：如果......或是......可能......假设......不要让作者在写作时判断，而是做确切的描述，以便指导文章的生成
    7. 保证读完率，读完率是最重要的指标，每一章ending部分必须设置钩子并标记出来，格式为：“钩子：......”
    8. 爆点、爽点必须非常密集，尽量每章都安排爆点，爽点，以带动读者情绪
    9. 注意节奏控制，不要太快推进进度，严格遵守风格指南中的规划
    10.剧情需要为情绪服务，要不断起伏，给读者期待感
    11.关键剧情需要详细描写，可安排多章，可以适当插入支线剧情，时刻记住所有剧情、描写都是为了带动情绪服务
    12.出场人物，即使是只出场一次的配角也需要给名字，不要用一位XX、某个XX等代替
    13.少用逗号、省略号,不能用“他，XXXX，XXXX；他，XXXX，XXXX”这种句式
    【避免问题】
    1.你有剧情进展过快的问题，安排大纲内容时需要反思，将目前安排的单章内容拆分成多章描写，或者补充适当的支线情节
    2.你曾经多次犯错，让不该出现的角色出场。编写每一章大纲前，每次都要确认角色的状态，不该出现的角色（如已经阵亡，已离队，地理位置不符等）不能出现在剧情中
    3.你曾多次使用已经消耗掉的，或是不存在的物品。编写每一章大纲前，每次都要确认可用物品的状态，不能出现未知来源，或是已经消耗掉的物品
    4.大纲chapter_summary编写时禁止使用括号，当你想要使用括号时，你想要写的内容绝对会有逻辑错误，应当重新规划成不需要括号也能正确表达的内容
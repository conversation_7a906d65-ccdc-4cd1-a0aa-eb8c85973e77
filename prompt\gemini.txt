请根据以下男频网络小说信息，生成一个详细的结构化故事主线：

【背景设定】
{
  "content": "这是一个充满魔法与剑的西方魔幻世界，主角作为旧神代最后幸存者，拥有满级实力却低调行事。世界由人类王国、精灵森林、龙族山脉等多元势力构成，隐藏着神格碎片争夺的暗流。力量体系分为九阶职业等级，主角超阶存在。故事从主角解决地区危机开始，逐步揭露上古神战秘辛，最终对抗深渊入侵。风格热血高燃，穿插轻幽默与群像高光，满足爽文核心需求。",
  "categories": {
    "world_view": "世界名为『艾尔瑞亚』，处于第三纪元的魔法复兴时代。千年前的神魔战争导致旧神陨落，新神体系建立，但神格碎片散落世界各地。魔法与武技并存，龙语魔法和精灵秘术被视为顶级力量。大陆由三大势力主导：人类的光明教廷、精灵的永夜议会、龙族的苍穹之翼，暗处还有深渊势力渗透。世界运行遵循『魔力潮汐』规律，每百年爆发一次元素暴动。",
    "geography": "主大陆『奥莱克斯』呈新月形，西侧是终年积雪的冰封山脉，东侧为精灵居住的翡翠林海，中部是人类七王国平原。特殊地标包括：1) 通天塔遗址（旧神实验室） 2) 沸腾之海（海底火山群） 3) 叹息峡谷（神战剑气所劈）。气候受魔法影响，南方常年雨季，北方冬季会出现极光魔法阵。每月15日的『双月重合』现象会增强所有魔法效果300%。",
    "countries": "1) 圣罗兰帝国：教廷直属，骑士文化盛行 2) 自由城邦联盟：商业中心，黑市横行 3) 翡翠王朝（精灵）：魔法禁地，排外 4) 龙脊荒原（龙族）：空中浮岛群 5) 深渊裂谷：恶魔领地。七王国通过『白银盟约』维持表面和平，实则暗斗。圣罗兰皇帝与教廷教皇权力制衡，精灵女王实行长老议会制。",
    "organizations": "1) 光明教廷（伪善神代言人） 2) 暗影兄弟会（刺客组织） 3) 魔导士协会（研究禁忌魔法） 4) 佣兵公会（主角伪装身份处） 5) 永夜议会（精灵高层） 6) 龙之冠（龙族长老会） 7) 真理之眼（考古组织） 8) 血色蔷薇（女巫团体） 9) 铁盾骑士团（帝国精锐） 10) 深渊教派（最终反派）。各组织通过『神格碎片』线索展开明争暗斗。",
    "items": "1) 诅咒金币（主角封印器） 2) 弑神剑格拉墨 3) 精灵圣弓希尔文 4) 龙晶法杖奥姆 5) 光明圣典（伪造版） 6) 恶魔契约书 7) 空间戒指『虚无之匣』 8) 时间沙漏『永恒碎片』 9) 抗魔铠甲『泰坦之肤』 10) 幻影斗篷 11) 禁咒卷轴『末日审判』 12) 生命泉水（精灵至宝） 13) 龙血药剂 14) 神格碎片（共7块） 15) 魔导炮『星辰碎裂者』 16) 灵魂绑定匕首 17) 预言水晶球 18) 元素之心（火/水/风/土） 19) 圣骑士徽章（可变形） 20) 恶魔角（炼金材料） 21) 天使羽毛 22) 贤者之石 23) 兽王号角 24) 荆棘王冠 25) 不死鸟之羽 26) 海妖之歌（乐器） 27) 巨人指骨 28) 阴影面具 29) 太阳护符 30) 月亮吊坠。所有神器分SSS到D级，主角随身物品多为SSS级伪装成废铁。",
    "cultivation": "力量分九阶：见习（1-3）-精英（4-6）-传奇（7-9）-超阶（主角）。职业有：魔剑士（主角）、元素使、圣骑士、暗杀者等。修炼方式包括：1) 魔力冥想 2) 武技锤炼 3) 血脉觉醒 4) 神器共鸣。技能体系含：基础技（火球术）、进阶技（烈焰风暴）、禁咒（陨星天降）。主角拥有『神代魔法』特权，可无视吟唱与冷却。特殊能力『全元素亲和』『绝对暴击』。",
    "races": "1) 人类（数量优势但天赋普通） 2) 精灵（魔法天才但生育困难） 3) 龙族（物理巅峰却傲慢） 4) 矮人（锻造专精） 5) 兽人（狂暴化） 6) 血族（夜行） 7) 人鱼（海战无敌） 8) 翼人（空战） 9) 恶魔（深渊种） 10) 远古巨人（濒临灭绝）。龙族与精灵世仇，人类内部歧视亚人。主角真实身份为『神血混种』（旧神+龙族）。",
    "history": "1) 创世战争（古神分裂大陆） 2) 精灵大迁徙（躲避人类扩张） 3) 龙陨之战（屠龙事件） 4) 光明教廷成立（篡改历史） 5) 七日灾变（深渊首次入侵） 6) 神格碎裂（主角参与的秘密战争） 7) 白银盟约签订（人类停战协议） 8) 魔导革命（魔法科技兴起） 9) 永夜之乱（精灵内战） 10) 预言『双月重合时，旧神归来日』。所有事件背后均有神格碎片线索。",
    "culture": "人类崇尚骑士精神与教会礼仪，精灵遵循自然之道，龙族以力量为尊。通用节日：1) 圣光祭（伪神诞辰） 2) 龙吼日（龙族比武） 3) 新月节（精灵祈福）。禁忌包括：对精灵提及砍伐生命树、对人类质疑教廷权威、对龙族嘲笑体型。特色风俗：决斗文化、魔法契约精神、探险者行酒令。主角常破坏规矩引发笑料。",
    "rules": "1) 魔法守恒定律（施法消耗等价生命力） 2) 血脉压制（高阶种族威压） 3) 神器认主规则 4) 深渊侵蚀不可逆 5) 神格碎片唯一绑定 6) 禁咒需支付代价（主角除外） 7) 比武决斗致死无责 8) 黑市交易守秘原则 9) 龙族不得干预人族内战（盟约限制） 10) 精灵森林禁飞令。主角常以『实力即规则』打破常规。"
  }
}
【输出要求】
        总章节数：100

        请提供一个完整的故事主线规划，以JSON格式输出，必须包含以下内容：

        必须严格遵循以下JSON结构：
        ```json
        {{
          "story_title": "小说标题",
          "outlines": [
            {{
              "index": 1,
              "title": "第一章的建议标题",
              "chapter_summary": {
            "opening": "章节开始的场景和初始情况描述",
            "development": "情节如何推进，主要角色互动，关键场景描述",
            "climax": "本章的高潮部分，主要冲突或重要发现描述",
            "ending": "章节如何结束，为下一章做铺垫的描述"
          },
          "characters": [
            {
              "name": "人物名称",
              "actions": "此人物在本章的关键行动和变化",
              "emotions": "角色在本章的情感变化",
              "firstAppearance":"是否初次登场"
            }
            // 其他出场人物...
          ],
          "scenes": [
            "场景描述1",
            "场景描述2",
            "场景描述3",
            // 可以有更多场景...
          ],
          "key_points": [
            "关键情节点1",
            "关键情节点2",
            "关键情节点3"
            // 可以有更多关键点...
          ],
          "foreshadowings": {
            "planted": [
              {
                "id": "伏笔ID（如f1）",
                "content": "伏笔内容",
                "method": "埋下方式的描述"
              }.
            ],
            "revealed": [
              {
                "id": "伏笔ID（如f2）",
                "content": "伏笔内容",
                "effect": "回收效果的描述"
              }
            ]
          }
            }},
            // 第2章到第99章的内容（不要使用省略号，必须完整列出所有章节）
            {{
              "index": 100,
              "title": "最终章的建议标题",
                 "chapter_summary": {
            "opening": "章节开始的场景和初始情况描述",
            "development": "情节如何推进，主要角色互动，关键场景描述",
            "climax": "本章的高潮部分，主要冲突或重要发现描述",
            "ending": "章节如何结束，为下一章做铺垫的描述"
          },
          "characters": [
            {
              "name": "人物名称",
              "actions": "此人物在本章的关键行动和变化",
              "emotions": "角色在本章的情感变化",
              "firstAppearance":"是否初次登场"
            }
            // 其他出场人物...
          ],
          "scenes": [
            "场景描述1",
            "场景描述2",
            "场景描述3",
            // 可以有更多场景...
          ],
          "key_points": [
            "关键情节点1",
            "关键情节点2",
            "关键情节点3"
            // 可以有更多关键点...
          ],
          "foreshadowings": {
            "planted": [
              {
                "id": "伏笔ID（如f1）",
                "content": "伏笔内容",
                "method": "埋下方式的描述"
              }.
            ],
            "revealed": [
              {
                "id": "伏笔ID（如f2）",
                "content": "伏笔内容",
                "effect": "回收效果的描述"
              }
            ]
          }
            }}
          ]
        }}
        ```

        【重要格式说明】：
        1. index必须是整数类型，不能是字符串，例如应该是"index": 1而不是"index": "1"
        2. outlines数组必须包含从第1章到第100章的所有章节，不能遗漏任何章节
        3. 严格确保生成确切的100章，不能多也不能少
        4. 严格按照背景设定，一切概念都来自背景设定，不要违背背景设定，也不要出现背景设定中没有的概念
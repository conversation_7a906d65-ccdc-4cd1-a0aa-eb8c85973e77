"""
背景设定模型
"""

import json
import os
from typing import Dict, Any, Optional

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import OUTPUT_DIR

class Background:
    """小说背景设定模型"""

    def __init__(self, genre: str, content: str = None, categories: Dict[str, str] = None):
        """
        初始化背景设定模型

        Args:
            genre: 小说流派
            content: 背景设定内容
            categories: 分类背景设定，包括世界观、地理、国家、组织、物品等
        """
        self.genre = genre
        self.content = content or ""
        self.categories = categories or {
            "world_view": "",  # 世界观
            "geography": "",   # 地理环境
            "countries": "",   # 国家/区域
            "organizations": "", # 组织/势力
            "items": "",       # 重要物品/装备
            "cultivation": "", # 修炼体系
            "races": "",       # 种族设定
            "history": "",     # 历史背景
            "culture": "",     # 文化习俗
            "rules": ""        # 世界规则/法则
        }
        self.file_path = os.path.join(OUTPUT_DIR, "background.json")

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            字典格式的背景设定
        """
        return {
            "genre": self.genre,
            "content": self.content,
            "categories": self.categories
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Background':
        """
        从字典创建背景设定模型

        Args:
            data: 字典格式的背景设定

        Returns:
            背景设定模型实例
        """
        return cls(
            genre=data.get("genre", ""),
            content=data.get("content", ""),
            categories=data.get("categories", {})
        )

    def save(self) -> bool:
        """
        保存背景设定到文件

        Returns:
            保存是否成功
        """
        try:
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存背景设定时出错: {str(e)}")
            return False

    @classmethod
    def load(cls, file_path: Optional[str] = None) -> Optional['Background']:
        """
        从文件加载背景设定

        Args:
            file_path: 文件路径，如果为None则使用默认路径

        Returns:
            背景设定模型实例，如果加载失败则返回None
        """
        try:
            file_path = file_path or os.path.join(OUTPUT_DIR, "background.json")
            if not os.path.exists(file_path):
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            return cls.from_dict(data)
        except Exception as e:
            print(f"加载背景设定时出错: {str(e)}")
            return None

    def __str__(self) -> str:
        """
        字符串表示

        Returns:
            背景设定的字符串表示
        """
        return self.content
#!/usr/bin/env python3
"""
测试优化后的JSON修复功能
"""

import os
import sys
import json
import time

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.json_helper import fix_json, quick_fix_missing_commas
from utils.fix_storyline import fix_storyline_json

def test_quick_fix():
    """测试快速修复功能"""
    print("\n===== 测试快速修复功能 =====")
    
    # 测试用例：缺少逗号的简单JSON
    test_json = '{"field1": "value1" "field2": "value2" "field3": "value3"}'
    
    # 测量处理时间
    start_time = time.time()
    fixed_json = quick_fix_missing_commas(test_json)
    quick_time = time.time() - start_time
    
    print(f"原始JSON: {test_json}")
    print(f"快速修复: {fixed_json}")
    print(f"快速修复耗时: {quick_time:.6f}秒")
    
    # 检查是否可以解析
    try:
        parsed = json.loads(fixed_json)
        print(f"解析结果: {parsed}")
        print("快速修复成功!")
    except json.JSONDecodeError as e:
        print(f"快速修复后仍然解析失败: {str(e)}")

def test_fix_json_with_timeout():
    """测试带超时的修复功能"""
    print("\n===== 测试带超时的修复功能 =====")
    
    # 生成一个大型的复杂JSON用于测试
    complex_json = '{'
    for i in range(100):
        # 故意加入缺少逗号的错误
        if i % 5 == 0:
            complex_json += f'"field{i}": {{"nested": "value{i}" "more": ["item1" "item2" "item3"]}}'
        else:
            complex_json += f'"field{i}": {{"nested": "value{i}", "more": ["item1", "item2", "item3"]}},'
    complex_json += '}'
    
    # 测试不同超时时间
    for timeout in [0.1, 0.5, 1.0, 2.0]:
        start_time = time.time()
        fixed_json = fix_json(complex_json, timeout_seconds=timeout)
        total_time = time.time() - start_time
        
        print(f"\n使用超时时间 {timeout}秒:")
        print(f"修复耗时: {total_time:.6f}秒")
        
        # 检查是否可以解析
        try:
            json.loads(fixed_json)
            print("修复后可以成功解析!")
        except json.JSONDecodeError as e:
            print(f"修复后仍然解析失败: {str(e)}")

def test_fix_storyline():
    """测试故事主线修复功能"""
    print("\n===== 测试故事主线修复功能 =====")
    
    # 检查是否存在main_storyline.json
    file_path = os.path.join("output", "main_storyline.json")
    if os.path.exists(file_path):
        print(f"发现main_storyline.json文件，尝试修复")
        
        # 读取文件内容
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        file_size = len(content)
        print(f"文件大小: {file_size} 字节")
        
        # 尝试直接解析
        try:
            json.loads(content)
            print("文件可以直接解析，不需要修复")
        except json.JSONDecodeError as e:
            print(f"解析错误: {str(e)}")
            
            # 使用fix_storyline_json进行修复
            start_time = time.time()
            result = fix_storyline_json()
            total_time = time.time() - start_time
            
            print(f"修复耗时: {total_time:.6f}秒")
            print(f"修复结果: {'成功' if result else '失败'}")
    else:
        print(f"未找到main_storyline.json文件")

if __name__ == "__main__":
    test_quick_fix()
    test_fix_json_with_timeout()
    test_fix_storyline() 
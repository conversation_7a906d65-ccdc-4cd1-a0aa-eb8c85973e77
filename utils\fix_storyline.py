"""
专门用于修复和解析main_storyline.json文件的工具
"""

import json
import os
import re
import sys
from typing import Dict, Any, List, Optional, Union

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import OUTPUT_DIR

def fix_storyline_file():
    """
    读取main_storyline.json文件并修复格式问题
    
    Returns:
        Dict: 修复后的故事主线数据
    """
    file_path = os.path.join(OUTPUT_DIR, "main_storyline.json")
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return None
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复并解析
        fixed_content = fix_storyline_json(content)
        
        # 将修复后的数据写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(fixed_content, f, ensure_ascii=False, indent=2)
        
        return fixed_content
    except Exception as e:
        print(f"修复main_storyline.json失败: {e}")
        return None

def fix_storyline_json(json_str: str) -> Dict[str, Any]:
    """
    修复main_storyline.json内容的格式问题
    
    Args:
        json_str: JSON字符串
    
    Returns:
        Dict: 修复后的JSON数据
    """
    # 如果输入为空，返回空对象
    if not json_str:
        return {}
    
    # 1. 预处理 - 修复常见格式问题
    # 1.1 修复引号问题
    json_str = _preprocess_quotes(json_str)
    
    # 1.2 修复特殊符号和转义字符
    json_str = _preprocess_special_chars(json_str)
    
    # 2. 尝试直接解析
    try:
        result = json.loads(json_str)
        print("直接解析成功")
        return result
    except json.JSONDecodeError as e:
        print(f"直接解析失败: {e}")
    
    # 3. 应用强化修复
    json_str = _apply_advanced_fixes(json_str)
    
    # 4. 再次尝试解析
    try:
        result = json.loads(json_str)
        print("强化修复后解析成功")
        return result
    except json.JSONDecodeError as e:
        print(f"强化修复后解析仍然失败: {e}")
    
    # 5. 使用结构化提取
    result = _extract_structured_data(json_str)
    if result and result.get("outlines"):
        print("使用结构化提取成功")
        return result
    
    # 6. 使用直接正则提取方法
    result = _regex_extract(json_str)
    if result and result.get("outlines"):
        print("使用正则表达式直接提取成功")
        return result
    
    # 7. 如果所有方法都失败，但我们至少提取了一些章节，那么使用它们
    if result and result.get("story_title"):
        # 确保有outlines字段
        if "outlines" not in result:
            result["outlines"] = []
        print(f"部分提取成功，故事标题: {result['story_title']}")
        return result
    
    # 所有方法都失败，返回空对象
    print("所有修复方法都失败")
    return {"story_title": "未命名故事", "outlines": []}

def _preprocess_quotes(json_str: str) -> str:
    """
    处理引号相关的问题
    """
    # 替换中文引号为英文引号
    json_str = json_str.replace('"', '"').replace('"', '"')
    json_str = json_str.replace(''', "'").replace(''', "'")
    
    # 替换单引号为双引号(除非单引号在双引号内部)
    in_string = False
    new_str = []
    i = 0
    while i < len(json_str):
        char = json_str[i]
        if char == '"' and (i == 0 or json_str[i-1] != '\\'):
            in_string = not in_string
            new_str.append(char)
        elif char == "'" and not in_string:
            new_str.append('"')
        else:
            new_str.append(char)
        i += 1
    
    return ''.join(new_str)

def _preprocess_special_chars(json_str: str) -> str:
    """
    处理特殊字符和转义字符
    """
    # 修复已知的特殊字符问题
    # 修复"天道枷锁"等文本中的特殊字符
    json_str = re.sub(r'(["\']["\']),', r'\1",', json_str)
    json_str = re.sub(r',"(["\']["\'])', r',""\1', json_str)
    
    # 修复控制字符
    json_str = re.sub(r'[\x00-\x1F\x7F]', '', json_str)
    
    # 确保正确的UTF-8编码
    return json_str

def _apply_advanced_fixes(json_str: str) -> str:
    """
    应用高级修复技术
    """
    # 修复逗号问题
    json_str = re.sub(r',\s*([}\]])', r'\1', json_str)  # 移除末尾逗号
    json_str = re.sub(r'([}\]"])\s*([{["a-zA-Z0-9_])', r'\1,\2', json_str)  # 添加缺失的逗号
    
    # 修复属性名没有引号的问题
    json_str = re.sub(r'([{,])\s*([a-zA-Z0-9_]+)\s*:', r'\1"\2":', json_str)
    
    # 修复括号匹配问题
    open_brackets = json_str.count('{') - json_str.count('}')
    if open_brackets > 0:
        json_str += '}' * open_brackets
    
    open_squares = json_str.count('[') - json_str.count(']')
    if open_squares > 0:
        json_str += ']' * open_squares
    
    return json_str

def _extract_structured_data(json_str: str) -> Optional[Dict[str, Any]]:
    """
    结构化提取故事主线数据
    """
    try:
        # 尝试提取story_title
        title_match = re.search(r'"story_title"\s*:\s*"([^"]*)"', json_str)
        title = title_match.group(1) if title_match else "未命名故事"
        
        # 提取outlines/chapter_outlines
        result = {"story_title": title, "outlines": []}
        
        # 从JSON对象中提取章节，处理嵌套的大括号
        # 匹配整个大括号包围的对象，处理嵌套情况
        pattern = r'{([^{}]|(?R))*}'
        
        # 如果正则引擎不支持(?R)递归模式，使用替代方案
        try:
            chapter_matches = list(re.finditer(pattern, json_str))
        except re.error:
            # 替代方案：使用简单的大括号匹配，不处理嵌套
            simple_pattern = r'{[^{}]*}'
            chapter_matches = list(re.finditer(simple_pattern, json_str))
        
        # 提取所有看起来像章节的对象
        chapter_objects = []
        
        # 从字符串中查找章节对象
        for line in json_str.split('\n'):
            if '"index"' in line and '"title"' in line:
                # 找到可能是章节开始的行
                start_idx = json_str.find(line)
                if start_idx >= 0:
                    # 从这一行开始，向后查找整个章节对象
                    brace_count = 0
                    end_idx = start_idx
                    chapter_text = ""
                    
                    for i in range(start_idx, len(json_str)):
                        if json_str[i] == '{':
                            brace_count += 1
                            if brace_count == 1:
                                # 找到对象开始
                                chapter_text = "{"
                        elif json_str[i] == '}':
                            brace_count -= 1
                            chapter_text += "}"
                            if brace_count == 0:
                                # 找到对象结束
                                end_idx = i + 1
                                break
                        elif brace_count > 0:
                            # 在对象内，收集文本
                            chapter_text += json_str[i]
                    
                    if brace_count == 0 and chapter_text:
                        # 找到完整对象
                        chapter_objects.append(chapter_text)
        
        # 如果没有找到章节对象，尝试直接提取关键字段
        if not chapter_objects:
            # 查找所有index、title和content
            index_matches = re.finditer(r'"index"\s*:\s*(\d+)', json_str)
            title_matches = re.finditer(r'"title"\s*:\s*"([^"]*)"', json_str)
            content_matches = re.finditer(r'"content"\s*:\s*"([^"]*)"', json_str)
            
            # 收集所有字段值
            indices = [int(m.group(1)) for m in index_matches]
            titles = [m.group(1) for m in title_matches]
            contents = [m.group(1) for m in content_matches]
            
            # 基于顺序创建章节
            max_len = max(len(indices), len(titles), len(contents))
            for i in range(max_len):
                chapter = {}
                if i < len(indices):
                    chapter["index"] = indices[i]
                if i < len(titles):
                    chapter["title"] = titles[i]
                if i < len(contents):
                    chapter["content"] = contents[i]
                
                if chapter:
                    result["outlines"].append(chapter)
            
            return result
                
        # 处理找到的章节对象
        for obj_text in chapter_objects:
            try:
                # 尝试解析为JSON对象
                # 首先修复明显的格式问题
                fixed_text = obj_text
                fixed_text = re.sub(r'"\s*([a-zA-Z0-9_]+)\s*":', r'"\1":', fixed_text)  # 修复键名格式
                fixed_text = re.sub(r'"\s*,', '",', fixed_text)  # 修复引号后的逗号
                fixed_text = re.sub(r',\s*}', '}', fixed_text)  # 移除结尾逗号
                
                # 尝试解析
                try:
                    chapter_obj = json.loads(fixed_text)
                    if isinstance(chapter_obj, dict):
                        result["outlines"].append(chapter_obj)
                except json.JSONDecodeError:
                    # 无法解析，使用正则提取关键字段
                    index_match = re.search(r'"index"\s*:\s*(\d+)', fixed_text)
                    title_match = re.search(r'"title"\s*:\s*"([^"]*)"', fixed_text)
                    content_match = re.search(r'"content"\s*:\s*"([^"]*)"', fixed_text)
                    
                    chapter = {}
                    if index_match:
                        try:
                            chapter["index"] = int(index_match.group(1))
                        except:
                            pass
                    if title_match:
                        chapter["title"] = title_match.group(1)
                    if content_match:
                        chapter["content"] = content_match.group(1)
                    
                    if chapter:
                        result["outlines"].append(chapter)
            except Exception as e:
                print(f"处理章节对象时出错: {e}")
        
        # 处理大括号包裹的完整对象
        outlines_matches = re.finditer(r'"(outlines|chapter_outlines)"\s*:\s*\[(.*?)\]', json_str, re.DOTALL)
        for match in outlines_matches:
            field_name = match.group(1)
            outlines_content = match.group(2)
            
            # 提取每个outline项
            try:
                # 尝试提取大括号包围的对象
                outlines = []
                outline_pattern = r'{([^{}]*(?:{[^{}]*}[^{}]*)*?)}'
                
                for outline_match in re.finditer(outline_pattern, outlines_content):
                    outline_text = '{' + outline_match.group(1) + '}'
                    try:
                        # 尝试解析单个outline
                        outline_text = re.sub(r'"\s*,', '",', outline_text)  # 修复引号后的逗号
                        outline_text = re.sub(r',\s*}', '}', outline_text)  # 移除结尾逗号
                        outline = json.loads(outline_text)
                        outlines.append(outline)
                    except json.JSONDecodeError:
                        # 如果解析失败，使用正则表达式提取关键字段
                        index_match = re.search(r'"index"\s*:\s*(\d+)', outline_text)
                        title_match = re.search(r'"title"\s*:\s*"([^"]*)"', outline_text)
                        content_match = re.search(r'"content"\s*:\s*"([^"]*)"', outline_text)
                        
                        outline = {}
                        if index_match:
                            try:
                                outline["index"] = int(index_match.group(1))
                            except:
                                pass
                        if title_match:
                            outline["title"] = title_match.group(1)
                        if content_match:
                            outline["content"] = content_match.group(1)
                        
                        if outline:
                            outlines.append(outline)
                
                if outlines:
                    result[field_name] = outlines
            except Exception as e:
                print(f"提取outlines时出错: {e}")
        
        # 合并章节对象和outlines字段提取的内容
        if result.get("chapter_outlines") and not result.get("outlines"):
            result["outlines"] = result["chapter_outlines"]
            del result["chapter_outlines"]
        
        # 如果outlines为空，但我们有单独解析的章节
        if not result.get("outlines") and chapter_objects:
            # 已经添加到result["outlines"]中了
            pass
            
        # 确保章节顺序是正确的
        if result.get("outlines"):
            result["outlines"] = sorted(result["outlines"], 
                                         key=lambda x: x.get("index", x.get("chapter_number", 999)) 
                                            if isinstance(x, dict) else 999)
        
        return result
        
    except Exception as e:
        print(f"结构化提取失败: {e}")
        return None

def _regex_extract(json_str: str) -> Optional[Dict[str, Any]]:
    """
    使用正则表达式直接提取内容，最后的尝试方法
    """
    try:
        # 构建基本结构
        result = {"story_title": "未命名故事", "outlines": []}
        
        # 提取标题
        title_match = re.search(r'"story_title"\s*:\s*"([^"]*)"', json_str)
        if title_match:
            result["story_title"] = title_match.group(1)
        
        # 提取章节序号
        index_matches = list(re.finditer(r'"index"\s*:\s*(\d+)', json_str))
        print(f"正则提取: 找到 {len(index_matches)} 个章节序号")
        
        # 提取章节标题
        title_matches = list(re.finditer(r'"title"\s*:\s*"([^"]*)"', json_str))
        print(f"正则提取: 找到 {len(title_matches)} 个章节标题")
        
        # 提取章节内容
        content_matches = list(re.finditer(r'"content"\s*:\s*"([^"]*)"', json_str))
        print(f"正则提取: 找到 {len(content_matches)} 个章节内容")
        
        # 确保所有列表长度一致，只保留最短长度
        min_len = min(len(index_matches), len(title_matches), len(content_matches))
        
        # 合并结果
        for i in range(min_len):
            try:
                chapter = {
                    "index": int(index_matches[i].group(1)),
                    "title": title_matches[i].group(1),
                    "content": content_matches[i].group(1)
                }
                result["outlines"].append(chapter)
            except Exception as e:
                print(f"处理第{i+1}个章节时出错: {e}")
        
        print(f"正则提取: 成功提取 {len(result['outlines'])} 个完整章节")
        
        # 如果提取成功，返回结果
        if result["outlines"]:
            return result
            
        # 如果没有找到章节，尝试另一种方法: 直接提取带有索引和内容的章节块
        try:
            print("尝试替代提取方法...")
            block_pattern = r'"index"\s*:\s*(\d+)[^}]*"title"\s*:\s*"([^"]*)"[^}]*"content"\s*:\s*"([^"]*)"'
            block_matches = list(re.finditer(block_pattern, json_str, re.DOTALL))
            
            if block_matches:
                result["outlines"] = []
                for match in block_matches:
                    try:
                        chapter = {
                            "index": int(match.group(1)),
                            "title": match.group(2),
                            "content": match.group(3)
                        }
                        result["outlines"].append(chapter)
                    except Exception as e:
                        print(f"处理章节块时出错: {e}")
                
                print(f"替代方法: 提取了 {len(result['outlines'])} 个章节")
                return result
        except Exception as e:
            print(f"替代提取方法失败: {e}")
        
        return None
    
    except Exception as e:
        print(f"正则表达式提取失败: {e}")
        return None

def get_chapter_from_storyline(storyline_data: Dict[str, Any], chapter_number: int) -> Optional[Dict[str, Any]]:
    """
    从故事主线数据中获取特定章节
    
    Args:
        storyline_data: 故事主线数据
        chapter_number: 章节号
    
    Returns:
        Dict: 章节数据，如果不存在则返回None
    """
    # 检查outlines字段
    outlines = storyline_data.get("outlines") or storyline_data.get("chapter_outlines") or []
    
    for chapter in outlines:
        if isinstance(chapter, dict):
            chapter_index = chapter.get("chapter_number") or chapter.get("index")
            if chapter_index == chapter_number:
                return chapter
    
    return None

def validate_storyline_quality(storyline_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    检查故事主线是否满足质量要求：章节内容足够丰富且包含男女主角的具体名字
    
    Args:
        storyline_data: 故事主线数据
    
    Returns:
        Dict: {"valid": True/False, "issues": [问题列表], "total_length": 总字数}
    """
    issues = []
    
    # 检查outlines字段是否存在
    outlines = storyline_data.get("outlines") or storyline_data.get("chapter_outlines") or []
    if not outlines:
        return {"valid": False, "issues": ["故事主线没有章节内容"], "total_length": 0}
    
    # 统计总字数
    total_content_length = 0
    short_chapters = 0
    
    # 存储所有内容文本以后面分析
    all_content = ""
    
    for chapter in outlines:
        if not isinstance(chapter, dict):
            continue
            
        # 计算章节内容长度
        content = chapter.get("content", "")
        content_length = len(content)
        total_content_length += content_length
        all_content += content + " "
        
        # 检查章节内容是否过短（少于30个字符）
        if content_length < 30:
            short_chapters += 1
    
    # 检查总字数是否过少（根据章节数进行判断）
    chapter_count = len(outlines)
    expected_min_length = chapter_count * 50  # 每章平均至少50字
    
    if total_content_length < expected_min_length:
        issues.append(f"故事主线总字数过少：{total_content_length}字 (期望至少{expected_min_length}字)")
    
    if short_chapters > chapter_count * 0.2:  # 如果超过20%的章节内容过短
        issues.append(f"有{short_chapters}章内容过短 (共{chapter_count}章)")
    
    # # 检查是否只使用了泛称而没有具体名字
    # generic_names = ["男主", "女主", "主角", "男主角", "女主角"]
    
    # # 检查主线内容中是否包含泛称
    # has_generic_names = any(name in all_content for name in generic_names)
    
    # # 如果包含泛称，认为需要重新生成以给角色具体的名字
    # if has_generic_names:
    #     issues.append("发现使用了男主、女主等泛称，建议重新生成并给角色具体名字")
    
    # 返回验证结果
    return {
        "valid": len(issues) == 0,
        "issues": issues,
        "total_length": total_content_length
    }

if __name__ == "__main__":
    # 测试代码
    result = fix_storyline_file()
    if result:
        print(f"修复成功! 共找到 {len(result.get('outlines', []))} 个章节")
    else:
        print("修复失败") 
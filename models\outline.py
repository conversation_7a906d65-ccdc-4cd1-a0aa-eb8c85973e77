"""
大纲模型
"""

import json
import os
from typing import Dict, Any, List, Optional

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import OUTPUT_DIR

class ChapterOutline:
    """章节大纲模型"""

    def __init__(self, chapter_number: int, title: str = "", content: str = "",
                 is_generated: bool = False, is_completed: bool = False,
                 chapter_summary: Dict[str, Any] = None, 
                 characters: List[Dict[str, Any]] = None,
                 key_points: List[str] = None,
                 foreshadowings: Dict[str, List[str]] = None):
        """
        初始化章节大纲模型

        Args:
            chapter_number: 章节号
            title: 章节标题
            content: 大纲内容
            is_generated: 章节内容是否已生成
            is_completed: 章节是否已完成（生成并检查通过）
            chapter_summary: 章节概要，包含开端/发展/高潮/结尾子字段
            characters: 出场人物列表
            key_points: 关键情节点列表  
            foreshadowings: 伏笔安排，包含新埋下和回收的伏笔
        """
        self.chapter_number = chapter_number
        self.index = chapter_number  # 添加索引字段，与chapter_number同值
        self.title = title
        self.content = content
        self.is_generated = is_generated
        self.is_completed = is_completed
        
        # 新增结构化字段
        self.chapter_summary = chapter_summary or {
            "opening": "",
            "development": "",
            "climax": "",
            "ending": ""
        }
        self.characters = characters or []
        self.key_points = key_points or []
        self.foreshadowings = foreshadowings or {
            "planted": [],
            "revealed": []
        }

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            字典格式的章节大纲
        """
        result = {
            "chapter_number": self.chapter_number,
            "index": self.chapter_number,  # 同时输出两种字段
            "title": self.title,
            "content": self.content,
            "is_generated": self.is_generated,
            "is_completed": self.is_completed
        }
        
        # 添加结构化字段（如果有值）
        if any(v for v in self.chapter_summary.values()):
            result["chapter_summary"] = self.chapter_summary
            
        if self.characters:
            result["characters"] = self.characters
            
        if self.key_points:
            result["key_points"] = self.key_points
            
        if any(v for v in self.foreshadowings.values()):
            result["foreshadowings"] = self.foreshadowings
            
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChapterOutline':
        """
        从字典创建章节大纲模型

        Args:
            data: 字典格式的章节大纲

        Returns:
            章节大纲模型实例
        """
        # 优先使用chapter_number，其次使用index
        chapter_num = data.get("chapter_number", data.get("index", 0))
        return cls(
            chapter_number=chapter_num,
            title=data.get("title", ""),
            content=data.get("content", ""),
            is_generated=data.get("is_generated", False),
            is_completed=data.get("is_completed", False),
            chapter_summary=data.get("chapter_summary", None),
            characters=data.get("characters", None),
            key_points=data.get("key_points", None),
            foreshadowings=data.get("foreshadowings", None)
        )


class NovelOutline:
    """小说大纲模型"""

    def __init__(self, total_chapters: int = 0, chapters: List[ChapterOutline] = None, main_storyline: str = None):
        """
        初始化小说大纲模型

        Args:
            total_chapters: 总章节数
            chapters: 章节列表
            main_storyline: 故事主线(JSON文本或结构化对象)
        """
        self.total_chapters = total_chapters
        self.chapters = chapters or []
        self.main_storyline = main_storyline
        self.file_path = os.path.join(OUTPUT_DIR, "outline.json")

    def add_chapter(self, chapter: ChapterOutline) -> None:
        """
        添加章节

        Args:
            chapter: 章节大纲
        """
        # 检查章节号是否已存在，如果存在则更新
        for i, ch in enumerate(self.chapters):
            if ch.chapter_number == chapter.chapter_number:
                self.chapters[i] = chapter
                return

        # 如果不存在则添加并排序
        self.chapters.append(chapter)
        self.chapters.sort(key=lambda x: x.chapter_number)

        # 更新总章节数
        if len(self.chapters) > self.total_chapters:
            self.total_chapters = len(self.chapters)

    def get_chapter(self, chapter_number: int) -> Optional[ChapterOutline]:
        """
        获取章节

        Args:
            chapter_number: 章节号

        Returns:
            章节大纲，如果不存在则返回None
        """
        for chapter in self.chapters:
            if chapter.chapter_number == chapter_number:
                return chapter
        return None

    def remove_chapter(self, chapter_number: int) -> bool:
        """
        删除章节

        Args:
            chapter_number: 章节号

        Returns:
            删除是否成功
        """
        for i, ch in enumerate(self.chapters):
            if ch.chapter_number == chapter_number:
                self.chapters.pop(i)
                return True
        return False

    def get_completed_chapters_count(self) -> int:
        """
        获取已完成章节数

        Returns:
            已完成章节数
        """
        return sum(1 for chapter in self.chapters if chapter.is_completed)

    def get_generated_chapters_count(self) -> int:
        """
        获取已生成章节数

        Returns:
            已生成章节数
        """
        return sum(1 for chapter in self.chapters if chapter.is_generated)

    def get_previous_chapters_summary(self, current_chapter: int,
                                     max_chapters: int = 3) -> str:
        """
        获取前几章内容摘要

        Args:
            current_chapter: 当前章节号
            max_chapters: 最大摘要章节数

        Returns:
            前几章内容摘要
        """
        summaries = []

        # 获取更多的章节，以便后面进行去重和筛选
        extended_max = max_chapters * 2

        # 获取前几章的大纲
        for i in range(current_chapter - 1, max(0, current_chapter - extended_max - 1), -1):
            chapter = self.get_chapter(i)
            if chapter and chapter.is_completed:
                # 添加章节号、标题和内容
                summaries.append({
                    "chapter_number": chapter.chapter_number,
                    "title": chapter.title,
                    "content": chapter.content
                })

        if not summaries:
            return "这是第一章，无前序内容。"

        # 反转列表，使其按章节顺序排列
        summaries.reverse()

        # 去除重复内容
        filtered_summaries = []
        content_hashes = set()

        for summary in summaries:
            # 创建内容的简化版本用于比较（去除空格和标点）
            simplified_content = ''.join(c for c in summary["content"] if not c.isspace() and c not in '，。！？,.!?')
            content_hash = hash(simplified_content[:100])  # 使用内容前100个字符的哈希值

            if content_hash not in content_hashes:
                content_hashes.add(content_hash)
                filtered_summaries.append(summary)

                # 如果已经有足够的不重复章节，就停止
                if len(filtered_summaries) >= max_chapters:
                    break

        # 将过滤后的摘要转换为文本格式
        formatted_summaries = []
        for summary in filtered_summaries:
            formatted_summaries.append(
                f"第{summary['chapter_number']}章 {summary['title']}：{summary['content']}"
            )

        return "\n\n".join(formatted_summaries)

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            字典格式的小说大纲
        """
        result = {
            "total_chapters": self.total_chapters,
            "chapters": [chapter.to_dict() for chapter in self.chapters]
        }
        
        # 添加主线信息（如果存在）
        if self.main_storyline:
            # 如果main_storyline已经是JSON格式的字符串，尝试解析为对象
            if isinstance(self.main_storyline, str):
                try:
                    import json
                    from utils.json_helper import parse_json_safely
                    
                    # 尝试解析为对象
                    parsed_data = parse_json_safely(self.main_storyline)
                    if parsed_data:
                        # 处理解析后的数据，确保只使用outlines字段
                        if isinstance(parsed_data, dict):
                            # 如果有chapter_outlines但没有outlines，则复制一份
                            if "chapter_outlines" in parsed_data and "outlines" not in parsed_data:
                                parsed_data["outlines"] = parsed_data["chapter_outlines"]
                            
                            # 删除chapter_outlines字段
                            if "chapter_outlines" in parsed_data:
                                del parsed_data["chapter_outlines"]
                                
                        result["main_storyline"] = parsed_data
                    else:
                        # 如果解析失败，存储为原始文本
                        result["main_storyline"] = {"content": self.main_storyline}
                except:
                    result["main_storyline"] = {"content": self.main_storyline}
            else:
                # 如果已经是对象（字典/列表等），直接存储并确保只有outlines字段
                if isinstance(self.main_storyline, dict):
                    # 创建副本避免修改原始数据
                    storyline_copy = self.main_storyline.copy()
                    
                    # 如果有chapter_outlines但没有outlines，则复制过来
                    if "chapter_outlines" in storyline_copy and "outlines" not in storyline_copy:
                        storyline_copy["outlines"] = storyline_copy["chapter_outlines"]
                    
                    # 删除chapter_outlines字段
                    if "chapter_outlines" in storyline_copy:
                        del storyline_copy["chapter_outlines"]
                    
                    result["main_storyline"] = storyline_copy
                else:
                    # 如果是列表或其他结构，直接保存
                    result["main_storyline"] = self.main_storyline
                
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'NovelOutline':
        """
        从字典创建小说大纲模型

        Args:
            data: 字典格式的小说大纲

        Returns:
            小说大纲模型实例
        """
        chapters = [ChapterOutline.from_dict(ch) for ch in data.get("chapters", [])]
        
        # 提取主线信息
        main_storyline = None
        if "main_storyline" in data:
            main_storyline_data = data["main_storyline"]
            
            try:
                # 如果是字典且只有content字段，提取文本内容
                if isinstance(main_storyline_data, dict) and "content" in main_storyline_data and len(main_storyline_data) == 1:
                    main_storyline = main_storyline_data["content"]
                else:
                    # 否则保留原始数据
                    import json
                    
                    # 如果main_storyline_data不是字符串，将其序列化
                    if not isinstance(main_storyline_data, str):
                        main_storyline_data = json.dumps(main_storyline_data, ensure_ascii=False, indent=2)
                    
                    main_storyline = main_storyline_data
            except Exception:
                # 如果处理失败，尝试最简单的方式保留数据
                import json
                if isinstance(main_storyline_data, dict) and "content" in main_storyline_data and len(main_storyline_data) == 1:
                    main_storyline = main_storyline_data["content"]
                else:
                    try:
                        main_storyline = json.dumps(main_storyline_data, ensure_ascii=False, indent=2)
                    except:
                        main_storyline = str(main_storyline_data)
        
        return cls(
            total_chapters=data.get("total_chapters", 0),
            chapters=chapters,
            main_storyline=main_storyline
        )

    def save(self) -> bool:
        """
        保存小说大纲到文件

        Returns:
            保存是否成功
        """
        try:
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存小说大纲时出错: {str(e)}")
            return False

    @classmethod
    def load(cls, file_path: Optional[str] = None) -> Optional['NovelOutline']:
        """
        从文件加载小说大纲

        Args:
            file_path: 文件路径，如果为None则使用默认路径

        Returns:
            小说大纲模型实例，如果加载失败则返回None
        """
        try:
            file_path = file_path or os.path.join(OUTPUT_DIR, "outline.json")
            if not os.path.exists(file_path):
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            return cls.from_dict(data)
        except Exception as e:
            print(f"加载小说大纲时出错: {str(e)}")
            return None

class ChapterOutlineManager:
    """章节大纲管理器，基于NovelOutline类，但提供更多功能"""

    def __init__(self, total_chapters: int = 0, chapters: List[ChapterOutline] = None):
        """
        初始化章节大纲管理器
        
        Args:
            total_chapters: 总章节数
            chapters: 章节列表
        """
        self.total_chapters = total_chapters
        self.chapters = chapters or []
        self.file_path = os.path.join(OUTPUT_DIR, "outline.json")
    
    def add_chapter(self, chapter: ChapterOutline) -> None:
        """
        添加章节
        
        Args:
            chapter: 章节大纲
        """
        # 检查章节号是否已存在，如果存在则更新
        for i, ch in enumerate(self.chapters):
            if ch.chapter_number == chapter.chapter_number:
                self.chapters[i] = chapter
                return
        
        # 如果不存在则添加并排序
        self.chapters.append(chapter)
        self.chapters.sort(key=lambda x: x.chapter_number)
        
        # 更新总章节数
        if len(self.chapters) > self.total_chapters:
            self.total_chapters = len(self.chapters)
    
    def get_chapter(self, chapter_number: int) -> Optional[ChapterOutline]:
        """
        获取章节
        
        Args:
            chapter_number: 章节号
        
        Returns:
            章节大纲，如果不存在则返回None
        """
        for chapter in self.chapters:
            if chapter.chapter_number == chapter_number:
                return chapter
        return None
    
    def get_last_generated_chapter(self) -> Optional[int]:
        """
        获取最后一个已生成的章节号
        
        Returns:
            最后一个已生成的章节号，如果没有则返回None
        """
        generated_chapters = [ch.chapter_number for ch in self.chapters if ch.is_generated]
        return max(generated_chapters) if generated_chapters else None
    
    def get_completed_chapters_count(self) -> int:
        """
        获取已完成章节数
        
        Returns:
            已完成章节数
        """
        return sum(1 for chapter in self.chapters if chapter.is_completed)
    
    def get_previous_chapters_summary(self, current_chapter: int, max_chapters: int = 3) -> str:
        """
        获取前几章内容摘要
        
        Args:
            current_chapter: 当前章节号
            max_chapters: 最大摘要章节数
        
        Returns:
            前几章内容摘要
        """
        summaries = []
        
        # 获取前几章的大纲
        for i in range(current_chapter - 1, max(0, current_chapter - max_chapters - 1), -1):
            chapter = self.get_chapter(i)
            if chapter and chapter.is_completed:
                summaries.append(f"第{chapter.chapter_number}章 {chapter.title}：{chapter.content}")
        
        if not summaries:
            return "这是第一章，无前序内容。"
        
        # 反转列表，使其按章节顺序排列
        summaries.reverse()
        
        return "\n\n".join(summaries)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            字典格式的章节大纲管理器
        """
        return {
            "total_chapters": self.total_chapters,
            "chapters": [chapter.to_dict() for chapter in self.chapters]
        }
    
    def save(self) -> bool:
        """
        保存章节大纲到文件
        
        Returns:
            保存是否成功
        """
        try:
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存章节大纲时出错: {str(e)}")
            return False
    
    @classmethod
    def load(cls, total_chapters: int) -> Optional['ChapterOutlineManager']:
        """
        从文件加载章节大纲
        
        Args:
            total_chapters: 总章节数，用于初始化
        
        Returns:
            章节大纲管理器实例，如果加载失败则返回None
        """
        try:
            file_path = os.path.join(OUTPUT_DIR, "outline.json")
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            chapters = [ChapterOutline.from_dict(ch) for ch in data.get("chapters", [])]
            
            return cls(
                total_chapters=data.get("total_chapters", total_chapters),
                chapters=chapters
            )
        except Exception as e:
            print(f"加载章节大纲时出错: {str(e)}")
            return None
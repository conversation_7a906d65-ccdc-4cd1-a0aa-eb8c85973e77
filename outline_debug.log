2025-05-20 15:45:55,284 - __main__ - INFO - ===== 开始测试生成第1章大纲 =====
2025-05-20 15:45:55,285 - __main__ - ERROR - 加载项目失败，请确保项目已经初始化
2025-05-20 15:57:06,415 - __main__ - INFO - ===== 开始测试生成第1章大纲 =====
2025-05-20 15:57:06,416 - __main__ - ERROR - 加载项目失败，请确保项目已经初始化
2025-05-20 15:58:23,515 - __main__ - INFO - 直接分析文件: debug.json
2025-05-20 15:58:23,515 - __main__ - INFO - === 原始JSON字符串 ===
2025-05-20 15:58:23,515 - __main__ - INFO - JSON前500字符: ﻿{
  "story_title": "鏄熻景閬撻€?,
  "outlines": [
    {
      "index": 1,
      "title": "閲忓瓙閲嶇敓",
      "content": "40宀佺殑閲忓瓙鐗╃悊瀛﹀闄堢巹鍦ㄥ疄楠屽鐖嗙偢涓┛瓒婂埌18宀佸悓鍚嶅皯骞翠綋鍐咃紝鍙戠幇韬淇湡涓栫晫銆傞€氳繃鍐呰瀵熻浣撳唴娈嬬暀鐨勬槦杈颁箣鍔涳紝灏濊瘯鐢ㄦ尝鍑芥暟瑙ｉ噴鐏垫皵杩愯銆傝璺繃鐨勬槦闄ㄥ畻鏀跺緬闃熶紞璇涓哄ぉ璧嬪紓绂€锛屽煁涓嬩笌濂充富鑻忕拑鍒濇鐩搁亣鐨勪紡绗斻€?
    },
    {
      "index": 2,
      "title": "鍐扮伒鏍圭殑灏戝コ",
      "content": "鍏ラ棬娴嬭瘯鎻湶闄堢巹瀹為檯鐏垫牴娣锋潅锛屽嵈琚畨鎺掍笌澶╂墠灏戝コ鑻忕拑鍚岀粍銆傝嫃鐠冨洜瀹舵棌鐏棬妗堝帉鎭剁敺鎬ф帴瑙︼紝瀵归檲鐜勮交娴妇姝㈠挨涓哄弽鎰熴€傞檲鐜勬殫涓敤鍏夎氨鍒嗘瀽娉曞彂鐜板ス鍐扮伒鏍瑰瓨鍦ㄩ噺瀛愮籂缂犵幇璞★紝鏁呮剰寮曞彂浜夋墽鑾峰彇鏁版嵁鏍锋湰銆?
    },
   ...
2025-05-20 15:58:23,516 - __main__ - INFO - JSON后500字符: 槦杈颁箣鍔涘瓨鍦ㄩ噺瀛愬叧鑱旓紝寮曡捣绁炵浜虹殑鐩戣銆?
    },
    {
      "index": 6,
      "title": "濡栧吔妫灄鐨勫崥寮?,
      "content": "瀹楅棬娲惧紵瀛愭竻鍓垮彉寮傚鍏斤紝闄堢巹鍙戠幇濡栧吔鍩哄洜閿佸憟鐜板懆鏈熸€у穿婧冦€傚嵄鎬ユ椂鍒荤敤澹版尝鍏辨尟骞叉壈鍏界兢锛屾晳涓嬭鍥扮殑鑻忕拑銆傚ス鍥犱激鍔胯繃閲嶄笉寰椾笉鎺ュ彈闄堢巹鐨勮緭琛€鐤楁硶锛屼袱浜鸿娑蹭骇鐢熺伒鑳借瀺鍚堢幇璞★紝琚殫澶勭殑榛戣。浜鸿褰曘€?
    },
    {
      "index": 7,
      "title": "鎷嶅崠浼氱殑缁忔祹瀛?,
      "content": "闄堢巹鐢ㄥ崥寮堣鎿嶇旱瀹楅棬鎷嶅崠浼氾紝浣庝环璐緱娈嬬己鐨勬槦杈板浘銆傝嫃鐠冭鍑烘鐗╀笌瀹舵棌閬楃墿鐩镐技锛岃杩笌浠栬揪鎴愬悎浣溿€備袱浜洪伃閬囩绉樹慨澹姠澶猴紝闄堢巹鍚姩鑷埗鐨勭數纾佸睆闅滆缃紝鍙戠幇瀵规柟浣跨敤绫讳技閲忓瓙绾犵紶鐨勪紶璁柟寮忋€?
    },
    {
      "index": 8,
...
2025-05-20 15:58:23,518 - __main__ - ERROR - JSON解析错误: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
2025-05-20 15:58:23,518 - __main__ - ERROR - 错误位置附近的内容（可视化）: ﻿{\n␣␣"story_title":␣"鏄熻景閬撻€?,\n␣␣"outlines":␣[\n␣␣␣␣
2025-05-20 15:58:23,518 - __main__ - ERROR - 错误位置指示: ^
2025-05-20 15:58:23,518 - __main__ - INFO - 尝试修复换行符+title问题...
2025-05-20 15:58:23,519 - __main__ - INFO - 应用了换行符+title修复
2025-05-20 15:58:23,519 - __main__ - ERROR - 修复换行符+title问题后仍然解析失败: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
2025-05-20 15:58:23,519 - __main__ - INFO - 尝试使用fix_storyline_json修复...
2025-05-20 15:58:23,522 - __main__ - INFO - 使用fix_storyline_json修复成功
2025-05-20 15:58:23,523 - __main__ - INFO - 修复后的JSON已保存到: D:\ai\novel\output\fixed_outline.json
2025-05-20 16:24:58,003 - __main__ - INFO - ===== 开始测试生成第1章大纲 =====
2025-05-20 16:24:58,004 - __main__ - ERROR - 加载项目失败，请确保项目已经初始化
2025-05-20 16:25:33,115 - __main__ - INFO - ===== 开始测试生成第1章大纲 =====
2025-05-20 16:25:33,116 - __main__ - INFO - 找到必要的项目文件，尝试初始化...
2025-05-20 16:25:33,116 - __main__ - INFO - 开始生成第1章大纲...
2025-05-20 16:25:33,117 - __main__ - ERROR - 第1章大纲生成失败
2025-05-20 16:26:09,955 - __main__ - INFO - ===== 模拟调试JSON中的\n title错误 =====
2025-05-20 16:26:09,955 - __main__ - INFO - 已创建模拟错误的JSON文件: D:\ai\novel\output\test_error.json
2025-05-20 16:26:09,955 - __main__ - INFO - === 原始JSON字符串 ===
2025-05-20 16:26:09,955 - __main__ - INFO - 
    {
      "title": "第1章 觉醒",
      "chapter_summary": {
        "opening": "章节开始的场景描述",
        "development": "情节如何推进",
        "climax": "本章的高潮部分"
      },
      "characters": [
        {
          "name": "主角名字"
        },
        {
          "name": "配角名字"
        }
      ]
      
          "title": "这里有错误的title字段",
      "key_points": [
        "关键点1",
        "关键点2"
      ]
    }
    
2025-05-20 16:26:09,956 - __main__ - ERROR - JSON解析错误: Expecting ',' delimiter: line 18 column 11 (char 299)
2025-05-20 16:26:09,957 - __main__ - ERROR - 错误位置附近的内容（可视化）: "name":␣"配角名字"\n␣␣␣␣␣␣␣␣}\n␣␣␣␣␣␣]\n␣␣␣␣␣␣\n␣␣␣␣␣␣␣␣␣␣"title":␣"这里有错误的title字段",\n␣␣␣␣␣␣"key_points":␣[\n␣␣
2025-05-20 16:26:09,957 - __main__ - ERROR - 错误位置指示:                                                   ^
2025-05-20 16:26:09,957 - __main__ - INFO - 尝试修复换行符+title问题...
2025-05-20 16:26:09,957 - __main__ - INFO - 应用了换行符+title修复
2025-05-20 16:26:09,957 - __main__ - ERROR - 修复换行符+title问题后仍然解析失败: Expecting ',' delimiter: line 8 column 9 (char 220)
2025-05-20 16:26:09,958 - __main__ - INFO - 尝试使用fix_storyline_json修复...
2025-05-20 16:26:09,958 - __main__ - INFO - 使用fix_storyline_json修复成功
2025-05-20 16:26:09,959 - __main__ - INFO - 修复后的JSON已保存到: D:\ai\novel\output\fixed_outline.json
2025-05-20 16:26:09,959 - __main__ - INFO - ===== 修复后的JSON =====
2025-05-20 16:26:09,959 - __main__ - INFO - 
    { "title": "第1章 觉醒", "chapter_summary": { "opening": "章节开始的场景描述", "development": "情节如何推进", "climax": "本章的高潮部分"
      }, "characters": [
        { "name": "主角名字"
        },
        { "name": "配角名字"
        }
      ] "title": "这里有错误的title字段", "key_points": [ "关键点1", "关键点2"
      ]
    }
    
2025-05-20 16:26:09,960 - __main__ - ERROR - 修复后仍无法解析: Expecting ',' delimiter: line 8 column 9 (char 220)
2025-05-20 16:26:51,596 - __main__ - INFO - ===== 模拟调试JSON中的\n title错误 =====
2025-05-20 16:26:51,596 - __main__ - INFO - 已创建模拟错误的JSON文件: D:\ai\novel\output\test_error.json
2025-05-20 16:26:51,597 - __main__ - INFO - 已创建复杂错误的JSON文件: D:\ai\novel\output\complex_error.json
2025-05-20 16:26:51,597 - __main__ - INFO - 
===== 测试修复基本错误 =====
2025-05-20 16:27:16,884 - __main__ - INFO - ===== 模拟调试JSON中的\n title错误 =====
2025-05-20 16:27:16,885 - __main__ - INFO - 已创建模拟错误的JSON文件: D:\ai\novel\output\test_error.json
2025-05-20 16:27:16,885 - __main__ - INFO - 已创建复杂错误的JSON文件: D:\ai\novel\output\complex_error.json
2025-05-20 16:27:16,885 - __main__ - INFO - 
===== 测试修复基本错误 =====
2025-05-20 16:27:16,886 - __main__ - INFO - 修复后的JSON:
2025-05-20 16:27:16,886 - __main__ - INFO -  { "title": "第1章 觉醒",, "chapter_summary": { "opening": "章节开始的场景描述",, "development": "情节如何推进", "climax": , "本章的高潮部分" }, "characters": [ { "name": , "主角名字" }, { "name": , "配角名字" } ], "title": "这里有错误的title字段",, "key_points": [ "关键点1", "关键点2" ] } 
2025-05-20 16:27:16,886 - __main__ - ERROR - 基本错误修复后仍无法解析: Expecting property name enclosed in double quotes: line 1 column 22 (char 21)
2025-05-20 16:27:16,887 - __main__ - INFO - 
===== 测试修复复杂错误 =====
2025-05-20 16:27:16,887 - __main__ - INFO - 修复后的JSON:
2025-05-20 16:27:16,887 - __main__ - INFO -  { "character_info": {, "name": "张三", "age": 25, "occupation": "学生" }, "story_outline": [ {"chapter": 1,, "title": "开始"}, {"chapter": 2,, "title": "发展"}, {"chapter": 3,, "title": "高潮"} ], "settings": {, "location": "大城市", "time": , "现代" } } 
2025-05-20 16:27:16,887 - __main__ - ERROR - 复杂错误修复后仍无法解析: Expecting property name enclosed in double quotes: line 1 column 23 (char 22)
2025-05-20 16:27:48,018 - __main__ - INFO - ===== 模拟调试JSON中的\n title错误 =====
2025-05-20 16:27:48,019 - __main__ - INFO - 已创建模拟错误的JSON文件: D:\ai\novel\output\test_error.json
2025-05-20 16:27:48,019 - __main__ - INFO - 已创建复杂错误的JSON文件: D:\ai\novel\output\complex_error.json
2025-05-20 16:27:48,019 - __main__ - INFO - 
===== 测试修复基本错误 =====
2025-05-20 16:27:48,020 - __main__ - INFO - 修复后的JSON:
2025-05-20 16:27:48,020 - __main__ - INFO -  { "title": "第1章 觉醒", "chapter_summary": { "opening": "章节开始的场景描述", "development": "情节如何推进", "climax": "本章的高潮部分" }, "characters": [ { "name": "主角名字" }, { "name": "配角名字" } ], "title": "这里有错误的title字段", "key_points": [ "关键点1", "关键点2" ] } 
2025-05-20 16:27:48,020 - __main__ - INFO - 基本错误修复成功! 能够正确解析JSON
2025-05-20 16:27:48,020 - __main__ - INFO - 
===== 测试修复复杂错误 =====
2025-05-20 16:27:48,021 - __main__ - INFO - 修复后的JSON:
2025-05-20 16:27:48,021 - __main__ - INFO - { "character_info": { "name": "张三", "age": 25, "occupation": "学生" }, "story_outline": [ {"chapter": 1, "title": "开始"}, {"chapter": 2, "title": "发展"}, {"chapter": 3, "title": "高潮"} ], "settings": { "location": "大城市", "time": "现代" } }
2025-05-20 16:27:48,022 - __main__ - INFO - 复杂错误修复成功! 能够正确解析JSON
2025-05-20 16:28:49,174 - __main__ - INFO - ===== 开始测试生成第1章大纲 =====
2025-05-20 16:28:49,175 - __main__ - INFO - 找到必要的项目文件，尝试初始化...
2025-05-20 16:28:49,175 - __main__ - INFO - 开始生成第1章大纲...
2025-05-20 16:28:49,175 - __main__ - ERROR - 第1章大纲生成失败
2025-05-20 16:29:32,256 - __main__ - INFO - ===== 开始测试生成第1章大纲 =====
2025-05-20 16:29:32,256 - __main__ - INFO - 找到必要的项目文件，尝试初始化...
2025-05-20 16:29:32,257 - __main__ - INFO - 开始生成第1章大纲...
2025-05-20 16:29:32,257 - __main__ - INFO - 直接从API获取第1章大纲...
2025-05-20 16:29:32,258 - __main__ - ERROR - 获取大纲时出错: '\n          "title"'
2025-05-20 16:29:32,283 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\ai\novel\generate_outline.py", line 252, in get_outline_directly
    outline_content = generator.api.generate_outline(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ai\novel\llm\deepseek_all.py", line 705, in generate_outline
    """.format(current_chapter, chapter_type_guidance, progress, chapter_specific_guidance)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyError: '\n          "title"'

2025-05-20 16:29:32,283 - __main__ - ERROR - 第1章大纲生成失败
2025-05-20 16:30:16,009 - __main__ - INFO - ===== 开始测试从outline.json中修复格式 =====
2025-05-20 16:30:16,009 - __main__ - INFO - 成功读取outline.json，长度: 11298字符
2025-05-20 16:30:16,009 - __main__ - INFO - 应用JSON修复...
2025-05-20 16:30:16,011 - __main__ - INFO - 修复后的JSON已保存到: D:\ai\novel\output\fixed_outline.json
2025-05-20 16:30:16,011 - __main__ - INFO - 修复后的JSON解析成功
2025-05-20 16:30:16,012 - __main__ - INFO - 格式化后的JSON已保存到: D:\ai\novel\output\formatted_outline.json
2025-05-20 16:32:05,867 - __main__ - INFO - ===== 开始测试生成第1章大纲 =====
2025-05-20 16:32:05,867 - __main__ - INFO - 找到必要的项目文件，尝试初始化...
2025-05-20 16:32:05,867 - __main__ - INFO - 开始生成第1章大纲...
2025-05-20 16:32:05,868 - __main__ - INFO - 直接从API获取第1章大纲...
2025-05-20 16:32:05,868 - __main__ - ERROR - 获取大纲时出错: '\n          "title"'
2025-05-20 16:32:05,870 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\ai\novel\generate_outline.py", line 252, in get_outline_directly
    outline_content = generator.api.generate_outline(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ai\novel\llm\deepseek_all.py", line 705, in generate_outline
    """.format(current_chapter, chapter_type_guidance, progress, chapter_specific_guidance)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyError: '\n          "title"'

2025-05-20 16:32:05,870 - __main__ - ERROR - 第1章大纲生成失败
2025-05-20 16:33:37,483 - __main__ - INFO - ===== 模拟调试JSON中的\n title错误 =====
2025-05-20 16:33:37,483 - __main__ - INFO - 已创建模拟错误的JSON文件: D:\ai\novel\output\test_error.json
2025-05-20 16:33:37,484 - __main__ - INFO - 已创建复杂错误的JSON文件: D:\ai\novel\output\complex_error.json
2025-05-20 16:33:37,484 - __main__ - INFO - 
===== 测试修复基本错误 =====
2025-05-20 16:33:37,484 - __main__ - INFO - 修复后的JSON:
2025-05-20 16:33:37,485 - __main__ - INFO -  { "title": "第1章 觉醒", "chapter_summary": { "opening": "章节开始的场景描述", "development": "情节如何推进", "climax": "本章的高潮部分" }, "characters": [ { "name": "主角名字" }, { "name": "配角名字" } ], "title": "这里有错误的title字段", "key_points": [ "关键点1", "关键点2" ] } 
2025-05-20 16:33:37,485 - __main__ - INFO - 基本错误修复成功! 能够正确解析JSON
2025-05-20 16:33:37,485 - __main__ - INFO - 
===== 测试修复复杂错误 =====
2025-05-20 16:33:37,486 - __main__ - INFO - 修复后的JSON:
2025-05-20 16:33:37,486 - __main__ - INFO - { "character_info": { "name": "张三", "age": 25, "occupation": "学生" }, "story_outline": [ {"chapter": 1, "title": "开始"}, {"chapter": 2, "title": "发展"}, {"chapter": 3, "title": "高潮"} ], "settings": { "location": "大城市", "time": "现代" } }
2025-05-20 16:33:37,486 - __main__ - INFO - 复杂错误修复成功! 能够正确解析JSON
2025-05-20 16:33:41,954 - __main__ - INFO - ===== 开始测试从outline.json中修复格式 =====
2025-05-20 16:33:41,954 - __main__ - INFO - 成功读取outline.json，长度: 11298字符
2025-05-20 16:33:41,955 - __main__ - INFO - 应用JSON修复...
2025-05-20 16:33:41,956 - __main__ - INFO - 修复后的JSON已保存到: D:\ai\novel\output\fixed_outline.json
2025-05-20 16:33:41,956 - __main__ - INFO - 修复后的JSON解析成功
2025-05-20 16:33:41,957 - __main__ - INFO - 格式化后的JSON已保存到: D:\ai\novel\output\formatted_outline.json
2025-05-20 16:38:56,449 - __main__ - INFO - ===== 模拟调试JSON中的\n title错误 =====
2025-05-20 16:38:56,449 - __main__ - INFO - 已创建模拟错误的JSON文件: D:\ai\novel\output\test_error.json
2025-05-20 16:38:56,449 - __main__ - INFO - 已创建复杂错误的JSON文件: D:\ai\novel\output\complex_error.json
2025-05-20 16:38:56,450 - __main__ - INFO - 
===== 测试修复基本错误 =====
2025-05-20 16:38:56,450 - __main__ - INFO - 修复后的JSON:
2025-05-20 16:38:56,450 - __main__ - INFO -  { "title": "第1章 觉醒", "chapter_summary": { "opening": "章节开始的场景描述", "development": "情节如何推进", "climax": "本章的高潮部分" }, "characters": [ { "name": "主角名字" }, { "name": "配角名字" } ], "title": "这里有错误的title字段", "key_points": [ "关键点1", "关键点2" ] } 
2025-05-20 16:38:56,450 - __main__ - INFO - 基本错误修复成功! 能够正确解析JSON
2025-05-20 16:38:56,450 - __main__ - INFO - 
===== 测试修复复杂错误 =====
2025-05-20 16:38:56,451 - __main__ - INFO - 修复后的JSON:
2025-05-20 16:38:56,451 - __main__ - INFO - { "character_info": { "name": "张三", "age": 25, "occupation": "学生" }, "story_outline": [ {"chapter": 1, "title": "开始"}, {"chapter": 2, "title": "发展"}, {"chapter": 3, "title": "高潮"} ], "settings": { "location": "大城市", "time": "现代" } }
2025-05-20 16:38:56,451 - __main__ - INFO - 复杂错误修复成功! 能够正确解析JSON
2025-05-20 16:39:01,596 - __main__ - INFO - ===== 开始测试从outline.json中修复格式 =====
2025-05-20 16:39:01,596 - __main__ - INFO - 成功读取outline.json，长度: 11298字符
2025-05-20 16:39:01,597 - __main__ - INFO - 应用JSON修复...
2025-05-20 16:39:01,598 - __main__ - INFO - 修复后的JSON已保存到: D:\ai\novel\output\fixed_outline.json
2025-05-20 16:39:01,598 - __main__ - INFO - 修复后的JSON解析成功
2025-05-20 16:39:01,599 - __main__ - INFO - 格式化后的JSON已保存到: D:\ai\novel\output\formatted_outline.json
2025-05-20 16:39:08,143 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 16:39:08,144 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 16:39:08,144 - __main__ - ERROR - 加载项目失败
2025-05-20 16:39:34,587 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 16:39:34,587 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 16:39:34,587 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 16:40:05,636 - __main__ - INFO - 第1章大纲直接生成成功
2025-05-20 16:42:13,793 - __main__ - INFO - ===== 开始测试生成第1章大纲 =====
2025-05-20 16:42:13,793 - __main__ - INFO - 找到必要的项目文件，尝试初始化...
2025-05-20 16:42:13,793 - __main__ - INFO - 开始生成第1章大纲...
2025-05-20 16:42:13,794 - __main__ - INFO - 直接从API获取第1章大纲...
2025-05-20 16:42:26,979 - __main__ - INFO - 原始JSON已保存到: D:\ai\novel\output\debug.json
2025-05-20 16:42:26,979 - __main__ - INFO - 应用JSON修复...
2025-05-20 16:42:26,980 - __main__ - INFO - 修复后的JSON已保存到: D:\ai\novel\output\fixed_debug.json
2025-05-20 16:42:26,980 - __main__ - INFO - 修复后的JSON解析成功
2025-05-20 16:42:26,981 - __main__ - INFO - 格式化后的JSON已保存到: D:\ai\novel\output\formatted_debug.json
2025-05-20 16:42:26,981 - __main__ - INFO - 第1章大纲生成成功
2025-05-20 16:47:30,124 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 16:47:30,124 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 16:47:30,124 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 16:47:49,168 - __main__ - INFO - 第1章大纲直接生成成功
2025-05-20 16:48:34,783 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 16:48:34,783 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 16:48:34,783 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 16:48:46,116 - __main__ - INFO - 第1章大纲直接生成成功
2025-05-20 16:49:14,359 - __main__ - INFO - ===== 开始测试从outline.json中修复格式 =====
2025-05-20 16:49:14,360 - __main__ - INFO - 成功读取outline.json，长度: 250字符
2025-05-20 16:49:14,360 - __main__ - INFO - 应用JSON修复...
2025-05-20 16:49:14,360 - __main__ - INFO - 修复后的JSON已保存到: D:\ai\novel\output\fixed_outline.json (仅用于调试)
2025-05-20 16:49:14,361 - __main__ - INFO - 修复后的JSON解析成功
2025-05-20 16:49:14,361 - __main__ - INFO - 格式化后的JSON已保存到: D:\ai\novel\output\formatted_outline.json (仅用于调试)
2025-05-20 16:49:14,361 - __main__ - INFO - 修复的内容不是标准章节大纲格式，仅作为调试文件保存
2025-05-20 16:49:19,510 - __main__ - INFO - 直接分析文件: output/debug.json
2025-05-20 16:49:19,510 - __main__ - INFO - === 原始JSON字符串 ===
2025-05-20 16:49:19,511 - __main__ - INFO - JSON前500字符: {
  "title": "第2章 命运转折",
  "chapter_summary": {
    "opening": "清晨，林陌从修炼状态中醒来，感受到体内灵力流转更加顺畅。他惊讶地发现，昨夜所得古剑已与他建立了某种联系。",
    "development": "林陌尝试运转功法，意外发现自己可以引导雷霆之力。正当他惊喜之时，宗门执事前来传达一个消息：三日后将有一场选拔测试，可能关系到他能否继续留在宗门。",
    "climax": "林陌决定冒险使用新得到的力量参加测试。在密林深处，他遇到一群修为高深的弟子，他们嘲笑林陌的废柴身份。危急时刻，古剑自主护主，释放出惊人的威能。",
    "ending": "一位神秘长老察觉到这股能量波动，暗中观察了整个过程。林陌击退对手后返回住处，不知自己已被盯上。"
  },
  "characters": [
    {
      "name": "林陌",
      "role": "主角",
      "description": "原本被认为灵根枯竭的废柴弟子，获得神秘古剑后开始崛起之路"
    },
    {...
2025-05-20 16:49:19,511 - __main__ - INFO - JSON后500字符: 看不起林陌"
    },
    {
      "name": "神秘长老",
      "role": "神秘人物",
      "description": "暗中观察林陌的强大修士，身份成谜"
    }
  ],
  "key_points": [
    "林陌尝试掌控古剑中的雷霆之力",
    "宗门宣布选拔测试的消息",
    "林陌与外门精英弟子的冲突",
    "古剑第一次展现护主能力",
    "神秘长老的暗中观察"
  ],
  "foreshadowings": {
    "planted": [
      {
        "id": "mysterious-elder",
        "content": "神秘长老对林陌的特殊关注，暗示他可能知道古剑的来历"
      },
      {
        "id": "sword-consciousness",
        "content": "古剑自主护主，暗示其中可能存在器灵或者其他意识"
      }
    ],
    "revealed": []
  }
}...
2025-05-20 16:49:19,512 - __main__ - WARNING - 预分析发现以下可能的错误模式:
2025-05-20 16:49:19,513 - __main__ - WARNING - - 发现换行+属性名错误模式
2025-05-20 16:49:19,513 - __main__ - INFO - 直接解析JSON成功
2025-05-20 16:50:00,644 - __main__ - INFO - ===== 开始将debug.json修复并保存到outline.json (章节2) =====
2025-05-20 16:50:00,644 - __main__ - INFO - 成功读取debug.json，长度: 1171字符
2025-05-20 16:50:00,644 - __main__ - INFO - 应用JSON修复...
2025-05-20 16:50:00,646 - __main__ - INFO - 修复后的JSON已保存到: D:\ai\novel\output\fixed_debug.json (仅用于调试)
2025-05-20 16:50:00,646 - __main__ - INFO - 修复后的JSON解析成功
2025-05-20 16:50:00,647 - __main__ - INFO - 格式化后的JSON已保存到: D:\ai\novel\output\formatted_debug.json (仅用于调试)
2025-05-20 16:50:00,647 - __main__ - INFO - 创建章节大纲对象...
2025-05-20 16:50:00,648 - __main__ - INFO - 成功将第2章大纲保存到outline.json
2025-05-20 16:57:47,104 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 16:57:47,104 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 16:57:47,104 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 16:58:01,613 - __main__ - INFO - 第1章大纲直接生成成功
2025-05-20 16:59:51,022 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 16:59:51,023 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 16:59:51,023 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 17:00:18,023 - __main__ - INFO - 第1章大纲直接生成成功
2025-05-20 17:08:15,074 - __main__ - INFO - ===== 开始直接测试第3章大纲生成 =====
2025-05-20 17:08:15,074 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 17:08:15,074 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 17:08:32,184 - __main__ - INFO - 第3章大纲直接生成成功
2025-05-20 17:15:15,588 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 17:15:15,588 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 17:15:15,589 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 17:15:32,017 - __main__ - INFO - 第1章大纲直接生成成功
2025-05-20 17:30:17,812 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 17:30:17,813 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 17:30:17,813 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 17:30:35,449 - __main__ - INFO - 第1章大纲直接生成成功
2025-05-20 17:53:08,078 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 17:53:08,079 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 17:53:08,079 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 17:53:26,303 - __main__ - INFO - 第1章大纲直接生成成功
2025-05-20 17:53:46,111 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 17:53:46,111 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 17:53:46,112 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 17:54:19,915 - __main__ - INFO - 第1章大纲直接生成成功
2025-05-20 18:07:40,619 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 18:07:40,619 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 18:07:40,620 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 18:07:56,848 - __main__ - INFO - 第1章大纲直接生成成功
2025-05-20 18:12:45,558 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 18:12:45,559 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 18:12:45,559 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 18:13:01,879 - __main__ - INFO - 第1章大纲直接生成成功
2025-05-20 18:17:28,827 - __main__ - INFO - ===== 开始将debug.json修复并保存到outline.json (章节1) =====
2025-05-20 18:17:28,827 - __main__ - ERROR - 文件不存在: D:\ai\novel\output\debug.json
2025-05-20 18:17:33,899 - __main__ - INFO - ===== 开始直接测试第1章大纲生成 =====
2025-05-20 18:17:33,899 - __main__ - ERROR - 找不到必要的项目文件，请确保项目已经初始化
2025-05-20 18:17:48,898 - __main__ - INFO - ===== 开始将debug.json修复并保存到outline.json (章节1) =====
2025-05-20 18:17:48,898 - __main__ - INFO - 成功读取debug.json，长度: 1610字符
2025-05-20 18:17:48,898 - __main__ - INFO - 应用JSON修复...
2025-05-20 18:17:48,899 - __main__ - INFO - 修复后的JSON已保存到: D:\ai\novel\output\fixed_debug.json (仅用于调试)
2025-05-20 18:17:48,900 - __main__ - ERROR - 修复后的JSON解析失败: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
2025-05-20 18:18:29,456 - __main__ - INFO - ===== 开始将debug.json修复并保存到outline.json (章节1) =====
2025-05-20 18:18:29,456 - __main__ - INFO - 检测到UTF-8 BOM头，将移除
2025-05-20 18:18:29,457 - __main__ - INFO - 成功读取debug.json，长度: 1649字符
2025-05-20 18:18:29,457 - __main__ - INFO - 应用JSON修复...
2025-05-20 18:18:29,458 - __main__ - INFO - 修复后的JSON已保存到: D:\ai\novel\output\fixed_debug.json (仅用于调试)
2025-05-20 18:18:29,459 - __main__ - ERROR - 修复后的JSON解析失败: Expecting ',' delimiter: line 1 column 29 (char 28)
2025-05-20 18:19:07,768 - __main__ - INFO - ===== 开始将debug.json修复并保存到outline.json (章节1) =====
2025-05-20 18:19:07,768 - __main__ - INFO - 检测到UTF-8 BOM头，将移除
2025-05-20 18:19:07,769 - __main__ - INFO - 成功读取debug.json，长度: 1649字符
2025-05-20 18:19:07,769 - __main__ - INFO - 应用JSON修复...
2025-05-20 18:19:07,770 - __main__ - INFO - 修复后的JSON已保存到: D:\ai\novel\output\fixed_debug.json (仅用于调试)
2025-05-20 18:19:07,770 - __main__ - ERROR - 修复后的JSON解析失败: Expecting ',' delimiter: line 1 column 29 (char 28)
2025-05-20 18:37:48,223 - __main__ - INFO - ===== 开始直接测试第2章大纲生成 =====
2025-05-20 18:37:48,223 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 18:37:48,224 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 18:38:10,993 - __main__ - INFO - 第2章大纲直接生成成功
2025-05-20 19:11:19,818 - __main__ - INFO - ===== 开始直接测试第2章大纲生成 =====
2025-05-20 19:11:19,818 - __main__ - INFO - 找到必要的项目文件，尝试加载...
2025-05-20 19:11:19,819 - __main__ - WARNING - 加载项目失败，尝试手动设置必要属性...
2025-05-20 19:12:07,546 - __main__ - INFO - 第2章大纲直接生成成功

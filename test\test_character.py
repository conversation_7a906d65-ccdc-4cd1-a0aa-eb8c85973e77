#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
from models.character import Character, <PERSON><PERSON><PERSON><PERSON>

def test_character_firstappearance():
    print("=== 测试不同角色类型的首次出场章节 ===")
    
    # 测试主角 - 应该总是在第1章出场
    test_char1 = Character(name='测试主角', role='主角', first_appearance_chapter=3)
    print(f"主角（设置首次出场章节为3）: {test_char1.first_appearance_chapter}  // 预期为1，主角总是从第1章出场")
    
    # 测试女主 - 默认在第2章出场
    test_char2 = Character(name='测试女主', role='女主')
    print(f"女主（未设置首次出场章节）: {test_char2.first_appearance_chapter}  // 预期为2，女主默认在第2章出场")
    
    # 测试女主 - 可以显式指定首次出场章节
    test_char3 = Character(name='测试女主2', role='女主', first_appearance_chapter=5)
    print(f"女主（设置首次出场章节为5）: {test_char3.first_appearance_chapter}  // 预期为5，显式设置生效")
    
    # 测试配角 - 使用默认值
    test_char4 = Character(name='测试配角', role='主要配角')
    print(f"配角（未设置首次出场章节）: {test_char4.first_appearance_chapter}  // 预期为1，默认值")
    
    # 测试配角 - 指定首次出场章节
    test_char5 = Character(name='测试配角2', role='次要配角', first_appearance_chapter=7)
    print(f"配角（设置首次出场章节为7）: {test_char5.first_appearance_chapter}  // 预期为7，显式设置生效")
    
    print("\n=== 测试to_dict和from_dict方法 ===")
    # 转换为字典并打印
    test_char_dict = test_char5.to_dict()
    print("字典包含first_appearance_chapter字段:")
    print(f"first_appearance_chapter: {test_char_dict.get('first_appearance_chapter')}  // 预期为7")
    
    # 从字典中恢复
    recovered_char = Character.from_dict(test_char_dict)
    print("\n从字典恢复后保留first_appearance_chapter值:")
    print(f"首次出场章节: {recovered_char.first_appearance_chapter}  // 预期为7")
    
    print("\n=== 测试CharacterManager ===")
    manager = CharacterManager()
    
    # 添加不同首次出场章节的角色
    manager.add_character(Character('主角', '主角'))
    manager.add_character(Character('女主', '女主'))
    manager.add_character(Character('配角A', '主要配角', first_appearance_chapter=3))
    manager.add_character(Character('配角B', '次要配角', first_appearance_chapter=5))
    manager.add_character(Character('反派', '主要反派', first_appearance_chapter=7))
    
    print("\n测试add_character_from_dict方法:")
    char_dict = {
        "name": "通过字典添加的角色",
        "role": "次要配角",
        "first_appearance_chapter": 8
    }
    manager.add_character_from_dict(char_dict)
    char = manager.get_character("通过字典添加的角色")
    print(f"添加的角色首次出场章节: {char.first_appearance_chapter}  // 预期为8")
    
    # 确保determine_character_appearance函数检查首次出场章节
    # 通过手动设置值来模拟
    char_for_appearance = Character('测试出场', '次要配角', first_appearance_chapter=10)
    
    # 获取出场角色列表
    print("\n测试出场角色列表 (当前为第5章):")
    appearing_chars = []
    for character in manager.characters:
        if character.name != "通过字典添加的角色":  # 跳过前面字典添加的角色
            print(f"- {character.name}: 首次出场章节{character.first_appearance_chapter}，是否出场: ", end="")
            should_appear = current_chapter = 5
            if current_chapter >= character.first_appearance_chapter:
                print("有资格出场")
            else:
                print("不能出场 (章节未达到)")

if __name__ == '__main__':
    test_character_firstappearance() 
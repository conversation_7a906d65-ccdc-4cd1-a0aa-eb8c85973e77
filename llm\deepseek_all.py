"""
DeepSeek API全功能模块
整合了DeepSeek API的所有功能，替代Gemini API
"""

import json
import requests
import re
import time
from typing import List, Dict, Any, Optional, Union, Tuple

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import (DEEPSEEK_API_URL, DEEPSEEK_API_KEY, MAX_RETRIES, TIMEOUT,
                   DEFAULT_TEMPERATURE, CREATIVE_TEMPERATURE, ANALYTICAL_TEMPERATURE, FACTUAL_TEMPERATURE, MIN_CHAPTER_LENGTH)
from llm.deepseek import DeepSeekAPI

class DeepSeekAllAPI(DeepSeekAPI):
    """DeepSeek API全功能封装，包含所有原Gemini功能"""

    def __init__(self):
        """初始化DeepSeek API调用封装"""
        super().__init__()
        # 添加背景设定白名单缓存
        self._bg_whitelist_cache = {}  # 使用字典缓存不同背景设定的白名单

    def _extract_background_whitelist(self, background: str) -> set:
        """
        从背景设定中提取允许的专有名词、地名、设定等，形成白名单
        
        Args:
            background: 背景设定文本或JSON
            
        Returns:
            白名单集合，包含背景设定中允许的所有专有名词、地名等
        """
        import re
        import json
        # 计算背景设定的哈希值作为缓存键
        import hashlib
        bg_hash = hashlib.md5(background.encode('utf-8')).hexdigest()
        
        # 如果缓存中已有该背景设定的白名单，直接返回
        if bg_hash in self._bg_whitelist_cache:
            print(f"使用缓存的背景设定白名单（{len(self._bg_whitelist_cache[bg_hash])}个术语）")
            return self._bg_whitelist_cache[bg_hash]
            
        # 否则重新提取
        allowed_terms = set()
        try:
            # 假设背景为JSON或结构化文本，优先解析
            if background.strip().startswith('{'):
                bg_data = json.loads(background)
                # 提取所有字符串型字段
                def extract_terms(obj):
                    if isinstance(obj, dict):
                        for v in obj.values():
                            yield from extract_terms(v)
                    elif isinstance(obj, list):
                        for v in obj:
                            yield from extract_terms(v)
                    elif isinstance(obj, str):
                        # 按中文/英文词语分割
                        for term in re.findall(r'[\u4e00-\u9fa5]{2,}|[A-Za-z][A-Za-z0-9_\-]{2,}', obj):
                            yield term
                allowed_terms = set(extract_terms(bg_data))
            else:
                # 纯文本背景，按词语分割
                allowed_terms = set(re.findall(r'[\u4e00-\u9fa5]{2,}|[A-Za-z][A-Za-z0-9_\-]{2,}', background))
        except Exception as e:
            print(f"提取背景设定白名单时出错: {e}")
            allowed_terms = set(re.findall(r'[\u4e00-\u9fa5]{2,}|[A-Za-z][A-Za-z0-9_\-]{2,}', background))
            
        # 过滤过短、无意义词
        allowed_terms = {t for t in allowed_terms if len(t) > 1}
        
        # 缓存结果
        self._bg_whitelist_cache[bg_hash] = allowed_terms
        print(f"已从背景设定中提取{len(allowed_terms)}个允许使用的术语并缓存")
        
        return allowed_terms

    def generate_main_storyline(self, genre: str, style_guide: str, background: str, total_chapters: int, original_storyline: str = None, force_regenerate: bool = False) -> Optional[str]:
        """
        生成故事主线概述

        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            total_chapters: 总章节数
            original_storyline: 原有的故事主线，用于重新生成时参考
            force_regenerate: 是否强制完全重新生成，不参考原有内容

        Returns:
            故事主线概述，如果出错则返回None
        """
        base_prompt = f"""
        请根据以下男频网络小说信息，生成一个详细的结构化故事主线：

        流派：{genre}
        风格指南：{style_guide}
        背景设定：{background}
        总章节数：{total_chapters}

        请提供一个完整的故事主线规划，以JSON格式输出，必须包含以下内容：
        1. 故事标题
        2. 每一章的详细内容规划（从第1章到第{total_chapters}章，每章内容简约描述大约100字，不能遗漏任何章节）

        【JSON格式要求】
        必须严格遵循以下JSON结构：
        ```json
        {{
          "story_title": "小说标题",
          "outlines": [
            {{
              "index": 1,
              "title": "第一章的建议标题",
              "content": "本章内容简要描述（100字）"
            }},
            // 第2章到第{total_chapters-1}章的内容（不要使用省略号，必须完整列出所有章节）
            {{
              "index": {total_chapters},
              "title": "最终章的建议标题",
              "content": "最终章内容简要描述（100字）"
            }}
          ]
        }}
        ```

        【重要格式说明】：
        1. index必须是整数类型，不能是字符串，例如应该是"index": 1而不是"index": "1"
        2. outlines数组必须包含从第1章到第{total_chapters}章的所有章节，不能遗漏任何章节
        3. 严格确保生成确切的{total_chapters}章，不能多也不能少
        4. 这是硬性要求，必须严格遵守，否则将导致系统错误
        """

        # 如果有原有的故事主线且不是强制重新生成，则基于原有内容进行修改
        if original_storyline and not force_regenerate:
            prompt = f"""
            请根据以下男频网络小说信息，修改并结构化现有的故事主线：

            流派：{genre}
            风格指南：{style_guide}
            背景设定：{background}
            总章节数：{total_chapters}

            【现有故事主线】：
            {original_storyline}

            请基于现有的故事主线进行修改和完善，将其转换为结构化的JSON格式。保留原有故事主线中的核心元素、主要角色和关键情节，但解决以下问题：
            1. 修复与背景设定的任何冲突或不一致
            2. 确保故事结构更加合理和完整
            3. 增强故事的吸引力和紧凑性
            4. 确保为每一章都提供详细的内容指导

            【JSON格式要求】
            必须严格遵循以下JSON结构：
            ```json
            {{
              "story_title": "小说标题",
              "outlines": [
                {{
                  "index": 1,
                  "title": "第一章的建议标题",
                  "content": "本章内容简要描述（100字）"
                }},
                // 第2章到第{total_chapters-1}章
                {{
                  "index": {total_chapters},
                  "title": "最终章的建议标题",
                  "content": "最终章内容简要描述（100字）"
                }}
              ]
            }}
            ```

            【重要格式及内容说明】：
            1. index必须是整数类型，不能是字符串，例如应该是"index": 1而不是"index": "1"
            2. outlines数组必须包含从第1章到第{total_chapters}章的所有章节，不能遗漏任何章节
            3. 严格确保生成确切的{total_chapters}章，不能多也不能少
            4. 这是硬性要求，必须严格遵守，否则将导致系统错误
            5. 如果现有故事主线章节数量不符合要求，请完整重新创建所有{total_chapters}章的内容
            """
        else:
            prompt = base_prompt

        result = self.generate_content(prompt, temperature=CREATIVE_TEMPERATURE, max_tokens=8000)
        if not result:
            return None

        # 规范化结果，确保只包含story_title和outlines字段
        try:
            # 提取JSON
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            json_str = json_match.group(1) if json_match else result

            try:
                # 尝试解析JSON
                data = json.loads(json_str)

                # 规范化数据 - 只保留必要字段
                clean_data = {
                    "story_title": data.get("story_title", "未知标题")
                }

                # 处理章节大纲
                outlines = []
                if "outlines" in data and isinstance(data["outlines"], list):
                    outlines = data["outlines"]
                elif "chapter_outlines" in data and isinstance(data["chapter_outlines"], list):
                    outlines = data["chapter_outlines"]

                # 检查章节数量
                if len(outlines) != total_chapters:
                    print(f"警告：生成的章节数量({len(outlines)})与要求的章节数量({total_chapters})不符，将进行完全重新生成")

                    # 与之前不同，现在我们不再尝试补充缺失的章节，而是进行完全重新生成
                    return self.generate_main_storyline(genre, style_guide, background, total_chapters, None, True)

                # 添加规范化的outlines字段
                clean_data["outlines"] = outlines

                # 验证最终章节数量
                final_chapters_count = len(clean_data["outlines"])
                if final_chapters_count != total_chapters:
                    print(f"警告：最终章节数量({final_chapters_count})仍与要求的章节数量({total_chapters})不符，将再次尝试完全重新生成")
                    return self.generate_main_storyline(genre, style_guide, background, total_chapters, None, True)
                else:
                    print(f"最终生成的主线包含{final_chapters_count}章，符合要求")

                # 返回干净的JSON字符串
                return json.dumps(clean_data, ensure_ascii=False, indent=2)

            except json.JSONDecodeError as e:
                print(f"解析JSON失败: {e}")
                # 如果解析失败，使用简单的修复方法
                try:
                    # 使用辅助函数修复JSON
                    fixed_data = self._fix_json(json_str)
                    if fixed_data:
                        # 规范化并清理
                        clean_data = {
                            "story_title": fixed_data.get("story_title", "未知标题"),
                            "outlines": fixed_data.get("outlines", fixed_data.get("chapter_outlines", []))
                        }

                        # 检查修复后的章节数量
                        if len(clean_data["outlines"]) != total_chapters:
                            print(f"警告：修复后的章节数量({len(clean_data['outlines'])})仍与要求的章节数量({total_chapters})不符，将进行完全重新生成")
                            return self.generate_main_storyline(genre, style_guide, background, total_chapters, None, True)

                        return json.dumps(clean_data, ensure_ascii=False, indent=2)
                except Exception as e2:
                    print(f"修复JSON失败: {e2}")

        except Exception as e:
            print(f"处理生成的故事主线时出错: {e}")

        # 如果所有处理尝试都失败，尝试重新生成
        if not force_regenerate:
            print("所有处理尝试失败，将进行完全重新生成")
            return self.generate_main_storyline(genre, style_guide, background, total_chapters, None, True)

        # 如果已经是强制重新生成模式仍然失败，返回原始结果
        return result

    def generate_novel_style(self, genre: str, target_length: int = None, total_chapters: int = None) -> Optional[str]:
        """
        生成小说风格提示词

        Args:
            genre: 小说流派
            target_length: 目标小说总长度（字数），默认为None
            total_chapters: 总章节数，默认为None

        Returns:
            风格提示词，如果出错则返回None
        """
        # 导入必要的模块
        import math
        from config import DEFAULT_CHAPTER_LENGTH, TARGET_NOVEL_LENGTH

        # 设置默认值
        if target_length is None:
            target_length = TARGET_NOVEL_LENGTH

        # 如果未提供总章节数，则计算
        if total_chapters is None:
            total_chapters = math.ceil(target_length / DEFAULT_CHAPTER_LENGTH)

        prompt = """
        请为一部男频网络小说的{0}流派生成详细的风格提示词。

        小说基本信息：
        - 总字数：约{1}字
        - 总章节数：{2}章

        提示词应包含以下内容：
        1. 该流派的文风特点
        2. 常用写作手法和套路
        3. 情节发展模式
        4. 角色塑造特点
        5. 世界观设定要点
        6. 读者期望

        请以结构化方式输出，便于后续创作使用。
        """.format(genre, target_length, total_chapters)

        return self.generate_content(prompt, temperature=0.7)

    def generate_background(self, genre: str, style_guide: str) -> Optional[Dict[str, Any]]:
        """
        生成小说背景设定

        Args:
            genre: 小说流派
            style_guide: 风格指南

        Returns:
            背景设定字典，包含总体内容和分类内容，如果出错则返回None
        """
        prompt = """
        根据以下男频网络小说的流派和风格指南，生成详细的背景设定：

        流派：{0}
        风格指南：{1}

        请为这部小说创建完整且丰富的背景设定，需要分为以下几个类别，每个类别至少300字：

        1. 世界观（world_view）
            - 时代背景（具体年代、历史时期或架空世界的时间线）
            - 世界运行的基本规则（如有魔法、超能力等特殊元素）
            - 世界的整体格局和发展阶段

        2. 地理环境（geography）
            - 大陆分布、主要地形地貌
            - 重要的自然景观和地标
            - 气候特征与自然规律（可能的特殊天象、季节变化）

        3. 国家/区域（countries）
            - 主要国家、城市、区域的名称和特点
            - 各国家/区域之间的关系和边界
            - 主要国家的政治体制和统治者

        4. 组织/势力（organizations）
            - 主要门派、公会、宗教组织等
            - 各组织的宗旨、特点和影响力
            - 组织之间的关系和冲突
            - 至少10个组织/势力

        5. 重要物品/装备（items）
            - 世界中的特殊物品、武器、装备、绝学、武学、秘籍
            - 珍稀资源和材料
            - 具有特殊意义或力量的宝物
            - 至少30个重要物品/装备

        6. 修炼体系（cultivation）
            - 力量体系和等级划分
            - 修炼方法和境界
            - 特殊能力和技能体系

        7. 种族设定（races）
            - 世界中存在的种族及其特点
            - 种族之间的关系和地位差异
            - 特殊种族的能力和限制

        8. 历史背景（history）
            - 重要的历史事件和战争
            - 影响世界格局的重大变革
            - 古老的传说和预言
            - 至少10个重要历史事件和战争或预言

        9. 文化习俗（culture）
            - 主要文化特点和风俗习惯
            - 宗教信仰和禁忌
            - 节日庆典和传统活动

        10. 世界规则/法则（rules）
            - 特殊的自然法则或魔法规则
            - 社会规范和道德准则
            - 禁忌和限制

        请以JSON格式输出，每个类别作为一个键值对，同时提供一个总体内容的概述。确保所有设定相互协调，没有逻辑矛盾。
        所有设定必须符合{0}类型小说的特点，并与风格指南保持一致。

        输出格式示例：
        ```json
        {{
            "content": "总体背景设定概述...",
            "categories": {{
                "world_view": "详细的世界观设定...",
                "geography": "详细的地理环境设定...",
                "countries": "详细的国家/区域设定...",
                "organizations": "详细的组织/势力设定...",
                "items": "详细的重要物品/装备设定...",
                "cultivation": "详细的修炼体系设定...",
                "races": "详细的种族设定...",
                "history": "详细的历史背景设定...",
                "culture": "详细的文化习俗设定...",
                "rules": "详细的世界规则/法则设定..."
            }}
        }}
        ```
        """.format(genre, style_guide)

        response = self.generate_content(prompt, temperature=0.7)

        if not response:
            return None

        try:
            # 尝试提取JSON部分
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                response = json_match.group(1)

            # 解析JSON
            import json
            background_data = json.loads(response)

            # 确保有必要的字段
            if "content" not in background_data:
                background_data["content"] = "未提供总体背景设定"

            if "categories" not in background_data:
                background_data["categories"] = {}

            # 确保所有类别都存在
            required_categories = [
                "world_view", "geography", "countries", "organizations",
                "items", "cultivation", "races", "history", "culture", "rules"
            ]

            for category in required_categories:
                if category not in background_data["categories"]:
                    background_data["categories"][category] = ""

            return background_data
        except Exception as e:
            print(f"解析背景设定JSON时出错: {str(e)}")

            # 如果解析失败，创建一个基本结构
            return {
                "content": response,
                "categories": {
                    "world_view": "", "geography": "", "countries": "",
                    "organizations": "", "items": "", "cultivation": "",
                    "races": "", "history": "", "culture": "", "rules": ""
                }
            }

    def generate_new_characters(self, chapter_outline: str, characters_info: str, background: str) -> Optional[str]:
        """
        根据章节大纲生成新角色

        Args:
            chapter_outline: 章节大纲
            characters_info: 现有人物信息
            background: 背景设定

        Returns:
            新角色信息的JSON字符串，如果出错则返回None
        """
        prompt = f"""
        根据以下章节大纲和现有人物信息，为小说创建新角色：

        【章节大纲】
        {chapter_outline}

        【现有人物信息】
        {characters_info}

        【背景设定】
        {background}

        请根据章节大纲的需要，设计0-3个新角色。这些角色应该与大纲中描述的情节和场景相符，能够自然地融入故事。

        请以JSON格式输出新角色信息：
        ```json
        {{
            "new_characters": [
                {{
                    "name": "角色名",
                    "role": "角色类型（主角/女主/主要配角/次要配角/主要反派/次要反派）",
                    "basic_info": {{
                        "age": "年龄",
                        "gender": "性别",
                        "occupation": "职业"
                    }},
                    "appearance": "外貌描述",
                    "personality": "性格特点",
                    "abilities": ["能力1", "能力2"],
                    "background": "背景故事",
                    "motivation": "动机",
                    "relationships": {{
                        "已有角色名1": ["关系描述"],
                        "已有角色名2": ["关系描述"]
                    }},
                    "growth_path": "成长路径",
                    "current_status": "当前状态",
                    "current_power": "当前战力"
                }}
            ]
        }}
        ```

        重要格式说明：
        1. 所有字段必须使用双引号，不能使用单引号
        2. 字符串中如有引号，必须使用反斜杠转义，如 "他说\\"你好\\""
        3. relationships字段必须使用如下格式：{{"角色名1": ["关系1", "关系2"], "角色名2": ["关系3"]}}
        4. 不要在JSON中使用注释（如//开头的注释）
        5. 确保所有字段的值格式正确，不要有多余的逗号或缺少逗号

        如果章节大纲中没有需要新角色的情节，可以返回空数组：
        ```json
        {{
            "new_characters": []
        }}
        ```

        请确保新角色的设计符合男频网络小说的特点，并且能够为故事增添新的元素和可能性。
        """

        result = self.generate_content(prompt, temperature=0.7)

        # 提取JSON部分
        if result:
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                return json_match.group(1)
            else:
                return result

        return None

    def generate_outline(self, genre: str, style_guide: str, background: str,
                         current_chapter: int, total_chapters: int,
                         previous_chapters_summary: Optional[str] = None,
                         characters_info: Optional[str] = None,
                         foreshadowing_info: Optional[str] = None,
                         appearing_characters: Optional[str] = None,
                         main_storyline: Optional[str] = None,
                         recent_scenes: Optional[str] = None,
                         already_planted_foreshadowings: Optional[str] = None,
                         foreshadowings_to_plant: Optional[str] = None,
                         foreshadowings_to_reveal: Optional[str] = None) -> Optional[str]:
        """
        生成章节大纲

        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            current_chapter: 当前章节号
            total_chapters: 总章节数
            previous_chapters_summary: 前几章摘要（可选）
            characters_info: 人物卡片（可选）
            foreshadowing_info: 伏笔管理（可选）
            appearing_characters: 本章出场人物（可选）
            main_storyline: 故事主线概述（可选）
            recent_scenes: 最近章节出现的场景列表（可选）
            already_planted_foreshadowings: 已埋下的伏笔列表（可选）
            foreshadowings_to_plant: 需要在本章埋下的伏笔列表（可选）
            foreshadowings_to_reveal: 需要在本章回收的伏笔列表（可选）

        Returns:
            章节大纲，JSON格式，如果出错则返回None
        """
        # 从主线中提取当前章节的内容指导
        chapter_guide = None
        chapter_type_guidance = ""
        chapter_specific_guidance = ""
        
        # 安全检查：处理章节号
        try:
            current_chapter = int(current_chapter)
        except (ValueError, TypeError):
            print(f"警告：章节号 '{current_chapter}' 无效，使用默认值1")
            current_chapter = 1
            
        # 计算当前进度百分比
        progress = current_chapter / total_chapters if total_chapters > 0 else 0
            
        if main_storyline and main_storyline != "无":
            try:
                chapter_guide = self._extract_chapter_from_storyline(main_storyline, current_chapter)
                if chapter_guide:
                    chapter_specific_guidance = f"""
        【主线中的本章内容指导】：
        {chapter_guide}

        【重要】：本章大纲必须严格遵循上述主线内容指导，包括关键事件、出场人物和伏笔安排。
        """
            except Exception as e:
                print(f"从主线提取章节内容时出错: {e}")
                pass
        
        # 根据章节位置确定章节类型指导
        if current_chapter == 1:
            chapter_type_guidance = """
        【特别说明】：
        这是小说的第一章，需要：
        - 引入主要角色和故事世界
        - 建立基本冲突
        - 设定小说的基调和节奏
        - 吸引读者继续阅读
        """
        elif current_chapter == total_chapters:
            chapter_type_guidance = """
        【特别说明】：
        这是小说的最后一章，需要：
        - 解决主要冲突
        - 完成主角的成长弧线
        - 处理所有主要伏笔
        - 给读者一个令人满意的结局
        """
        elif current_chapter / total_chapters < 0.25:
            chapter_type_guidance = """
        【特别说明】：
        这是小说的开端部分，需要：
        - 继续发展主要角色
        - 扩展故事世界
        - 加深冲突和挑战
        - 铺设重要伏笔
        """
        elif current_chapter / total_chapters > 0.75:
            chapter_type_guidance = """
        【特别说明】：
        这是小说的结尾部分，需要：
        - 推动故事朝着高潮发展
        - 开始解决次要冲突
        - 回收前面埋下的伏笔
        - 为最终结局做准备
        """
        else:
            chapter_type_guidance = """
        【特别说明】：
        这是小说的中段，需要：
        - 加剧冲突和障碍
        - 推动角色成长
        - 丰富次要情节线
        - 维持读者兴趣
        """
        
        # 如果提供了特殊伏笔要求，添加到提示词中
        if foreshadowings_to_plant:
            chapter_specific_guidance += f"""
        【本章必须埋下的伏笔】：
        {foreshadowings_to_plant}

        请确保在大纲中明确计划如何埋下这些伏笔，这是必须的。
        """
        
        if foreshadowings_to_reveal:
            chapter_specific_guidance += f"""
        【本章必须回收的伏笔】：
        {foreshadowings_to_reveal}

        请确保在大纲中明确计划如何回收这些伏笔，这是必须的。
        """
        
        # 处理格式化字符串中可能出现的问题字符
        # 避免出现类似 KeyError: '\n "title"' 的格式化错误
        try:
            # 安全处理，防止格式化时出现问题
            prompt_template = """
        请为以下设定的小说创作第{0}章的大纲。

        流派：{genre}
        风格指南：{style_guide}
        背景设定：{background}
        当前章节：第{0}章（共{total_chapters}章）
        前几章摘要：{previous_summary}
        
        {chapter_type}
        
        {chapter_specific}

        【人物卡片】：
        {characters}

        【伏笔管理】：
        {foreshadowing}

        【重要创作约束】：
        1. 所有出现的概念、地点、组织、物品、种族、能力等必须严格限制在背景设定范围内
        2. 严禁创造背景设定中未提及的新概念、新地点、新组织、新物品、新种族或新能力
        3. 所有角色的能力和成长路径必须符合背景设定中的修炼体系和世界规则
        4. 如无法确定某个元素是否在背景设定中，请选择保守策略，使用明确存在的概念

        请按照以下JSON格式输出章节大纲：

        ```json
        {{
          "title": "第{0}章 标题",
          "chapter_summary": {{
            "opening": "章节开始的场景描述",
            "development": "情节如何推进",
            "climax": "本章的高潮部分",
            "ending": "章节结束的场景描述，为下一章做铺垫"
          }},
          "characters": [
            {{
              "name": "角色1",
              "actions": "角色在本章的行动",
              "emotions": "角色在本章的情感变化"
            }},
            {{
              "name": "角色2",
              "actions": "角色在本章的行动",
              "emotions": "角色在本章的情感变化"
            }}
          ],
          "scenes": [
            "场景1：简短描述",
            "场景2：简短描述",
            "场景3：简短描述"
          ],
          "key_points": [
            "关键情节点1",
            "关键情节点2",
            "关键情节点3"
          ],
          "foreshadowings": {{
            "planted": [
              {{
                "id": "伏笔ID",
                "description": "如何在本章中埋下伏笔"
              }}
            ],
            "revealed": [
              {{
                "id": "伏笔ID",
                "description": "如何在本章中回收伏笔"
              }}
            ]
          }}
        }}
        ```

        {chapter_type}
        {chapter_specific}

        重要要求：
        1. 你必须以有效的JSON格式响应，确保JSON结构完整且符合上述格式
        2. 本章情节必须与前几章有明显区别，不要重复前几章的情节或场景
        3. 确保本章大纲与故事主线概述保持一致，符合当前进度（{1:.1%}）的情节发展要求
        4. title字段必须是完整的章节标题，包含章节序号，例如"第{0}章 英雄归来"，不要省略"第{0}章"的部分
        5. 创造新颖的情节和场景，避免套路化和重复前文的内容
        6. 如果前几章有未解决的冲突或悬念，本章应该推进这些情节，而不是创建完全无关的新情节
        7. 【重要】所有概念、地点、组织、物品、种族、能力等必须来自背景设定中已有的内容
        8. 【重要】不要创造背景设定中未提及的新概念、新地点、新组织、新物品、新种族或新能力
        9. 【重要】所有角色的能力和成长路径必须符合背景设定中的修炼体系和世界规则
        10. 【最重要】伏笔管理规则：
            a. 仅包含在"本章必须埋下的伏笔"和"本章必须回收的伏笔"中明确列出的伏笔
            b. 严格禁止提及或暗示任何未在本章指定的伏笔信息
            c. 不要预先回收应该在未来章节回收的伏笔
            d. 不要埋下任何未被明确要求埋下的伏笔
            e. 对每个需要埋下或回收的伏笔，必须在大纲中明确指出如何处理
        11. 【最重要】只返回JSON格式数据，不要返回其他任何文本或注释
        """
            
            # 准备用于格式化的参数字典
            format_params = {
                "genre": genre,
                "style_guide": style_guide,
                "background": background,
                "total_chapters": total_chapters,
                "previous_summary": previous_chapters_summary or "无",
                "characters": characters_info or "无",
                "foreshadowing": foreshadowing_info or "无",
                "chapter_type": chapter_type_guidance,
                "chapter_specific": chapter_specific_guidance,
            }
            
            # 使用命名参数格式化以避免位置参数的问题
            prompt = prompt_template.format(current_chapter, progress, **format_params)
            
        except KeyError as e:
            print(f"格式化大纲提示词时出现KeyError: {e}")
            # 尝试修复格式化错误
            try:
                # 特殊处理常见的KeyError问题
                if str(e).startswith("'\\n") or "title" in str(e):
                    print("检测到换行+title格式错误，尝试修复...")
                    # 使用简化的提示词模板
                    prompt = f"""
                请为以下设定的小说创作第{current_chapter}章的大纲。
                流派：{genre}
                请输出JSON格式的大纲，包含标题、章节摘要、人物、场景和关键情节点。
                """
                else:
                    # 其他未知的KeyError
                    raise
            except Exception as e2:
                print(f"尝试修复格式化错误时出现新错误: {e2}")
                return None
        except Exception as e:
            print(f"格式化大纲提示词时出错: {e}")
            return None

        result = self.generate_content(prompt, temperature=CREATIVE_TEMPERATURE, max_tokens=8000)

        if result:
            try:
                # 尝试解析JSON，确保结构有效
                if result.strip().startswith("{") and result.strip().endswith("}"):
                    # 在解析前清理JSON字符串
                    cleaned_result = self._clean_json_string(result)
                    json_data = json.loads(cleaned_result)
                    return cleaned_result
                else:
                    # 尝试从文本中提取JSON
                    import re
                    json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(1)
                        # 在解析前清理JSON字符串
                        cleaned_json_str = self._clean_json_string(json_str)
                        json_data = json.loads(cleaned_json_str)
                        return cleaned_json_str
                    else:
                        # 如果找不到JSON，尝试修复并返回
                        cleaned_result = self._clean_json_string(result)
                        try:
                            json_data = json.loads(cleaned_result)
                            return cleaned_result
                        except:
                            # 如果仍然无法解析，返回原始结果
                            print("无法解析为JSON，返回原始结果")
                            # 移除Markdown代码块标记后再返回
                            result = re.sub(r'^```\w*\s*', '', result)
                            result = re.sub(r'\s*```\s*$', '', result)
                            return result
            except Exception as e:
                print(f"解析大纲JSON时出错: {e}")
                # 尝试更强力的修复方法
                try:
                    # 首先移除可能的Markdown代码块标记
                    result = re.sub(r'^```\w*\s*', '', result)
                    result = re.sub(r'\s*```\s*$', '', result)
                    
                    cleaned_result = self._clean_json_string(result, aggressive=True)
                    json_data = json.loads(cleaned_result)
                    print("使用强力修复方法成功解析JSON")
                    return cleaned_result
                except Exception as e2:
                    print(f"强力修复JSON失败: {e2}")
                    
                    # 尝试使用更多自定义修复方法
                    try:
                        from utils.fix_storyline import fix_storyline_json
                        from utils.json_helper import fix_json
                        
                        # 移除可能的Markdown代码块标记
                        result = re.sub(r'^```\w*\s*', '', result)
                        result = re.sub(r'\s*```\s*$', '', result)
                        result = re.sub(r'```json\s*(.*?)\s*```', r'\1', result, flags=re.DOTALL)
                        
                        # 尝试多种修复方法
                        print("尝试使用fix_storyline_json修复...")
                        fixed_data = fix_storyline_json(result)
                        if fixed_data:
                            print("使用fix_storyline_json修复成功")
                            return json.dumps(fixed_data, ensure_ascii=False)
                            
                        print("尝试使用fix_json修复...")
                        fixed_json = fix_json(result)
                        if fixed_json:
                            try:
                                json.loads(fixed_json)  # 验证是否有效
                                print("使用fix_json修复成功")
                                return fixed_json
                            except:
                                pass
                                
                    except ImportError:
                        print("修复工具不可用")
                    except Exception as e3:
                        print(f"使用额外修复工具时出错: {e3}")
                    
                    # 最后尝试清除Markdown标记后返回
                    result = re.sub(r'```json\s*(.*?)\s*```', r'\1', result, flags=re.DOTALL)
                    return result

        return result

    def generate_characters(self, genre: str, style_guide: str, background: str, main_storyline: str = None) -> Optional[str]:
        """
        生成人物卡片

        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            main_storyline: 故事主线概述（可选）

        Returns:
            人物卡片，如果出错则返回None
        """
        # 添加故事主线参数
        storyline_part = ""
        if main_storyline:
            storyline_part = f"""
        故事主线概述：{main_storyline}

        【重要要求】：必须全面扫描故事主线，确保创建所有在主线中出现或提及的人物，不管是主要人物还是次要人物，一个都不能遗漏。
        1. 首先仔细阅读故事主线中的所有章节内容
        2. 识别并列出每一个被提及的角色名字（包括只出现一次的角色）
        3. 为每一个识别到的角色创建完整的人物卡片
        4. 确保人物创建的全面性和完整性，与主线故事保持一致
        5. 如果主线中的角色未详细描述，需根据上下文和背景设定合理推断其特征和背景
        """

        prompt = f"""
        根据以下男频网络小说信息，创建详细的人物卡片：

        流派：{genre}
        风格指南：{style_guide}
        背景设定：{background}
        {storyline_part}

        请为这部小说创建所有必要的人物卡片（包括主角、女主、重要配角、次要配角、主要反派、次要反派等所有在主线中出现过的角色），每个人物卡片应包含：
        1. 基本信息（姓名、年龄、性别、身份/职业等）
        2. 外貌特征
        3. 性格特点
        4. 特殊能力/技能
        5. 背景故事
        6. 动机和目标
        7. 与其他角色的关系（格式为数组，不要使用字符串描述）
        8. 成长路径
        9. 当前状态
        10. 当前战力（根据背景设定中的战力等级划分，明确标注角色当前的战力水平）
        11. 首次出场章节（主角默认为1，其他角色可根据故事需要设定）
        12. 出场章节列表（指明该角色应该在哪些章节中出场，必须是数字数组）

        【重要人物创建指南】
        1. 如果有主线故事，必须仔细分析故事主线中的所有章节内容
        2. 确保创建所有在主线中提及的人物角色，即使是只出现一次的次要角色
        3. 确保角色设定与主线故事情节完全匹配，包括他们的能力、背景和出场章节
        4. 特别注意那些在故事发展中可能扮演关键角色的配角，即使他们初期戏份不多
        5. 确保角色数量足够支撑整个故事，一般不少于10个主要角色（包括主角、重要配角和主要反派）

        请以结构化JSON格式输出，便于后续程序处理和更新。格式参考：
        ```json
        {{
            "characters": [
                {{
                    "name": "角色名",
                    "role": "主角/女主/主要配角/次要配角/主要反派/次要反派",
                    "basic_info": {{
                        "age": 25,
                        "gender": "男/女",
                        "identity": "身份/职业"
                    }},
                    "appearance": "外貌描述",
                    "personality": "性格描述",
                    "abilities": ["能力1", "能力2", "能力3"],
                    "background": "背景故事",
                    "motivation": "动机与目标",
                    "relationships": {{
                        "角色名1": ["朋友", "盟友"],
                        "角色名2": ["敌人"],
                        "角色名3": ["师徒"]
                    }},
                    "growth_path": "成长轨迹",
                    "current_status": "当前状态",
                    "current_power": "当前战力",
                    "first_appearance_chapter": 1,
                    "appearance_chapters": [1, 2, 5, 8, 10]
                }},
                // 继续创建所有必要的角色，确保包含主线中提及的所有人物
            ]
        }}
        ```

        【出场章节说明】
        1. 对于主角，建议设置appearance_chapters包括全部或大多数章节
        2. 对于重要角色（如女主、关键配角），应确保在关键章节中出场
        3. 对于次要角色，应适当安排出场章节，符合其在故事中的作用
        4. 出场章节必须是数字数组，格式为[1, 2, 3, ...]
        5. 必须为每个角色安排适当的出场章节
        6. 出场章节安排必须与主线故事情节吻合

        【角色全面性检查】
        在完成人物卡片创建后，请进行如下检查：
        1. 是否包含了主线中每一个提及的角色？（必须确保100%覆盖）
        2. 角色之间的关系网络是否完整且一致？（互相引用的关系必须匹配）
        3. 角色的能力和背景是否与世界观设定一致？
        4. 每个角色的动机和目标是否有助于推动故事发展？
        5. 是否为每个角色分配了合适的出场章节？

        重要格式说明：
        1. 所有字段必须使用双引号，不能使用单引号
        2. 字符串中如有引号，必须使用反斜杠转义，如 "他说\\"你好\\""
        3. relationships字段必须使用如下格式：{{"角色名1": ["关系1", "关系2"], "角色名2": ["关系3"]}}
        4. appearance_chapters字段必须是数字数组，例如：[1, 3, 5, 8]
        5. 不要在JSON中使用注释（如//开头的注释）
        6. 确保所有字段的值格式正确，不要有多余的逗号或缺少逗号

        确保人物设定符合男频网络小说特点，特别是主角应具备吸引力和成长空间。当前战力必须根据背景设定中的战力等级划分来确定，这将是后续章节中人物战力评估的重要依据。
        """

        return self.generate_content(prompt, temperature=0.7)

    def generate_chapter_foreshadowing(self, genre: str, style_guide: str, background: str,
                                    characters: str, chapter_outline: str, chapter_number: int,
                                    total_chapters: int = 50) -> Optional[str]:
        """
        根据章节大纲生成新伏笔

        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            characters: 人物卡片
            chapter_outline: 章节大纲
            chapter_number: 当前章节号
            total_chapters: 总章节数，默认为50

        Returns:
            新伏笔，如果出错则返回None
        """
        prompt = """
        根据以下男频网络小说信息，为当前章节创建新的伏笔：

        流派：{0}
        风格指南：{1}
        背景设定：{2}
        人物卡片：{3}
        当前章节大纲：{4}
        当前章节号：{5}
        总章节数：{6}

        请根据当前章节大纲内容，创建2-3个新的伏笔，这些伏笔应该：
        1. 与当前章节内容紧密相关
        2. 能够在后续章节中回收
        3. 有助于推动故事情节发展
        4. 增加故事的悬念和吸引力

        对每个新伏笔，请提供：
        - 伏笔内容（具体描述）
        - 伏笔类型（主线/角色/世界观/感情线）
        - 埋下伏笔的章节（当前章节）
        - 回收伏笔的预计章节（必须大于当前章节且小于或等于总章节数{6}）
        - 伏笔的重要性（高/中/低）
        - 伏笔埋下方式（如何在当前章节中埋下这个伏笔）
        - 伏笔回收效果（回收时会对情节产生什么影响）
        - 相关角色（与伏笔相关的角色名列表）

        请以结构化JSON格式输出，便于后续程序处理和更新。格式参考：
        ```json
        {{
            "foreshadowings": [
                {{
                    "type": "主线/角色/世界观/感情线",
                    "content": "伏笔内容描述",
                    "plant_chapter": {5},
                    "reveal_chapter": 预计回收章节,
                    "importance": "高/中/低",
                    "plant_method": "埋下方式描述",
                    "reveal_effect": "回收效果描述",
                    "status": "未埋下",
                    "related_characters": ["角色名1", "角色名2"]
                }},
                // 其他伏笔...
            ]
        }}
        ```

        确保伏笔安排合理，能够增强故事的连贯性和吸引力。
        特别注意：所有回收章节号必须大于当前章节号{5}且小于或等于总章节数{6}。
        """.format(genre, style_guide, background, characters, chapter_outline, chapter_number, total_chapters)

        return self.generate_content(prompt, temperature=0.7)

    def generate_foreshadowing(self, genre: str, style_guide: str, background: str,
                                 characters: str, outline: str, total_chapters: int = 50) -> Optional[str]:
        """
        生成伏笔管理

        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            characters: 人物卡片
            outline: 大纲
            total_chapters: 总章节数，默认为50

        Returns:
            伏笔管理，如果出错则返回None
        """
        prompt = """
        根据以下男频网络小说信息，创建详细的伏笔管理计划：

        流派：{0}
        风格指南：{1}
        背景设定：{2}
        人物卡片：{3}
        大纲：{4}
        总章节数：{5}

        请规划整部小说中的伏笔安排，包括：
        1. 主线伏笔（影响主要剧情走向的关键线索）
        2. 角色伏笔（与角色成长、身世相关的线索）
        3. 世界观伏笔（与世界设定相关的隐藏信息）
        4. 感情线伏笔（与感情发展相关的铺垫）

        对每个伏笔，请提供：
        - 伏笔内容
        - 埋下伏笔的预计章节（必须小于或等于总章节数{5}）
        - 回收伏笔的预计章节（必须小于或等于总章节数{5}）
        - 伏笔的重要性（高/中/低）
        - 伏笔埋下方式
        - 伏笔回收效果

        伏笔管理的重要原则：
        1. 所有伏笔必须在小说结束前回收，不能留下未解决的伏笔
        2. 重要伏笔应该在小说的后半部分回收，特别是在高潮和结局部分
        3. 伏笔的回收应该分布在不同章节，避免在单一章节回收过多伏笔
        4. 主线伏笔的回收应该对剧情产生重大影响，特别是对结局的走向
        5. 最终章节应该回收最重要的伏笔，并与小说结局紧密相关
        6. 所有章节号必须在1到{5}之间，不能超出这个范围

        请以结构化JSON格式输出，便于后续程序处理和更新。格式参考：
        ```json
        {{
            "foreshadowings": [
                {{
                    "id": "f1",
                    "type": "主线",
                    "content": "伏笔内容描述",
                    "plant_chapter": 5,
                    "reveal_chapter": 20,
                    "importance": "高",
                    "plant_method": "埋下方式描述",
                    "reveal_effect": "回收效果描述",
                    "status": "未埋下",
                    "related_characters": ["角色名1", "角色名2"]
                }},
                {{
                    "id": "f2",
                    "type": "角色",
                    "content": "另一个伏笔内容描述",
                    "plant_chapter": 8,
                    "reveal_chapter": 25,
                    "importance": "中",
                    "plant_method": "埋下方式描述",
                    "reveal_effect": "回收效果描述",
                    "status": "未埋下",
                    "related_characters": ["角色名3"]
                }}
            ]
        }}
        ```

        【重要】JSON格式要求：
        1. 所有字段名必须使用双引号，不能使用单引号
        2. 字符串值必须使用双引号，不能使用单引号
        3. 数值不需要引号（如章节号）
        4. 数组和对象的最后一项后面不要加逗号
        5. 不要在JSON中添加注释
        6. 确保JSON格式严格正确，特别是逗号的使用

        确保伏笔安排合理，能够增强故事的连贯性和吸引力，并且所有伏笔都能在小说结束前得到回收和解释。
        特别注意：所有章节号必须在1到{5}之间，不能超出这个范围。
        """.format(genre, style_guide, background, characters, outline, total_chapters)

        return self.generate_content(prompt, temperature=0.7)

    def check_chapter_word_count(self, chapter_content: str) -> Dict[str, Any]:
        """
        检查章节字数

        Args:
            chapter_content: 章节内容

        Returns:
            检查结果字典
        """
        # 直接计算文本字数（中文每个字符算一个字，英文单词或数字算一个字）
        # 对于中英混合文本，我们按照中文习惯计算字数
        text = chapter_content.strip()
        
        # 移除标点符号和空白字符来计算实际字数
        import re
        # 保留中英文字符和数字，移除标点和空白
        clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', text)
        word_count = len(clean_text)
        
        # 严格判断字数是否在要求范围内
        passed = True
        issues = []
        
        # 字数不足4000字，严格不通过
        if word_count < 4000:
            passed = False
            issues.append({
                "type": "字数不足",
                "description": f"章节字数为{word_count}字，少于最低要求的4000字",
                "suggestion": "请扩展章节内容，使字数至少达到4000字"
            })
        # 字数超过8000字，标记为提醒但仍然通过
        elif word_count > 8000:
            issues.append({
                "type": "字数过多",
                "description": f"章节字数为{word_count}字，超过了建议的8000字上限",
                "suggestion": "可以考虑适当精简内容，控制在8000字以内"
            })
            
        # 输出详细的字数信息
        print(f"章节字数: {word_count}字（要求：4000-8000字）")
        if not passed:
            print(f"字数检查未通过：{issues[0]['description']}")
        
        return {
            "passed": passed,
            "word_count": word_count,
            "issues": issues
        }

    def check_chapter_word_count_outline(self, chapter_content: str) -> Dict[str, Any]:
        """
        检查章节字数（generate_from_outline模式专用）

        Args:
            chapter_content: 章节内容

        Returns:
            检查结果字典
        """
        # 直接计算文本字数（中文每个字符算一个字，英文单词或数字算一个字）
        # 对于中英混合文本，我们按照中文习惯计算字数
        text = chapter_content.strip()
        
        # 移除标点符号和空白字符来计算实际字数
        import re
        # 保留中英文字符和数字，移除标点和空白
        clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', text)
        word_count = len(clean_text)
        
        # 严格判断字数是否在要求范围内
        passed = True
        issues = []
        
        # 字数不足2500字，严格不通过
        if word_count < 2500:
            passed = False
            issues.append({
                "type": "字数不足",
                "description": f"章节字数为{word_count}字，少于最低要求的2500字（generate_from_outline模式）",
                "suggestion": "请扩展章节内容，使字数至少达到2500字"
            })
        # 字数超过3000字，标记为提醒但仍然通过
        elif word_count > 3000:
            issues.append({
                "type": "字数过多",
                "description": f"章节字数为{word_count}字，超过了建议的3000字上限（generate_from_outline模式）",
                "suggestion": "可以考虑适当精简内容，控制在3000字以内"
            })
            
        # 输出详细的字数信息
        print(f"章节字数: {word_count}字（generate_from_outline模式要求：2500-3000字）")
        if not passed:
            print(f"字数检查未通过：{issues[0]['description']}")
        
            return {
            "passed": passed,
            "word_count": word_count,
            "issues": issues
            }

    def check_chapter_style(self, chapter_content: str, genre: str) -> Dict[str, Any]:
        """
        检查章节风格

        Args:
            chapter_content: 章节内容
            genre: 小说流派

        Returns:
            检查结果字典
        """
        prompt = """
        请检查以下小说章节的风格是否符合要求：

        【章节内容】
        {0}

        【小说流派】
        {1}

        要求：
        1. 文风应符合男频网络小说特点，具有代入感和爽感
        2. 应符合{1}流派的特点
        3. 语言应流畅，节奏感强，情节有起伏
        4. 描写应生动形象，有画面感

        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "issues": [
                {{
                    "type": "风格问题",
                    "description": "问题详细描述",
                    "location": "问题在文中的大致位置",
                    "suggestion": "修改建议"
                }}
            ]
        }}
        ```

        如果风格符合要求，请返回passed为true且issues为空数组。
        """.format(chapter_content, genre)

        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "description": "风格检查失败",
                    "suggestion": "请重新检查"
                }]
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)
            return check_result
        except Exception as e:
            print(f"解析风格检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "description": f"解析风格检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }]
            }

    def check_characters_storyline(self, characters_info: str, main_storyline: str) -> Dict[str, Any]:
        """
        检查人物是否与故事主线冲突

        Args:
            characters_info: 人物信息
            main_storyline: 故事主线概述

        Returns:
            检查结果字典
        """
        prompt = f"""
        请检查以下小说人物是否与故事主线冲突：

        【人物信息】
        {characters_info}

        【故事主线概述】
        {main_storyline}

        要求：
        1. 人物设定应与故事主线保持一致
        2. 主要人物的背景故事应符合故事主线的背景
        3. 人物的动机和目标应符合故事主线中的角色发展轨迹
        4. 人物之间的关系应符合故事主线中的角色关系
        5. 人物的能力和战力应符合故事主线中的角色能力设定

        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "issues": [
                {{
                    "type": "人物冲突",
                    "character": "角色名",
                    "description": "问题详细描述",
                    "suggestion": "修改建议"
                }}
            ]
        }}
        ```

        如果没有发现与故事主线的冲突，请返回passed为true且issues为空数组。
        """

        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "character": "全部",
                    "description": "人物与故事主线冲突检查失败",
                    "suggestion": "请重新检查"
                }]
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)
            return check_result
        except Exception as e:
            print(f"解析人物与故事主线冲突检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "character": "全部",
                    "description": f"解析人物与故事主线冲突检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }]
            }

    def check_chapter_characters(self, chapter_content: str, characters: str, appearing_characters: str = None) -> Dict[str, Any]:
        """
        检查章节是否与人物设定冲突

        Args:
            chapter_content: 章节内容
            characters: 人物卡片
            appearing_characters: 本章出场人物（可选）

        Returns:
            检查结果字典
        """
        # 尝试解析角色数据以提取有效的角色名
        valid_character_names = []
        try:
            # 尝试解析角色信息，获取有效的角色名列表
            if characters.strip().startswith("{"):
                characters_data = json.loads(characters)
                if "characters" in characters_data and isinstance(characters_data["characters"], list):
                    valid_character_names = [char.get("name", "") for char in characters_data["characters"] if char.get("name")]
            else:
                # 如果不是JSON格式，尝试从文本中提取角色名
                char_matches = re.findall(r'姓名[:：]\s*([^\n,，、。]+)', characters)
                valid_character_names.extend(char_matches)
                char_matches = re.findall(r'名字[:：]\s*([^\n,，、。]+)', characters)
                valid_character_names.extend(char_matches)
                char_matches = re.findall(r'角色名[:：]\s*([^\n,，、。]+)', characters)
                valid_character_names.extend(char_matches)
        except Exception as e:
            print(f"解析角色数据时出错，将使用默认提示词: {str(e)}")
        
        valid_character_names = [name for name in valid_character_names if name and name.strip()]
        
        # 构建提示词
        base_prompt = """
        请检查以下小说章节是否与人物设定冲突：

        【章节内容】
        {0}

        【人物卡片】
        {1}
        """

        # 如果提供了出场人物列表，则只检查这些人物
        if appearing_characters:
            base_prompt += """
        【本章出场人物】
        {2}

        要求：
        1. 只检查本章出场人物的设定冲突
        2. 人物言行应符合其性格设定
        3. 人物能力应符合其设定范围
        4. 人物关系应符合已有设定
        5. 人物当前战力应与章节内容表现一致
        6. 应更新本章出场角色的状态和当前战力
        7. 严格禁止创建新的人物，只更新现有人物状态
        8. 只能使用已有角色名称，不要使用"角色名1"等泛化名称
        """.format(chapter_content, characters, appearing_characters)
        else:
            base_prompt += """
        要求：
        1. 人物言行应符合其性格设定
        2. 人物能力应符合其设定范围
        3. 人物关系应符合已有设定
        4. 人物当前战力应与章节内容表现一致
        5. 应更新本章出场角色的状态和当前战力
        6. 严格禁止创建新的人物，只更新现有人物状态
        7. 只能使用已有角色名称，不要使用"角色名1"等泛化名称
        """.format(chapter_content, characters)

        # 如果成功提取了有效角色名，添加到提示词中
        if valid_character_names:
            base_prompt += """
        【有效角色名列表】
        只能从以下角色名中选择，严禁使用其他名称或泛化名称：
        {}
        """.format(', '.join(valid_character_names))

        base_prompt += """
        请以JSON格式返回检查结果：
        ```json
        {
            "passed": true/false,
            "issues": [
                {
                    "type": "人物冲突",
                    "character": "具体人物名",
                    "description": "冲突描述",
                    "location": "冲突在文中的位置",
                    "suggestion": "修改建议"
                }
            ],
            "character_updates": [
                {
                    "name": "人物名称（必须是已有角色，不要创建新角色）",
                    "updates": {
                        "current_status": "角色当前状态更新"
                    }
                }
            ],
            "appearing_character_names": ["本章出现的人物名称1", "本章出现的人物名称2"]
        }
        ```

        注意：
        1. 所有人物名称必须是已有角色，而不是自创角色
        2. 不要使用"角色名1"、"角色名2"等泛化表示
        3. 如果无法确定某个人物的信息，请不要在更新中包含它
        4. 如果没有发现任何问题，passed设为true且issues为空数组
        5. appearing_character_names应只包含已存在的角色名称
        """

        result = self.generate_content(base_prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "character": "全部",
                    "description": "人物冲突检查失败",
                    "suggestion": "请重新检查"
                }],
                "character_updates": [],
                "appearing_character_names": []
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)

            # 验证角色名是否都在有效列表中
            if valid_character_names:
                # 过滤掉不在有效角色名列表中的角色更新
                filtered_updates = []
                for update in check_result.get("character_updates", []):
                    if update.get("name") in valid_character_names:
                        filtered_updates.append(update)
                    else:
                        print(f"警告：过滤掉无效的角色名 '{update.get('name')}'，该角色不存在")
                
                # 更新结果
                check_result["character_updates"] = filtered_updates
                
                # 过滤出场角色名
                appearing_names = check_result.get("appearing_character_names", [])
                check_result["appearing_character_names"] = [name for name in appearing_names if name in valid_character_names]

            return check_result
        except Exception as e:
            print(f"解析人物冲突检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "character": "全部",
                    "description": f"解析人物冲突检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }],
                "character_updates": [],
                "appearing_character_names": []
            }

    def check_storyline_background(self, main_storyline: str, background: str) -> Dict[str, Any]:
        """
        检查故事主线是否与背景设定冲突

        Args:
            main_storyline: 故事主线概述
            background: 背景设定

        Returns:
            检查结果字典
        """
        # 1. 先使用提取的背景设定白名单进行严格检查
        print("正在对故事主线进行背景设定白名单检查...")
        import re
        issues = []
        passed = True
        
        # 获取背景设定白名单（从缓存或重新提取）
        allowed_terms = self._extract_background_whitelist(background)
        
        # 在故事主线内容中查找所有专有名词/地名/设定
        found_terms = set(re.findall(r'[\u4e00-\u9fa5]{2,}|[A-Za-z][A-Za-z0-9_\-]{2,}', main_storyline))
        
        # 过滤背景设定允许的
        for term in found_terms:
            if term not in allowed_terms:
                issues.append({
                    "type": "背景设定违规",
                    "description": f"检测到故事主线中出现背景设定之外的概念：{term}",
                    "suggestion": "请严格使用背景设定内的概念，移除或替换违规内容"
                })
                passed = False
        
        # 如果白名单检查发现问题，直接返回结果
        if not passed:
            print(f"故事主线背景设定白名单检查未通过，发现{len(issues)}个违规概念")
            return {"passed": passed, "issues": issues}
        
        # 2. 如果白名单检查通过，使用LLM进行更全面的逻辑一致性检查
        prompt = f"""
        请严格检查以下小说故事主线是否与背景设定冲突：

        【故事主线概述】
        {main_storyline}

        【背景设定】
        {background}

        要求：
        1. 故事主线不应与背景设定冲突
        2. 故事主线中的世界观、规则、地理环境等应符合背景设定
        3. 故事主线中的组织、势力、种族等应符合背景设定
        4. 故事主线中不应出现背景设定中未提及的概念、地点、组织、物品、种族或能力
        5. 所有角色的能力和成长路径必须符合背景设定中的修炼体系和世界规则
        6. 故事发生的地点必须是背景设定中已经描述过的地点
        7. 故事中的冲突必须基于背景设定中已有的势力、种族或组织之间的关系

        请特别注意检查故事主线中是否出现了背景设定中未提及的概念，这是最严重的问题。
        【重要】：已进行初步白名单检查，未发现明显的违规概念，请进一步检查逻辑冲突和设定一致性问题。

        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "issues": [
                {{
                    "type": "背景冲突",
                    "description": "问题详细描述",
                    "suggestion": "修改建议"
                }}
            ]
        }}
        ```

        如果没有发现背景冲突，请返回passed为true且issues为空数组。
        如果发现任何背景设定中未提及的概念，请务必标记为问题并提供具体的修改建议。
        """

        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "description": "故事主线与背景设定冲突检查失败",
                    "suggestion": "请重新检查"
                }]
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)
            return check_result
        except Exception as e:
            print(f"解析故事主线与背景设定冲突检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "description": f"解析故事主线与背景设定冲突检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }]
            }

    def check_storyline_style(self, main_storyline: str, style_guide: str) -> Dict[str, Any]:
        """
        检查故事主线是否与风格指南冲突

        Args:
            main_storyline: 故事主线概述
            style_guide: 风格指南

        Returns:
            检查结果字典
        """
        prompt = f"""
        请检查以下小说故事主线是否与风格指南冲突：

        【故事主线概述】
        {main_storyline}

        【风格指南】
        {style_guide}

        要求：
        1. 故事主线应与风格指南保持一致
        2. 故事主线中的文风特点应符合风格指南
        3. 故事主线中的情节发展模式应符合风格指南
        4. 故事主线中的角色塑造特点应符合风格指南
        5. 故事主线中的世界观设定要点应符合风格指南
        6. 故事主线应符合读者期望

        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "issues": [
                {{
                    "type": "风格冲突",
                    "description": "问题详细描述",
                    "suggestion": "修改建议"
                }}
            ]
        }}
        ```

        如果没有发现与风格指南的冲突，请返回passed为true且issues为空数组。
        """

        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "description": "故事主线与风格指南冲突检查失败",
                    "suggestion": "请重新检查"
                }]
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)
            return check_result
        except Exception as e:
            print(f"解析故事主线与风格指南冲突检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "description": f"解析故事主线与风格指南冲突检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }]
            }

    def check_chapter_outline(self, chapter_content: str, chapter_outline: str, foreshadowing: str) -> str:
        """
        检查章节内容是否与章节大纲冲突，以及是否正确处理了伏笔

        Args:
            chapter_content: 章节内容
            chapter_outline: 章节大纲
            foreshadowing: 伏笔信息

        Returns:
            检查结果的JSON字符串，格式如下：
            {
                "passed": true/false,  # 任何problems或issues存在时，passed都为false
                "issues": [
                    {
                        "type": "大纲冲突",  # 问题类型
                        "description": "...",  # 问题描述
                        "location": "...",  # 问题位置
                        "suggestion": "..."  # 修复建议
                    }
                ]
            }
        """
        # 格式化伏笔信息，如果不是空字符串
        foreshadowing_formatted = foreshadowing if isinstance(foreshadowing, str) and foreshadowing.strip() else "无"
            
            # 构建提示词
        prompt = """
        请检查以下章节内容是否与大纲冲突，以及是否正确处理了伏笔：

            【章节大纲】
        {}

        【伏笔信息】
        {}

            【章节内容】
        {}
        
        请严格检查以下几点：
        1. 章节内容是否涵盖了大纲中的所有关键点
        2. 章节内容是否与大纲的设定存在冲突
        3. 章节内容是否正确处理了伏笔的埋下与回收
        4. 人物形象、对话和行为是否与设定一致
        5. 场景描写是否与大纲提供的信息一致
        6. 故事发展是否符合逻辑，有无明显的矛盾或不合理之处
        
        请以JSON格式返回检查结果：
        {{
            "passed": true/false,
            "issues": [
                {{
                    "type": "问题类型",
                    "description": "详细描述问题",
                    "location": "问题位置",
                    "suggestion": "修复建议"
                }}
              ]
            }}
        
        注意：
        - 即使问题不严重，只要发现任何问题都应该将"passed"设为false
        - 任何大纲冲突、伏笔处理错误、逻辑矛盾等都必须记录在"issues"中
        - 如果没有发现问题，则"issues"为空数组，"passed"为true
        """.format(chapter_outline, foreshadowing_formatted, chapter_content)
        
        result = self.generate_content(prompt, temperature=ANALYTICAL_TEMPERATURE)
        if result:
            # 尝试清理并解析JSON结果
            try:
                # 1. 提取JSON部分
                json_match = re.search(r'```(?:json)?\s*({.*?})\s*```', result, re.DOTALL)
                if json_match:
                    result = json_match.group(1)
                
                # 2. 如果开头有多余文本，尝试找到JSON起始位置
                if not result.strip().startswith('{'):
                    json_start = result.find('{')
                    if json_start != -1:
                        result = result[json_start:]
                
                # 3. 如果结尾有多余文本，尝试找到JSON结束位置
                if not result.strip().endswith('}'):
                    json_end = result.rfind('}')
                    if json_end != -1:
                        result = result[:json_end+1]
                
                # 4. 解析JSON
                check_data = json.loads(result)
                
                # 5. 确保passed字段正确设置 - 任何issues存在时都设为false
                if check_data.get("issues") and len(check_data.get("issues", [])) > 0:
                    check_data["passed"] = False
                
                # 6. 返回格式化的JSON
                return json.dumps(check_data, ensure_ascii=False)
            except Exception as e:
                        print(f"解析检查结果时出错: {e}")
        
        # 如果出错或结果为空，返回默认结果
        return json.dumps({"passed": False, "issues": [{"type": "检查失败", "description": "无法检查章节与大纲的一致性", "suggestion": "请重新生成"}]}, ensure_ascii=False)

    def check_chapter_storyline(self, chapter_outline: str, main_storyline: str, current_chapter: int, total_chapters: int) -> Dict[str, Any]:
        """
        检查章节大纲是否与故事主线冲突

        Args:
            chapter_outline: 章节大纲
            main_storyline: 故事主线概述
            current_chapter: 当前章节号
            total_chapters: 总章节数

        Returns:
            检查结果字典
        """
        # 1. 先检查章节大纲是否与背景设定冲突
        # 获取小说背景设定
        # try:
        #     novel_info_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "output", "novel_info.json")
        #     background = ""
        #     if os.path.exists(novel_info_path):
        #         with open(novel_info_path, 'r', encoding='utf-8') as f:
        #             novel_info = json.load(f)
        #             background = novel_info.get("background", "")
            
        #     if background:
        #         print("检查章节大纲是否与背景设定冲突...")
        #         background_check_result = self.check_chapter_background(chapter_outline, background)
        #         if not background_check_result.get("passed", False):
        #             issues = background_check_result.get("issues", [])
        #             print(f"章节大纲背景设定检查未通过，发现{len(issues)}个问题")
        #             return {
        #                 "passed": False,
        #                 "issues": issues,
        #                 "foreshadowing_updates": []
        #             }
        # except Exception as e:
        #     print(f"检查章节大纲与背景设定冲突时出错: {str(e)}")
        
        # 2. 继续检查章节大纲是否与故事主线冲突
        # 计算当前进度
        progress = current_chapter / total_chapters

        # 从主线中提取当前章节的内容指导
        chapter_guide = None
        try:
            if main_storyline and main_storyline != "无":
                chapter_guide = self._extract_chapter_from_storyline(main_storyline, current_chapter)
        except Exception as e:
            print(f"从主线中提取章节内容指导时出错: {str(e)}")

        # 构建提示词
        # 如果成功提取了章节指导，则使用更具体的检查提示词
        if chapter_guide:
            prompt = f"""
                请检查以下小说章节大纲是否与故事主线中指定的本章内容指导一致：

            【章节大纲】
            {chapter_outline}

                【主线中的本章内容指导】
                {chapter_guide}

            【当前进度】
            当前是第{current_chapter}章，总共{total_chapters}章，完成度约为{progress:.1%}

            要求：
                1. 章节大纲必须与主线中指定的本章内容完全一致
                2. 章节大纲必须包含主线中提到的所有关键事件
                3. 章节大纲必须包含主线中指定的所有出场人物
                4. 章节大纲必须按照主线要求埋下和回收相应的伏笔
                5. 虽然章节大纲可以在细节上有所扩展，但不能改变主线的基本方向

            请以JSON格式返回检查结果：
            ```json
            {{
                "passed": true/false,
                "issues": [
                    {{
                        "type": "主线偏离",
                        "description": "问题详细描述",
                        "suggestion": "修改建议"
                    }}
                ]
            }}
            ```

                如果没有发现与主线内容指导的冲突，请返回passed为true且issues为空数组。
            """
        else:
            # 使用常规检查提示词
            prompt = f"""
            请检查以下小说章节大纲是否与故事主线概述冲突：

            【章节大纲】
            {chapter_outline}

            【故事主线概述】
            {main_storyline}

            【当前进度】
            当前是第{current_chapter}章，总共{total_chapters}章，完成度约为{progress:.1%}

        要求：
            1. 章节大纲应与故事主线概述保持一致
            2. 章节大纲中的情节发展应符合当前进度的预期
            3. 章节大纲中的角色发展应符合故事主线中的角色发展轨迹
            4. 章节大纲中的伏笔埋设和回收应符合故事主线的安排
            5. 章节大纲中的冲突和转折应符合故事主线的整体节奏

        请以JSON格式返回检查结果：
        ```json
        {{
            "passed": true/false,
            "issues": [
                {{
                        "type": "主线偏离",
                    "description": "问题详细描述",
                    "suggestion": "修改建议"
                }}
            ]
        }}
        ```

            如果没有发现与故事主线的冲突，请返回passed为true且issues为空数组。
            """

        result = self.generate_content(prompt, temperature=0.2)
        if not result:
            return {
                "passed": False,
                "issues": [{
                    "type": "检查失败",
                    "description": "大纲冲突检查失败",
                    "suggestion": "请重新检查"
                }],
                "foreshadowing_updates": []
            }

        try:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            if json_match:
                result = json_match.group(1)

            # 解析JSON
            check_result = json.loads(result)

            # 确保结果包含必要的字段
            if "foreshadowing_updates" not in check_result:
                check_result["foreshadowing_updates"] = []

            return check_result
        except Exception as e:
            print(f"解析大纲冲突检查结果时出错: {str(e)}")
            return {
                "passed": False,
                "issues": [{
                    "type": "解析错误",
                    "description": f"解析大纲冲突检查结果时出错: {str(e)}",
                    "suggestion": "请重新检查"
                }],
                "foreshadowing_updates": [],
            }

    def check_chapter(self, chapter_content: str, chapter_outline: str,
                      characters: str, background: str,
                      foreshadowing: str, appearing_characters: str = None) -> Optional[Dict[str, Any]]:
        """
        检查章节内容

        Args:
            chapter_content: 章节内容
            chapter_outline: 章节大纲
            characters: 人物卡片
            background: 背景设定
            foreshadowing: 伏笔管理
            appearing_characters: 本章出场人物（可选）

        Returns:
            检查结果字典，包含是否通过、问题列表和修改建议，如果出错则返回None
        """
        try:
            # 从小说信息中获取流派
            novel_info_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "output", "novel_info.json")
            genre = ""
            if os.path.exists(novel_info_path):
                try:
                    with open(novel_info_path, 'r', encoding='utf-8') as f:
                        novel_info = json.load(f)
                        genre = novel_info.get("genre", "")
                except:
                    pass

            # 1. 严格检查字数
            print("正在严格检查章节字数...")
            word_count_result = self.check_chapter_word_count(chapter_content)
            
            # 获取字数信息并明确输出
            word_count = word_count_result.get("word_count", 0)
            print(f"章节字数: {word_count}字（要求：5000-12000字）")
            
            if not word_count_result.get("passed", False):
                issues = word_count_result.get("issues", [])
                for issue in issues:
                    issue_type = issue.get("type", "")
                    if "字数不足" in issue_type:
                        print(f"字数检查未通过: 字数严重不足 {word_count}字 < 5000字（最低要求）")
                    elif "字数过多" in issue_type:
                        print(f"字数检查未通过: 字数过多 {word_count}字 > 12000字（建议上限）")
                    else:
                        print(f"字数检查未通过: {issue.get('description', '')}")
                
                print("由于字数检查未通过，中止后续检查步骤")
                return word_count_result

            # 2. 检查风格
            # print("正在检查章节风格...")
            # style_result = self.check_chapter_style(chapter_content, genre)
            # if not style_result.get("passed", False):
            #     print(f"风格检查未通过: {style_result.get('issues', [])}")
            #     return style_result

            # 3. 检查背景设定冲突
            # print("正在检查背景设定冲突...")
            # background_result = self.check_chapter_background(chapter_content, background)
            # if not background_result.get("passed", False):
            #     print(f"背景设定检查未通过: {background_result.get('issues', [])}")
            #     return background_result

            # 4. 检查人物冲突
            print("正在检查是否与人物设定冲突...")
            characters_result = self.check_chapter_characters(chapter_content, characters, appearing_characters)
            if not characters_result.get("passed", False):
                print(f"人物冲突检查未通过: {characters_result.get('issues', [])}")
                return characters_result

            # 5. 检查大纲冲突
            print("正在检查是否与大纲冲突...")
            outline_result_json = self.check_chapter_outline(chapter_content, chapter_outline, foreshadowing)
            
            try:
                # 解析返回的JSON字符串
                outline_result = json.loads(outline_result_json)
            except json.JSONDecodeError as e:
                print(f"解析大纲检查结果时出错: {str(e)}")
                return {
                    "passed": False,
                    "issues": [{
                        "type": "解析错误",
                        "description": f"无法解析大纲检查结果: {str(e)}",
                        "suggestion": "请重新检查"
                    }]
                }
                
            if not outline_result.get("passed", False):
                print(f"大纲冲突检查未通过: {outline_result.get('issues', [])}")
                return outline_result

            # 合并所有检查结果
            final_result = {
                "passed": True,
                "issues": [],
                "character_updates": characters_result.get("character_updates", []),
                "foreshadowing_updates": outline_result.get("foreshadowing_updates", []),
                "appearing_character_names": characters_result.get("appearing_character_names", [])
            }

            print("章节检查通过！")
            print(f"发现 {len(final_result['character_updates'])} 个人物状态更新")
            print(f"发现 {len(final_result['foreshadowing_updates'])} 个伏笔状态更新")
            print(f"本章出场人物: {', '.join(final_result['appearing_character_names'])}")

            return final_result

        except Exception as e:
            import traceback
            print(f"检查章节时出错: {str(e)}")
            traceback.print_exc()
            return {
                "passed": False,
                "issues": [{
                    "type": "检查错误",
                    "description": f"检查过程中出错: {str(e)}",
                    "location": "整篇",
                    "suggestion": "请重新检查"
                }],
                "character_updates": [],
                "foreshadowing_updates": [],
                "appearing_character_names": []
            }

    def generate_chapter_content(self, chapter_number: int, chapter_outline: str,
                                 background: str, characters: str, foreshadowing: str,
                                 previous_chapters_summary: Optional[str] = None,
                                 generation_mode: str = "traditional") -> Optional[str]:
        """
        生成章节内容

        Args:
            chapter_number: 章节号
            chapter_outline: 章节大纲
            background: 背景设定
            characters: 人物卡片
            foreshadowing: 伏笔管理
            previous_chapters_summary: 前几章内容摘要（可选）
            generation_mode: 生成模式，"traditional"表示传统模式，"outline"表示从大纲生成模式

        Returns:
            章节内容，如果出错则返回None
        """
        previous_summary = previous_chapters_summary if previous_chapters_summary else "这是第一章，无前序内容。"

        # 检查并处理结构化JSON大纲
        chapter_outline_for_prompt = chapter_outline
        try:
            # 如果大纲是JSON格式，转换为更易读的文本格式
            if isinstance(chapter_outline, str) and chapter_outline.strip().startswith('{') and chapter_outline.strip().endswith('}'):
                import json
                outline_data = json.loads(chapter_outline)

                # 构建格式化的大纲文本
                formatted_outline = f"# 第{chapter_number}章 {outline_data.get('title', '').replace(f'第{chapter_number}章', '').strip()}\n\n"

                # 添加章节概要
                if 'chapter_summary' in outline_data and isinstance(outline_data['chapter_summary'], dict):
                    summary = outline_data['chapter_summary']
                    formatted_outline += "## 章节概要\n\n"

                    if summary.get('opening'):
                        formatted_outline += f"### 开端\n{summary['opening']}\n\n"

                    if summary.get('development'):
                        formatted_outline += f"### 发展\n{summary['development']}\n\n"

                    if summary.get('climax'):
                        formatted_outline += f"### 高潮\n{summary['climax']}\n\n"

                    if summary.get('ending'):
                        formatted_outline += f"### 结尾\n{summary['ending']}\n\n"

                # 添加出场人物
                if 'characters' in outline_data and outline_data['characters']:
                    formatted_outline += "## 出场人物\n"
                    for char in outline_data['characters']:
                        formatted_outline += f"- **{char.get('name', '')}**: {char.get('actions', '')}\n"
                    formatted_outline += "\n"

                # 添加关键情节点
                if 'key_points' in outline_data and outline_data['key_points']:
                    formatted_outline += "## 关键情节点\n"
                    for i, point in enumerate(outline_data['key_points'], 1):
                        formatted_outline += f"{i}. {point}\n"
                    formatted_outline += "\n"

                # 添加伏笔安排
                if 'foreshadowings' in outline_data and isinstance(outline_data['foreshadowings'], dict):
                    fs = outline_data['foreshadowings']
                    formatted_outline += "## 伏笔安排\n"

                    if 'planted' in fs and fs['planted']:
                        formatted_outline += "- **新埋下**：\n"
                        for plant in fs['planted']:
                            formatted_outline += f"  - {plant.get('id', '')}: {plant.get('content', '')}, 埋下方式: {plant.get('method', '')}\n"

                    if 'revealed' in fs and fs['revealed']:
                        formatted_outline += "- **回收的**：\n"
                        for reveal in fs['revealed']:
                            formatted_outline += f"  - {reveal.get('id', '')}: {reveal.get('content', '')}, 回收效果: {reveal.get('effect', '')}\n"

                # 使用格式化后的大纲
                chapter_outline_for_prompt = formatted_outline
                print(f"已将JSON大纲转换为可读文本格式，用于章节内容生成")
        except Exception as e:
            print(f"处理JSON大纲时出错: {e}，将使用原始大纲")
            # 出错时使用原始大纲
            chapter_outline_for_prompt = chapter_outline

        # 根据生成模式选择不同的提示词
        if generation_mode == "outline":
            # generate_from_outline模式专用提示词
            prompt = """
        请根据以下信息，创作一篇高质量的男频网络小说章节：

        【章节信息】
        章节号：第{0}章

        【章节大纲】
        {1}

        【背景设定】
        {2}

        【人物卡片】
        {3}

        【伏笔管理】
        {4}

        【前序内容摘要】
        {5}

        创作要求：
        1. 【关键要求】严格按照大纲的章节概要（开端、发展、高潮、结尾）进行创作，不得偏离或添加大纲未提及的情节
        2. 【关键要求】必须严格按照大纲中的关键情节点（key_points）进行展开，每个关键情节点都必须在章节中体现
        3. 【关键要求】严格按照伏笔管理要求，准确埋下和回收指定的伏笔，不得遗漏或添加
        4. 【关键要求】只能使用背景设定中已有的概念、地点、组织、物品、种族、能力等，严禁创造任何背景设定中未提及的新元素
        5. 【关键要求】所有角色行为必须符合人物卡片中的设定，不得偏离角色性格和能力
        6. 【字数要求-重要】章节必须达到5000字以上！请务必生成充分完整的内容，详细展开每个情节点，丰富对话和描写。
        7. 【字数要求】内容要详实充分，每个大纲要点都要详细展开，不能简略概括，必须达到5000字以上的篇幅。
        8. 【字数要求】请注意：这是一个完整的章节，需要5000字以上的详细内容，不是简短的片段或摘要。
        9. 文风要符合男频网络小说特点，富有代入感和爽感
        10. 使用第三人称视角，以主角为中心展开叙述
        11. 必须提供完整的章节标题，格式为"第{0}章 标题"
        12. 【关键要求】如果前序内容摘要中包含前一章结尾内容，必须确保本章开头与前一章结尾自然连贯衔接，在情节、场景、人物状态等方面保持逻辑一致性
        13. 不要在正文中包含任何元数据标记或注释
        14. 【严格要求】内容必须是大纲的严格扩写，不允许任何超出大纲范围的情节发展
        15. 【再次强调字数】请确保生成至少5000字的完整章节内容，包含丰富的对话、描写、心理活动等。

        请直接输出小说章节内容，不要输出其他无关内容。生成的章节必须达到5000字以上！
        """.format(chapter_number, chapter_outline_for_prompt, background, characters, foreshadowing, previous_summary)
        else:
            # 传统模式提示词
            prompt = """
            请根据以下信息，创作一篇高质量的男频网络小说章节：

            【章节信息】
            章节号：第{0}章

            【章节大纲】
            {1}

            【背景设定】
            {2}

            【人物卡片】
            {3}

            【伏笔管理】
            {4}

            【前序内容摘要】
            {5}

            创作要求：
            1. 严格按照大纲内容创作，不要偏离大纲设定的情节和走向
            2. 恪守背景设定和人物设定，不要创造与已有设定冲突的内容
            3. 适当埋下和回收伏笔（参考伏笔管理）
            4. 合理展现人物性格和关系发展
            5. 文风要符合男频网络小说特点，富有代入感和爽感
            6. 【重要】章节字数必须达到4000-5000字左右，不能少于4000字，节奏要流畅，情节有起伏
            7. 使用第三人称视角，以主角为中心展开叙述
            8. 注意小说细节和场景描写，增强代入感
            9. 必须提供完整的章节标题，格式为"第{0}章 标题"
            10. 如果不是第一章或视角切换，必须与前一章结尾自然衔接
            11. 不要在正文中包含任何元数据标记，如"**高潮（星陨异变）**"、"**第五章 葬神寒渊劫（高潮部分）**"等
            12. 不要在正文中包含任何非小说内容的标记或注释
            13. 【重要】所有概念、地点、组织、物品、种族、能力等必须来自背景设定中已有的内容
            14. 【重要】不要创造背景设定中未提及的新概念、新地点、新组织、新物品、新种族或新能力
            15. 【重要】所有角色的能力和成长路径必须符合背景设定中的修炼体系和世界规则

            请直接输出小说章节内容，不要输出其他无关内容。
            """.format(chapter_number, chapter_outline_for_prompt, background, characters, foreshadowing, previous_summary)

            result = self.generate_content(prompt, temperature=CREATIVE_TEMPERATURE, max_tokens=16000 if generation_mode == "outline" else 8000)
        if result:
            # 移除内容开头的元数据和说明
            # 移除类似 "（以下是严格按您要求扩充至10000字的完整章节内容，所有新增内容均用下划线标出）" 的说明
            result = re.sub(r'^（[^）]*）[\n\r]+', '', result)
            result = re.sub(r'^【[^】]*】[\n\r]+', '', result)
            result = re.sub(r'^---[\n\r]+', '', result)

            # 移除可能存在的元数据标记
            # 移除类似 "**高潮（星陨异变）**" 的标记
            result = re.sub(r'\*\*[^*]+\*\*', '', result)
            # 移除类似 "（高潮部分）" 的标记
            result = re.sub(r'（[^）]*部分[^）]*）', '', result)
            # 移除类似 "（完整扩充版）" 的标记
            result = re.sub(r'（[^）]*扩充[^）]*）', '', result)
            # 移除下划线标记
            result = re.sub(r'_(.+?)_', r'\1', result)

            # 尝试提取章节标题
            # 首先检查是否有Markdown格式的标题
            markdown_title_match = re.search(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*(.+?)[\n\r]', result)
            if markdown_title_match and markdown_title_match.group(1).strip():
                # 提取Markdown标题中的实际标题部分
                title = markdown_title_match.group(1).strip()
                full_title = f"第{chapter_number}章 {title}"
                # 移除Markdown标题行
                result = re.sub(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]', '', result, 1)
            else:
                # 检查是否有完整的"第X章 标题"格式
                full_title_match = re.search(r'第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*(.+?)[\n\r]', result)

                # 如果找到完整标题
                if full_title_match and full_title_match.group(1).strip():
                    title = full_title_match.group(1).strip()
                    full_title = f"第{chapter_number}章 {title}"
                else:
                    # 如果只找到"第X章"而没有具体标题
                    chapter_only_match = re.search(r'(第[一二三四五六七八九十百千万\d]+章)\s*[\n\r]', result)

                    # 从大纲中提取标题
                    title_from_outline = self._extract_title_from_outline(chapter_outline)

                    # 如果从大纲中提取到了标题，直接使用
                    if title_from_outline:
                        title = title_from_outline
                        print(f"从大纲中提取到标题：{title}")
                    else:
                        # 如果从大纲中提取不到标题，使用默认标题
                        title = f"未命名章节{chapter_number}"
                        print(f"未从大纲中提取到标题，使用默认标题：{title}")

                    full_title = f"第{chapter_number}章 {title}"

            # 检查内容是否完整（最后一个句子是否以标点符号结尾）
            last_sentence_incomplete = False
            if result.strip():
                last_char = result.strip()[-1]
                if last_char not in '。！？.!?"\'》）)':
                    last_sentence_incomplete = True
                    # 添加一个结束符号
                    result = result.strip() + "。"

            # 格式化章节内容
            # 检查是否已经包含章节标题
            if not result.strip().startswith(full_title) and not result.strip().startswith(f"第{chapter_number}章"):
                # 添加完整标题
                content = f"{full_title}\n\n{result}"
            else:
                # 替换可能不完整的标题
                if 'chapter_only_match' in locals() and chapter_only_match:
                    content = re.sub(r'第[一二三四五六七八九十百千万\d]+章\s*[\n\r]', f"{full_title}\n\n", result, 1)
                else:
                    content = result

            # 如果内容不完整，添加警告日志
            if last_sentence_incomplete:
                print(f"警告：第{chapter_number}章内容可能不完整，已自动添加结束符号")
                
            # 检查字数并打印
            # 保留中英文字符和数字，移除标点和空白
            clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', content)
            word_count = len(clean_text)
            
            # 根据生成模式打印不同的字数信息
            if generation_mode == "outline":
                print(f"章节总字数: {word_count}字（generate_from_outline模式要求：2500-3000字）")
                if word_count < 2500:
                    print(f"警告：第{chapter_number}章总字数为{word_count}字，少于要求的最低字数2500字")
            else:
                print(f"章节总字数: {word_count}字（传统模式要求：至少4000字）")
                if word_count < 4000:
                    print(f"警告：第{chapter_number}章总字数为{word_count}字，少于要求的最低字数4000字")
                
            return content

    def generate_chapter_segment(self, segment_type: str, chapter_number: int,
                                 chapter_outline: str, background: str,
                                 characters: str, foreshadowing: str,
                                 previous_content: Optional[str] = None,
                                 generation_mode: str = "outline") -> Optional[str]:
        """
        生成章节片段

        Args:
            segment_type: 片段类型（开端/发展/高潮/结尾）
            chapter_number: 章节号
            chapter_outline: 章节大纲
            background: 背景设定
            characters: 人物卡片
            foreshadowing: 伏笔管理
            previous_content: 前面已生成的内容或前一章结尾内容（可选）
            generation_mode: 生成模式，"outline"表示从大纲生成模式（字数较少），"traditional"表示传统模式（字数较多）

        Returns:
            章节片段，如果出错则返回None
        """
        prev_content = previous_content if previous_content else "（无前序内容）"

        # 判断是否是开头部分且有前一章内容
        is_opening_with_prev_chapter = segment_type == "开端" and previous_content and "（无前序内容）" not in previous_content

        # 从大纲中提取总章节数信息
        total_chapters = 0
        try:
            # 尝试从大纲中提取"当前章节：X/Y"格式的信息
            chapter_info_match = re.search(r'当前章节[：:]\s*\d+/(\d+)', chapter_outline)
            if chapter_info_match:
                total_chapters = int(chapter_info_match.group(1))
            else:
                # 默认设置为100章，避免误判为最终章
                total_chapters = 100
        except:
            total_chapters = 100

        # 检查并处理结构化JSON大纲
        chapter_outline_for_prompt = chapter_outline
        try:
            # 如果大纲是JSON格式，转换为更易读的文本格式
            if isinstance(chapter_outline, str) and chapter_outline.strip().startswith('{') and chapter_outline.strip().endswith('}'):
                import json
                outline_data = json.loads(chapter_outline)

                # 构建格式化的大纲文本，重点强调当前需要生成的段落部分
                formatted_outline = f"# 第{chapter_number}章 {outline_data.get('title', '').replace(f'第{chapter_number}章', '').strip()}\n\n"

                # 添加章节概要，突出当前段落
                if 'chapter_summary' in outline_data and isinstance(outline_data['chapter_summary'], dict):
                    summary = outline_data['chapter_summary']
                    formatted_outline += "## 章节概要\n\n"

                    # 根据当前生成的段落类型，添加特殊标记
                    if segment_type == "开端" and summary.get('opening'):
                        formatted_outline += f"### 开端 【当前需要生成部分】\n{summary['opening']}\n\n"
                    elif summary.get('opening'):
                        formatted_outline += f"### 开端\n{summary['opening']}\n\n"

                    if segment_type == "发展" and summary.get('development'):
                        formatted_outline += f"### 发展 【当前需要生成部分】\n{summary['development']}\n\n"
                    elif summary.get('development'):
                        formatted_outline += f"### 发展\n{summary['development']}\n\n"

                    if segment_type == "高潮" and summary.get('climax'):
                        formatted_outline += f"### 高潮 【当前需要生成部分】\n{summary['climax']}\n\n"
                    elif summary.get('climax'):
                        formatted_outline += f"### 高潮\n{summary['climax']}\n\n"

                    if segment_type == "结尾" and summary.get('ending'):
                        formatted_outline += f"### 结尾 【当前需要生成部分】\n{summary['ending']}\n\n"
                    elif summary.get('ending'):
                        formatted_outline += f"### 结尾\n{summary['ending']}\n\n"

                # 添加出场人物
                if 'characters' in outline_data and outline_data['characters']:
                    formatted_outline += "## 出场人物\n"
                    for char in outline_data['characters']:
                        formatted_outline += f"- **{char.get('name', '')}**: {char.get('actions', '')}\n"
                    formatted_outline += "\n"

                # 添加关键情节点
                if 'key_points' in outline_data and outline_data['key_points']:
                    formatted_outline += "## 关键情节点\n"
                    for i, point in enumerate(outline_data['key_points'], 1):
                        formatted_outline += f"{i}. {point}\n"
                    formatted_outline += "\n"

                # 添加伏笔安排
                if 'foreshadowings' in outline_data and isinstance(outline_data['foreshadowings'], dict):
                    fs = outline_data['foreshadowings']
                    formatted_outline += "## 伏笔安排\n"

                    if 'planted' in fs and fs['planted']:
                        formatted_outline += "- **新埋下**：\n"
                        for plant in fs['planted']:
                            formatted_outline += f"  - {plant.get('id', '')}: {plant.get('content', '')}, 埋下方式: {plant.get('method', '')}\n"

                    if 'revealed' in fs and fs['revealed']:
                        formatted_outline += "- **回收的**：\n"
                        for reveal in fs['revealed']:
                            formatted_outline += f"  - {reveal.get('id', '')}: {reveal.get('content', '')}, 回收效果: {reveal.get('effect', '')}\n"

                # 使用格式化后的大纲
                chapter_outline_for_prompt = formatted_outline
                print(f"已将JSON大纲转换为可读文本格式，用于生成{segment_type}段落")
        except Exception as e:
            print(f"处理JSON大纲时出错: {e}，将使用原始大纲")
            # 出错时使用原始大纲
            chapter_outline_for_prompt = chapter_outline

        prompt = """
        请根据以下信息，创作一篇高质量的男频网络小说章节的{0}部分：

        【章节信息】
        章节号：第{1}章

        【章节大纲】
        {2}

        【背景设定】
        {3}

        【人物卡片】
        {4}

        【伏笔管理】
        {5}

        【已生成的前序内容】
        {6}

        创作要求：
        1. 严格按照大纲内容创作，不要偏离大纲设定的情节和走向
        2. 专注于{0}部分的内容创作
        3. 【重要】所有概念、地点、组织、物品、种族、能力等必须来自背景设定中已有的内容
        4. 【重要】严禁创造背景设定中未提及的新概念、新地点、新组织、新物品、新种族或新能力
        5. 【重要】所有角色的能力和成长路径必须符合背景设定中的修炼体系和世界规则
        """.format(segment_type, chapter_number, chapter_outline_for_prompt, background, characters, foreshadowing, prev_content)

        # 根据不同生成模式设置字数要求
        if generation_mode == "outline":
            # 大纲模式，使用较少的字数
            min_words_outline = {
                "开端": 900,
                "发展": 1200,
                "高潮": 900,
                "结尾": 900
            }
            max_words_outline = {
                "开端": 1500,
                "发展": 2000,
                "高潮": 1500,
                "结尾": 1500
            }
            
            # 最终章的结尾部分可能需要更多字数
            if segment_type == "结尾" and chapter_number == total_chapters:
                min_words_outline["结尾"] = 1200
                max_words_outline["结尾"] = 2000
            
            # 根据段落类型添加特定要求
        if segment_type == "开端":
            # 检查是否有前一章结尾内容
            if previous_content and "（无前序内容）" not in previous_content and chapter_number > 1:
                prompt += f"""
        3. 开端部分需要：
            - 必须包含完整的章节标题（格式为"第{chapter_number}章 标题"）
            - 必须与前一章结尾自然衔接，前一章结尾内容如下：

            【前一章结尾内容】
            {previous_content}

            - 设置场景和氛围
            - 引入主要人物
            - 埋下本章核心冲突/问题的伏笔
            - 保持节奏流畅，引人入胜
                - 【紧贴大纲，避免添加大纲中未提及的情节】
                - 【字数要求】必须产出{min_words_outline["开端"]}-{max_words_outline["开端"]}字，绝对不能少于{min_words_outline["开端"]}字
        """
            else:
                prompt += f"""
        3. 开端部分需要：
            - 必须包含完整的章节标题（格式为"第{chapter_number}章 标题"）
            - 如果不是第一章或视角切换，必须与前一章结尾自然衔接
            - 设置场景和氛围
            - 引入主要人物
            - 埋下本章核心冲突/问题的伏笔
            - 保持节奏流畅，引人入胜
                - 【紧贴大纲，避免添加大纲中未提及的情节】
                - 【字数要求】必须产出{min_words_outline["开端"]}-{max_words_outline["开端"]}字，绝对不能少于{min_words_outline["开端"]}字
        """
        elif segment_type == "发展":
                prompt += f"""
        3. 发展部分需要：
            - 推动情节向前发展
            - 展现角色互动和冲突
            - 增加情节复杂度和张力
            - 设置次级高潮
            - 埋下新的伏笔或回收前文伏笔
                - 【紧贴大纲，避免添加大纲中未提及的情节】
                - 【字数要求】必须产出{min_words_outline["发展"]}-{max_words_outline["发展"]}字，绝对不能少于{min_words_outline["发展"]}字
        """
        elif segment_type == "高潮":
                prompt += f"""
        3. 高潮部分需要：
            - 呈现章节的主要冲突和关键场景
            - 展现角色在面临重大挑战时的表现
            - 安排精彩的战斗/对决/突破场景
            - 制造情感共鸣点
            - 展现关键的伏笔回收
                - 【紧贴大纲，避免添加大纲中未提及的情节】
                - 【字数要求】必须产出{min_words_outline["高潮"]}-{max_words_outline["高潮"]}字，绝对不能少于{min_words_outline["高潮"]}字
        """
        elif segment_type == "结尾":
            # 检查是否是最后一章
            if chapter_number == total_chapters:
                prompt += f"""
        3. 结尾部分需要（最终章）：
            - 合理解决本章和整个故事的主要冲突
            - 展示角色的最终成长或变化
            - 提供明确的结局
            - 回收所有重要伏笔
            - 提供情感上的满足感和完整感
                - 【紧贴大纲，避免添加大纲中未提及的情节】
                - 【字数要求】必须产出{min_words_outline["结尾"]}-{max_words_outline["结尾"]}字，绝对不能少于{min_words_outline["结尾"]}字
        """
            else:
                prompt += f"""
        3. 结尾部分需要：
            - 合理解决本章的主要冲突
            - 展示角色的成长或变化
            - 埋下下一章的伏笔或悬念
            - 为下一章做好铺垫，创造自然过渡
            - 结尾场景和情绪应该能够与下一章开头形成连贯
            - 提供情感上的满足感
            - 保持与整体故事线的连贯性
                - 【紧贴大纲，避免添加大纲中未提及的情节】
                - 【字数要求】必须产出{min_words_outline["结尾"]}-{max_words_outline["结尾"]}字，绝对不能少于{min_words_outline["结尾"]}字
            """
        else:
            # 传统模式，使用较多的字数
            min_words_traditional = {
                "开端": 1500,
                "发展": 2000,
                "高潮": 1500,
                "结尾": 1500
            }
            max_words_traditional = {
                "开端": 2500,
                "发展": 3000,
                "高潮": 2500,
                "结尾": 2500
            }
            
            # 最终章的结尾部分可能需要更多字数
            if segment_type == "结尾" and chapter_number == total_chapters:
                min_words_traditional["结尾"] = 2000
                max_words_traditional["结尾"] = 3000
            
            # 根据段落类型添加特定要求
            if segment_type == "开端":
                # 检查是否有前一章结尾内容
                if previous_content and "（无前序内容）" not in previous_content and chapter_number > 1:
                    prompt += f"""
            3. 开端部分需要：
                - 必须包含完整的章节标题（格式为"第{chapter_number}章 标题"）
                - 必须与前一章结尾自然衔接，前一章结尾内容如下：

                【前一章结尾内容】
                {previous_content}

                - 设置场景和氛围
                - 引入主要人物
                - 埋下本章核心冲突/问题的伏笔
                - 保持节奏流畅，引人入胜
                - 【紧贴大纲，避免添加大纲中未提及的情节】
                - 【字数要求】必须产出{min_words_traditional["开端"]}-{max_words_traditional["开端"]}字，绝对不能少于{min_words_traditional["开端"]}字
            """
                else:
                    prompt += f"""
            3. 开端部分需要：
                - 必须包含完整的章节标题（格式为"第{chapter_number}章 标题"）
                - 如果不是第一章或视角切换，必须与前一章结尾自然衔接
                - 设置场景和氛围
                - 引入主要人物
                - 埋下本章核心冲突/问题的伏笔
                - 保持节奏流畅，引人入胜
                - 【紧贴大纲，避免添加大纲中未提及的情节】
                - 【字数要求】必须产出{min_words_traditional["开端"]}-{max_words_traditional["开端"]}字，绝对不能少于{min_words_traditional["开端"]}字
            """
            elif segment_type == "发展":
                prompt += f"""
            3. 发展部分需要：
                - 推动情节向前发展
                - 展现角色互动和冲突
                - 增加情节复杂度和张力
                - 设置次级高潮
                - 埋下新的伏笔或回收前文伏笔
                - 【紧贴大纲，避免添加大纲中未提及的情节】
                - 【字数要求】必须产出{min_words_traditional["发展"]}-{max_words_traditional["发展"]}字，绝对不能少于{min_words_traditional["发展"]}字
            """
            elif segment_type == "高潮":
                prompt += f"""
            3. 高潮部分需要：
                - 呈现章节的主要冲突和关键场景
                - 展现角色在面临重大挑战时的表现
                - 安排精彩的战斗/对决/突破场景
                - 制造情感共鸣点
                - 展现关键的伏笔回收
                - 【紧贴大纲，避免添加大纲中未提及的情节】
                - 【字数要求】必须产出{min_words_traditional["高潮"]}-{max_words_traditional["高潮"]}字，绝对不能少于{min_words_traditional["高潮"]}字
            """
            elif segment_type == "结尾":
                # 检查是否是最后一章
                if chapter_number == total_chapters:
                    prompt += f"""
            3. 结尾部分需要（最终章）：
                - 合理解决本章和整个故事的主要冲突
                - 展示角色的最终成长或变化
                - 提供明确的结局
                - 回收所有重要伏笔
                - 提供情感上的满足感和完整感
                - 【紧贴大纲，避免添加大纲中未提及的情节】
                - 【字数要求】必须产出{min_words_traditional["结尾"]}-{max_words_traditional["结尾"]}字，绝对不能少于{min_words_traditional["结尾"]}字
            """
                else:
                    prompt += f"""
            3. 结尾部分需要：
                - 合理解决本章的主要冲突
                - 展示角色的成长或变化
                - 埋下下一章的伏笔或悬念
                - 为下一章做好铺垫，创造自然过渡
                - 结尾场景和情绪应该能够与下一章开头形成连贯
                - 提供情感上的满足感
                - 保持与整体故事线的连贯性
                - 【紧贴大纲，避免添加大纲中未提及的情节】
                - 【字数要求】必须产出{min_words_traditional["结尾"]}-{max_words_traditional["结尾"]}字，绝对不能少于{min_words_traditional["结尾"]}字
        """

        prompt += """
        4. 确保内容逻辑连贯，与前序内容无缝衔接
        5. 文风要符合男频网络小说特点，富有代入感和爽感
        6. 使用第三人称视角，以主角为中心展开叙述
        7. 注意小说细节和场景描写，增强代入感
        8. 不要在正文中包含任何元数据标记，如"**高潮（星陨异变）**"、"**第五章 葬神寒渊劫（高潮部分）**"等
        9. 不要在正文中包含任何非小说内容的标记或注释
        10. 【重要】请确保生成足够字数的内容，满足字数要求是首要任务

        请直接输出小说章节{0}部分内容，不要输出其他无关内容。
        """.format(segment_type) 

        result = self.generate_content(prompt, temperature=CREATIVE_TEMPERATURE, max_tokens=16000 if generation_mode == "outline" else 8000)

        if result:
            # 移除可能存在的元数据标记
            # 移除类似 "**高潮（星陨异变）**" 的标记
            result = re.sub(r'\*\*[^*]+\*\*', '', result)
            # 移除类似 "（高潮部分）" 的标记
            result = re.sub(r'（[^）]*部分[^）]*）', '', result)
            # 移除类似 "（完整扩充版）" 的标记
            result = re.sub(r'（[^）]*扩充[^）]*）', '', result)

            # 检查字数并打印
            # 保留中英文字符和数字，移除标点和空白
            clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', result)
            word_count = len(clean_text)
            
            # 打印字数信息
            print(f"{segment_type}段落字数: {word_count}字")
            
            # 根据生成模式和段落类型确定最低字数要求
            min_words = 0
            if generation_mode == "outline":
                min_words_dict = min_words_outline
            else:
                min_words_dict = min_words_traditional
                
            min_words = min_words_dict.get(segment_type, 900)
                
            if word_count < min_words:
                print(f"警告：{segment_type}段落字数不足，仅有{word_count}字，少于{generation_mode}模式下的要求{min_words}字")

        return result

    def expand_chapter_content(self, chapter_content: str, expansion_suggestions: List[str],
                               chapter_outline: str, background: str, characters: str,
                               foreshadowing: str) -> Optional[str]:
        """
        根据建议扩充章节内容

        Args:
            chapter_content: 原章节内容
            expansion_suggestions: 扩充建议列表
            chapter_outline: 章节大纲
            background: 背景设定
            characters: 人物卡片
            foreshadowing: 伏笔管理

        Returns:
            扩充后的章节内容，如果出错则返回None
        """
        # 处理结构化JSON大纲
        chapter_outline_for_prompt = chapter_outline
        try:
            # 如果大纲是JSON格式，转换为更易读的文本格式
            if isinstance(chapter_outline, str) and chapter_outline.strip().startswith('{') and chapter_outline.strip().endswith('}'):
                import json
                outline_data = json.loads(chapter_outline)

                # 提取章节号
                chapter_number = 0
                title_match = re.search(r'第(\d+)章', outline_data.get('title', ''))
                if title_match:
                    try:
                        chapter_number = int(title_match.group(1))
                    except:
                        pass

                # 构建格式化的大纲文本
                formatted_outline = f"# {outline_data.get('title', '')}\n\n"

                # 添加章节概要
                if 'chapter_summary' in outline_data and isinstance(outline_data['chapter_summary'], dict):
                    summary = outline_data['chapter_summary']
                    formatted_outline += "## 章节概要\n\n"

                    if summary.get('opening'):
                        formatted_outline += f"### 开端\n{summary['opening']}\n\n"

                    if summary.get('development'):
                        formatted_outline += f"### 发展\n{summary['development']}\n\n"

                    if summary.get('climax'):
                        formatted_outline += f"### 高潮\n{summary['climax']}\n\n"

                    if summary.get('ending'):
                        formatted_outline += f"### 结尾\n{summary['ending']}\n\n"

                # 添加出场人物
                if 'characters' in outline_data and outline_data['characters']:
                    formatted_outline += "## 出场人物\n"
                    for char in outline_data['characters']:
                        formatted_outline += f"- **{char.get('name', '')}**: {char.get('actions', '')}\n"
                    formatted_outline += "\n"

                # 添加关键情节点
                if 'key_points' in outline_data and outline_data['key_points']:
                    formatted_outline += "## 关键情节点\n"
                    for i, point in enumerate(outline_data['key_points'], 1):
                        formatted_outline += f"{i}. {point}\n"
                    formatted_outline += "\n"

                # 添加伏笔安排
                if 'foreshadowings' in outline_data and isinstance(outline_data['foreshadowings'], dict):
                    fs = outline_data['foreshadowings']
                    formatted_outline += "## 伏笔安排\n"

                    if 'planted' in fs and fs['planted']:
                        formatted_outline += "- **新埋下**：\n"
                        for plant in fs['planted']:
                            formatted_outline += f"  - {plant.get('id', '')}: {plant.get('content', '')}, 埋下方式: {plant.get('method', '')}\n"

                    if 'revealed' in fs and fs['revealed']:
                        formatted_outline += "- **回收的**：\n"
                        for reveal in fs['revealed']:
                            formatted_outline += f"  - {reveal.get('id', '')}: {reveal.get('content', '')}, 回收效果: {reveal.get('effect', '')}\n"

                # 使用格式化后的大纲
                chapter_outline_for_prompt = formatted_outline
                print(f"已将JSON大纲转换为可读文本格式，用于章节内容扩展")
        except Exception as e:
            print(f"处理JSON大纲时出错: {e}，将使用原始大纲")
            # 出错时使用原始大纲
            chapter_outline_for_prompt = chapter_outline

        # 将扩充建议转换为文本
        suggestions_text = "\n".join([f"- {suggestion}" for suggestion in expansion_suggestions])

        prompt = """
        请根据以下信息，扩充男频网络小说章节内容，使其达到要求的字数（至少10000字，不能少于9500字）：

        【章节大纲】
        {0}

        【背景设定】
        {1}

        【人物卡片】
        {2}

        【伏笔管理】
        {3}

        【原章节内容】
        {4}

        【扩充建议】
        {5}

        扩充要求：
        1. 根据提供的扩充建议，有针对性地扩充内容
        2. 保持整体风格和流畅性，确保扩充内容与原内容无缝衔接
        3. 确保扩充后的内容符合大纲、背景设定和人物设定
        4. 不要改变原章节的基本情节走向
        5. 扩充后的总字数应达到至少8000字，理想字数为10000字
        6. 扩充内容应该是对原内容的丰富和补充，而不是简单的重复
        7. 可以适当增加细节描写、内心独白、场景渲染、战斗描写等
        8. 可以适当增加对世界观和人物关系的展开
        9. 【重要】所有概念、地点、组织、物品、种族、能力等必须来自背景设定中已有的内容
        10. 【重要】严禁创造背景设定中未提及的新概念、新地点、新组织、新物品、新种族或新能力
        11. 【重要】所有角色的能力和成长路径必须符合背景设定中的修炼体系和世界规则

        请直接输出扩充后的完整章节内容，不要输出其他无关内容或说明。
        """.format(chapter_outline_for_prompt, background, characters, foreshadowing, chapter_content, suggestions_text)

        return self.generate_content(prompt, temperature=0.7)

    def generate_chapter(self, chapter_number: int, chapter_outline: str,
                         background: str, characters: str, foreshadowing: str,
                         previous_chapters_summary: Optional[str] = None,
                         generation_instruction: str = "",
                         generation_mode: str = "traditional") -> Optional[str]:
        """
        一次性生成完整章节内容

        Args:
            chapter_number: 章节号
            chapter_outline: 章节大纲
            background: 背景设定
            characters: 人物卡片
            foreshadowing: 伏笔管理
            previous_chapters_summary: 前几章内容摘要（可选）
            generation_instruction: 生成指令（已弃用，为兼容性保留）
            generation_mode: 生成模式，"traditional"表示传统模式，"outline"表示从大纲生成模式

        Returns:
            完整的章节内容，如果出错则返回None
        """
        # 这个方法与generate_chapter_content基本相同，但名称不同以兼容现有代码
        return self.generate_chapter_content(
            chapter_number, chapter_outline, background, characters,
            foreshadowing, previous_chapters_summary, generation_mode
        )

    def _clean_json_string(self, json_str: str, aggressive: bool = False) -> str:
        """
        清理JSON字符串，修复常见的格式问题

        Args:
            json_str: 原始JSON字符串
            aggressive: 是否使用更激进的清理方法

        Returns:
            清理后的JSON字符串
        """
        # 记录原始JSON字符串（截断以避免日志过长）
        if len(json_str) > 200:
            print(f"原始JSON（前200字符）: {json_str[:200]}...")
        else:
            print(f"原始JSON: {json_str}")

        # 记录是否使用激进模式
        print(f"使用{'激进' if aggressive else '常规'}清理模式")
        
        # 移除Markdown代码块标记
        original = json_str
        # 移除整个代码块
        json_str = re.sub(r'```json\s*(.*?)\s*```', r'\1', json_str, flags=re.DOTALL)
        # 移除开头的标记
        json_str = re.sub(r'^```\w*\s*', '', json_str)
        # 移除结尾的标记
        json_str = re.sub(r'\s*```\s*$', '', json_str)
        if original != json_str:
            print("移除Markdown代码块标记")

        # 移除注释
        json_str = re.sub(r'//.*?(\n|$)', '', json_str)

        # 处理换行和缩进问题
        if aggressive:
            # 更激进的清理：移除所有换行和多余空格
            original = json_str
            json_str = re.sub(r'\s+', ' ', json_str)
            if original != json_str:
                print("应用激进清理: 移除所有换行和多余空格")

            # 在冒号后添加一个空格
            original = json_str
            json_str = re.sub(r':\s*', ': ', json_str)
            if original != json_str:
                print("应用激进清理: 规范化冒号后的空格")

            # 在逗号后添加一个空格
            original = json_str
            json_str = re.sub(r',\s*', ', ', json_str)
            if original != json_str:
                print("应用激进清理: 规范化逗号后的空格")
        else:
            # 常规清理：规范化换行和缩进
            original = json_str
            json_str = re.sub(r'\n\s*"', ' "', json_str)
            if original != json_str:
                print("应用常规清理: 规范化换行和缩进")

        # 修复引号问题
        original = json_str
        json_str = json_str.replace('\\"', '"')  # 移除转义的引号
        if original != json_str:
            print("修复: 移除转义的引号")

        original = json_str
        json_str = json_str.replace('""', '"')   # 修复双引号问题
        if original != json_str:
            print("修复: 修复双引号问题")

        # 修复属性名问题
        original = json_str
        json_str = re.sub(r'{\s*([^"{\s][^:]*?):', r'{ "\1":', json_str)  # 修复开头的属性名
        if original != json_str:
            print("修复: 修复开头的属性名")

        original = json_str
        json_str = re.sub(r',\s*([^"{\s][^:]*?):', r', "\1":', json_str)  # 修复中间的属性名
        if original != json_str:
            print("修复: 修复中间的属性名")

        # 修复多余的逗号
        original = json_str
        json_str = re.sub(r',\s*}', '}', json_str)  # 移除对象末尾多余的逗号
        if original != json_str:
            print("修复: 移除对象末尾多余的逗号")

        original = json_str
        json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组末尾多余的逗号
        if original != json_str:
            print("修复: 移除数组末尾多余的逗号")

        # 修复单引号问题
        original = json_str
        json_str = json_str.replace("'", '"')
        if original != json_str:
            print("修复: 将单引号替换为双引号")

        # 修复特殊问题：'\n          "title"' 这样的格式
        original = json_str
        json_str = re.sub(r'\\n\s+"', ' "', json_str)
        if original != json_str:
            print("修复: 处理特殊的换行+空格+引号格式")

        # 记录清理后的JSON字符串（截断以避免日志过长）
        if len(json_str) > 200:
            print(f"清理后JSON（前200字符）: {json_str[:200]}...")
        else:
            print(f"清理后JSON: {json_str}")

        return json_str

    def _extract_chapter_from_storyline(self, main_storyline: str, chapter_number: int) -> Optional[str]:
        """
        从故事主线中提取特定章节的内容

        Args:
            main_storyline: 故事主线(JSON格式或Python对象)
            chapter_number: 章节号

        Returns:
            章节内容，如果无法提取则返回None
        """
        try:
            print(f"提取第{chapter_number}章内容，主线类型为: {type(main_storyline)}")

            # 标准化数据类型
            storyline_data = None

            # 如果已经是字典或列表类型(已结构化)
            if isinstance(main_storyline, (dict, list)):
                storyline_data = self._normalize_storyline_data(main_storyline)
            else:
                # 处理可能是包装格式的情况
                if isinstance(main_storyline, str):
                    # 1. 首先尝试标准解析
                    try:
                        # 在解析前清理JSON字符串
                        cleaned_storyline = self._clean_json_string(main_storyline)
                        storyline_data = json.loads(cleaned_storyline)
                        storyline_data = self._normalize_storyline_data(storyline_data)
                        print("标准JSON解析成功")
                    except json.JSONDecodeError as e:
                        print(f"解析JSON失败: {e}，尝试修复")

                        # 2. 使用LLM修复（优先）
                        try:
                            from utils.json_helper import llm_fix_json
                            print("尝试使用LLM修复故事主线JSON...")
                            fixed_json_str = llm_fix_json(main_storyline, "故事主线", self)
                            if fixed_json_str:
                                try:
                                    storyline_data = json.loads(fixed_json_str)
                                    storyline_data = self._normalize_storyline_data(storyline_data)
                                    print("使用LLM成功修复故事主线JSON")
                                except json.JSONDecodeError:
                                    print("LLM修复后的JSON仍然无法解析，尝试其他方法")
                        except ImportError:
                            print("llm_fix_json不可用，尝试其他方法")
                        except Exception as e:
                            print(f"LLM修复失败: {e}，尝试其他方法")

                        # 3. 如果LLM修复失败，尝试使用专用修复工具
                        if storyline_data is None:
                            try:
                                from utils.fix_storyline import fix_storyline_json, get_chapter_from_storyline
                                print("尝试使用专用工具修复...")
                                # 解析故事主线JSON
                                storyline_data = fix_storyline_json(main_storyline)

                                # 使用专用函数获取章节
                                chapter = get_chapter_from_storyline(storyline_data, chapter_number)
                                if chapter:
                                    return chapter.get("content")
                            except ImportError:
                                print("未找到专用修复工具，尝试内置修复方法")
                                pass
                            except Exception as e:
                                print(f"专用工具修复失败: {e}，尝试内置修复方法")

                        # 4. 最后尝试使用内置修复方法
                        if storyline_data is None:
                            fixed_json = self._fix_json(main_storyline)
                            if fixed_json:
                                storyline_data = self._normalize_storyline_data(fixed_json)
                            else:
                                print("修复JSON失败")
                                return None
                else:
                    print(f"不支持的主线类型: {type(main_storyline)}")
                    return None

            # 如果前面的专用方法没有返回结果，使用标准方法提取章节
            outlines = storyline_data.get("outlines") or storyline_data.get("chapter_outlines") or []

            # 查找具有匹配chapter_number或index的章节
            for chapter in outlines:
                if isinstance(chapter, dict):
                    chapter_idx = chapter.get("chapter_number") or chapter.get("index")
                    if chapter_idx == chapter_number:
                        return chapter.get("content")

            print(f"未找到第{chapter_number}章的内容")
            return None

        except Exception as e:
            print(f"提取章节时出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _normalize_storyline_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        规范化故事主线数据结构，确保只使用outlines字段

        Args:
            data: 故事主线数据

        Returns:
            规范化后的数据
        """
        # 创建副本，避免修改原始数据
        normalized_data = data.copy() if isinstance(data, dict) else {"outlines": data} if isinstance(data, list) else {"outlines": []}

        # 确保有outlines字段
        if "chapter_outlines" in normalized_data and "outlines" not in normalized_data:
            normalized_data["outlines"] = normalized_data["chapter_outlines"]

        # 如果同时有两个字段，确保内容一致并删除chapter_outlines
        if "outlines" in normalized_data and "chapter_outlines" in normalized_data:
            # 如果outlines为空但chapter_outlines有值，使用chapter_outlines的值
            if not normalized_data["outlines"] and normalized_data["chapter_outlines"]:
                normalized_data["outlines"] = normalized_data["chapter_outlines"]

            # 删除chapter_outlines字段
            del normalized_data["chapter_outlines"]

        return normalized_data

    def _fix_json(self, json_str: str) -> Optional[Dict[str, Any]]:
        """
        修复API返回的JSON字符串

        Args:
            json_str: JSON字符串

        Returns:
            Dict: 修复后的JSON数据，失败则返回None
        """
        if not json_str:
            return None

        # 首先尝试使用LLM直接修复
        try:
            from utils.json_helper import llm_fix_json
            print("尝试使用LLM修复JSON...")
            fixed_json_str = llm_fix_json(json_str, "通用JSON", self)
            if fixed_json_str:
                try:
                    return json.loads(fixed_json_str)
                except json.JSONDecodeError:
                    print("LLM修复后的JSON仍然无法解析，尝试其他方法")
        except ImportError:
            print("llm_fix_json not available，尝试其他方法")
            pass
        except Exception as e:
            print(f"LLM修复JSON失败: {e}，尝试其他方法")

        # 如果LLM修复失败，尝试使用专门的修复工具
        try:
            from utils.fix_storyline import fix_storyline_json
            print("尝试使用fix_storyline_json修复...")
            return fix_storyline_json(json_str)
        except ImportError:
            print("fix_storyline_json not found，尝试其他方法")
            pass
        except Exception as e:
            print(f"使用fix_storyline_json修复失败: {e}，尝试其他方法")

        # 尝试从utils.json_helper导入fix_json
        try:
            from utils.json_helper import fix_json
            print("尝试使用fix_json修复...")
            fixed_json_str = fix_json(json_str)
            return json.loads(fixed_json_str)
        except ImportError:
            print("fix_json not found，尝试内置修复方法")
            pass
        except Exception as e:
            print(f"使用fix_json修复失败: {e}，尝试内置方法")

        # 使用内置修复方法
        try:
            # 简单修复一些常见问题
            print("尝试使用内置修复方法...")
            json_str = json_str.replace('\\', '\\\\')  # 处理反斜杠
            json_str = re.sub(r'(?<!\\)"([^"]*?)":\s*"([^"]*?)(?<!\\)"([,}\]])', r'"\1":"\2\"\3', json_str)  # 修复引号
            json_str = re.sub(r',\s*}', '}', json_str)  # 移除结尾多余的逗号
            json_str = re.sub(r',\s*]', ']', json_str)  # 移除结尾多余的逗号

            # 尝试解析
            return json.loads(json_str)
        except Exception as e:
            print(f"所有JSON修复方法均失败: {e}")
            return None

    def regenerate_chapter_outline(self, genre: str, style_guide: str, background: str,
                              current_chapter: int, total_chapters: int,
                              previous_chapters_summary: Optional[str] = None,
                              characters_info: Optional[str] = None,
                              foreshadowing_info: Optional[str] = None,
                              appearing_characters: Optional[str] = None,
                              main_storyline: Optional[str] = None,
                              original_outline: Optional[str] = None) -> Optional[str]:
        """
        重新生成章节大纲，基于原有内容进行修改
        
        Args:
            genre: 小说流派
            style_guide: 风格指南
            background: 背景设定
            current_chapter: 当前章节号
            total_chapters: 总章节数
            previous_chapters_summary: 前几章的摘要
            characters_info: 人物信息
            foreshadowing_info: 伏笔信息
            appearing_characters: 本章出场人物
            main_storyline: 主线剧情
            original_outline: 原有的章节大纲
            
        Returns:
            重新生成的章节大纲，如果失败返回None
        """
        try:
            # 提取当前章节在主线中的内容
            chapter_guide = None
            if main_storyline:
                chapter_guide = self._extract_chapter_from_storyline(main_storyline, current_chapter)
                
            # 构建提示词
            prompt = f"""
            【任务】重新生成网络小说第{current_chapter}章大纲，修改和完善现有内容

            【小说背景】
            流派：{genre}
            风格指南：{style_guide}
            世界观设定：{background}
            当前章节：第{current_chapter}章（共{total_chapters}章）
            """

            # 如果有章节指南，添加到提示词中
            if chapter_guide:
                prompt += f"""
                【章节主线指南】
                {chapter_guide}
                """

            # 如果有前面章节的摘要，添加到提示词中
            if previous_chapters_summary:
                prompt += f"""
                【前面章节摘要】
                {previous_chapters_summary}
                """

            # 如果有人物信息，添加到提示词中
            if characters_info:
                prompt += f"""
                【人物信息】
                {characters_info}
                """

            # 如果有伏笔信息，添加到提示词中
            if foreshadowing_info:
                prompt += f"""
                【伏笔设定】
                {foreshadowing_info}
                """

            # 如果有本章出场人物，添加到提示词中
            if appearing_characters:
                prompt += f"""
                【本章出场人物】
                {appearing_characters}
                """
                
            # 添加原有大纲内容
            if original_outline:
                prompt += f"""
                【原有大纲内容】
                {original_outline}
                
                请基于以上原有大纲内容进行修改和完善，不要完全重写，保留原有的核心情节和角色。
                主要对以下方面进行改进：
                1. 修复与人物设定、世界观的冲突
                2. 增强情节的连贯性和吸引力
                3. 适当调整对话和场景描写，使其更符合风格指南
                4. 确保合理的章节长度和内容密度
                """
            
            prompt += """
            请生成一个详细的章节大纲，包括以下内容：
            1. 章节标题
            2. 章节摘要（包括开篇、发展、高潮）
            3. 本章出场的重要人物及他们的行动
            4. 关键情节点和场景描写
            5. 本章涉及的伏笔安排

            输出格式要求为干净的JSON格式：
            ```json
            {
              "title": "第X章 章节标题",
              "chapter_summary": {
                "opening": "开篇内容描述",
                "development": "情节发展描述",
                "climax": "高潮部分描述"
              },
              "characters": [
                {
                  "name": "角色名",
                  "actions": ["行动1", "行动2"]
                }
              ],
              "key_points": [
                "关键情节点1",
                "关键情节点2"
              ],
              "scenes": [
                {
                  "description": "场景描述",
                  "characters": ["角色1", "角色2"],
                  "events": ["事件1", "事件2"]
                }
              ],
              "foreshadowings": [
                {
                  "type": "设置/揭示",
                  "description": "伏笔描述"
                }
              ]
            }
            ```

            【重要创作约束】：
            1. 所有出现的概念、地点、组织、物品、种族、能力等必须严格限制在背景设定范围内
            2. 严禁创造背景设定中未提及的新概念、新地点、新组织、新物品、新种族或新能力
            3. 所有角色的能力和成长路径必须符合背景设定中的修炼体系和世界规则
            4. 如无法确定某个元素是否在背景设定中，请选择保守策略，使用明确存在的概念

            注意：
            1. 确保JSON格式正确，特别是避免常见的格式错误（如多余的逗号、未闭合的引号等）
            2. 保持章节内容的连贯性，确保与前面章节的情节自然衔接
            3. 符合小说风格和流派特点，增强可读性和吸引力
            4. 确保所有字段内容丰富、具体，而不是简单的占位符
            """

            # 生成内容
            result = self.generate_content(prompt, temperature=0.7, max_tokens=4000)
            if not result:
                return None

            # 提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', result, re.DOTALL)
            cleaned_result = json_match.group(1) if json_match else result

            # 使用专门的清理函数处理JSON
            cleaned_result = self._clean_json_string(cleaned_result)

            # 尝试解析JSON确保格式正确
            try:
                json.loads(cleaned_result)
                return cleaned_result
            except json.JSONDecodeError as e:
                print(f"重新生成章节大纲JSON解析错误: {e}")
                # 应用更激进的清理
                cleaned_result = self._clean_json_string(cleaned_result, aggressive=True)
                try:
                    json.loads(cleaned_result)
                    return cleaned_result
                except json.JSONDecodeError:
                    # 如果仍然失败，尝试使用_fix_json方法
                    try:
                        fixed_data = self._fix_json(cleaned_result)
                        if fixed_data:
                            return json.dumps(fixed_data, ensure_ascii=False)
                    except Exception:
                        pass
                    
                    print("尝试所有JSON修复方法均失败，返回原始结果")
                    return result  # 返回原始结果作为最后的尝试

        except Exception as e:
            print(f"重新生成章节大纲时出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    def fix_chapter_content(self, chapter_content: str, fix_suggestions: List[str],
                          chapter_outline: str, background: str, characters: str,
                          foreshadowing: str, issue_type: str) -> Optional[str]:
        """
        根据修复建议修复章节内容中的特定问题

        Args:
            chapter_content: 原章节内容
            fix_suggestions: 修复建议列表
            chapter_outline: 章节大纲
            background: 背景设定
            characters: 人物卡片
            foreshadowing: 伏笔管理
            issue_type: 问题类型（如"背景冲突"、"人物冲突"、"大纲冲突"等）

        Returns:
            修复后的章节内容，如果出错则返回None
        """
        # 处理结构化JSON大纲
        chapter_outline_for_prompt = chapter_outline
        try:
            # 如果大纲是JSON格式，转换为更易读的文本格式
            if isinstance(chapter_outline, str) and chapter_outline.strip().startswith('{') and chapter_outline.strip().endswith('}'):
                import json
                outline_data = json.loads(chapter_outline)

                # 提取章节号
                chapter_number = 0
                title_match = re.search(r'第(\d+)章', outline_data.get('title', ''))
                if title_match:
                    try:
                        chapter_number = int(title_match.group(1))
                    except:
                        pass

                # 构建格式化的大纲文本
                formatted_outline = f"# {outline_data.get('title', '')}\n\n"

                # 添加章节概要
                if 'chapter_summary' in outline_data and isinstance(outline_data['chapter_summary'], dict):
                    summary = outline_data['chapter_summary']
                    formatted_outline += "## 章节概要\n\n"

                    if summary.get('opening'):
                        formatted_outline += f"### 开端\n{summary['opening']}\n\n"

                    if summary.get('development'):
                        formatted_outline += f"### 发展\n{summary['development']}\n\n"

                    if summary.get('climax'):
                        formatted_outline += f"### 高潮\n{summary['climax']}\n\n"

                    if summary.get('ending'):
                        formatted_outline += f"### 结尾\n{summary['ending']}\n\n"

                # 添加出场人物
                if 'characters' in outline_data and outline_data['characters']:
                    formatted_outline += "## 出场人物\n"
                    for char in outline_data['characters']:
                        formatted_outline += f"- **{char.get('name', '')}**: {char.get('actions', '')}\n"
                    formatted_outline += "\n"

                # 添加关键情节点
                if 'key_points' in outline_data and outline_data['key_points']:
                    formatted_outline += "## 关键情节点\n"
                    for i, point in enumerate(outline_data['key_points'], 1):
                        formatted_outline += f"{i}. {point}\n"
                    formatted_outline += "\n"

                # 添加伏笔安排
                if 'foreshadowings' in outline_data and isinstance(outline_data['foreshadowings'], dict):
                    fs = outline_data['foreshadowings']
                    formatted_outline += "## 伏笔安排\n"

                    if 'planted' in fs and fs['planted']:
                        formatted_outline += "- **新埋下**：\n"
                        for plant in fs['planted']:
                            formatted_outline += f"  - {plant.get('id', '')}: {plant.get('content', '')}, 埋下方式: {plant.get('method', '')}\n"

                    if 'revealed' in fs and fs['revealed']:
                        formatted_outline += "- **回收的**：\n"
                        for reveal in fs['revealed']:
                            formatted_outline += f"  - {reveal.get('id', '')}: {reveal.get('content', '')}, 回收效果: {reveal.get('effect', '')}\n"

                # 使用格式化后的大纲
                chapter_outline_for_prompt = formatted_outline
                print(f"已将JSON大纲转换为可读文本格式，用于章节内容修复")
        except Exception as e:
            print(f"处理JSON大纲时出错: {e}，将使用原始大纲")
            # 出错时使用原始大纲
            chapter_outline_for_prompt = chapter_outline

        # 将修复建议转换为文本
        suggestions_text = "\n".join([f"- {suggestion}" for suggestion in fix_suggestions])

        # 构建提示词
        issue_type_description = ""
        if "背景" in issue_type:
            issue_type_description = "背景设定冲突或世界观不一致"
        elif "人物" in issue_type:
            issue_type_description = "人物行为、能力或对话与设定不符"
        elif "大纲" in issue_type:
            issue_type_description = "与章节大纲或情节规划不符"
        else:
            issue_type_description = issue_type
            
        prompt = """
        请修复以下男频网络小说章节内容中的{0}问题：

        【章节大纲】
        {1}

        【背景设定】
        {2}

        【人物卡片】
        {3}

        【伏笔管理】
        {4}

        【原章节内容】
        {5}

        【修复建议】
        {6}

        修复要求：
        1. 根据提供的修复建议，针对性地解决{0}问题
        2. 保持整体风格和流畅性，确保修复后的内容与原内容无缝衔接
        3. 确保修复后的内容符合大纲、背景设定和人物设定
        4. 不要改变原章节的基本情节走向和核心事件
        5. 修复时应注意保持章节总字数，不要大幅缩减内容
        6. 修复时应注重解决问题，而非加入新的元素
        7. 修复后的内容应更加符合设定，逻辑更加连贯
        8. 如果原内容有明显错误，应该修正这些错误
        9. 【重要】所有概念、地点、组织、物品、种族、能力等必须来自背景设定中已有的内容
        10. 【重要】严禁创造背景设定中未提及的新概念、新地点、新组织、新物品、新种族或新能力
        11. 【重要】所有角色的能力和成长路径必须符合背景设定中的修炼体系和世界规则
        12.  【重要】字数必须在5000字以上

        请直接输出修复后的完整章节内容，不要输出修复过程或说明。
        """.format(issue_type_description, chapter_outline_for_prompt, background, characters, foreshadowing, chapter_content, suggestions_text)

        return self.generate_content(prompt, temperature=CREATIVE_TEMPERATURE)

    def check_chapter_background(self, chapter_content: str, background: str) -> dict:
        """
        检查章节内容是否与背景设定基本一致，允许一定数量的非关键性名词。
        
        Args:
            chapter_content: 章节内容
            background: 背景设定
            
        Returns:
            检查结果字典：
            {
                "passed": true/false,  # 仅当发现严重违规时才为false
                "issues": [  # 发现的问题列表，如果没有问题则为空数组
                    {
                        "type": "背景设定违规",  # 问题类型
                        "description": "检测到章节内容中出现背景设定之外的概念：xxx", # 问题描述
                        "suggestion": "请严格使用背景设定内的概念，移除或替换违规内容", # 修复建议
                        "severity": "high/medium/low"  # 问题严重程度
                    }
                ]
            }
            
        注意：只有严重违规才会导致passed=false，允许少量非严重违规。
        """
        import re
        issues = []
        passed = True
        
        # 获取背景设定白名单（从缓存或重新提取）
        allowed_terms = self._extract_background_whitelist(background)
        
        # 通用名词白名单，这些词不需要在背景设定中出现
        common_terms = {
            # 常见动作词
            "走", "跑", "跳", "看", "听", "说", "想", "笑", "哭", "叹", "坐", "站", "躺",
            "转身", "眨眼", "思考", "沉思", "沉默", "犹豫", "迟疑", "急忙", "缓慢", "徐徐",
            # 常见描述词
            "大", "小", "高", "低", "远", "近", "长", "短", "宽", "窄", "深", "浅",
            "红", "橙", "黄", "绿", "青", "蓝", "紫", "黑", "白", "灰", "褐", "金", "银",
            "美丽", "漂亮", "英俊", "帅气", "丑陋", "可爱", "威严", "庄重", "阴森", "温暖",
            # 常见环境词
            "天空", "大地", "太阳", "月亮", "星星", "云彩", "雨水", "风", "雪", "雾",
            "山", "水", "河流", "湖泊", "海", "沙漠", "平原", "森林", "树木", "花草",
            "洞穴", "道路", "桥", "房子", "建筑", "城市", "村庄", "城墙", "门", "窗",
            # 常见器物
            "桌子", "椅子", "床", "门", "窗", "碗", "盘子", "杯子", "刀", "叉", "勺子",
            "衣服", "鞋子", "帽子", "手套", "披风", "项链", "戒指", "手镯",
            # 常见身体部位
            "头", "脸", "眼睛", "鼻子", "嘴", "耳朵", "手", "脚", "腿", "胳膊", "肩膀", "背",
            "胸", "腹", "腰", "颈", "髋", "膝", "肘", "腕", "踝", "指", "趾", "发", "须",
            # 常见时间词
            "时间", "瞬间", "片刻", "刹那", "一会儿", "早上", "中午", "傍晚", "晚上", "夜晚",
            "清晨", "黎明", "黄昏", "日落", "午夜", "昨天", "今天", "明天", "过去", "现在", "将来",
            # 常见人际关系
            "朋友", "伙伴", "同伴", "敌人", "对手", "师傅", "学徒", "师父", "徒弟", "主人", "仆人",
            # 常见情绪
            "高兴", "悲伤", "愤怒", "恐惧", "担心", "紧张", "放松", "兴奋", "失望", "满足",
            # 其他通用概念
            "方法", "技巧", "方式", "计划", "战略", "战术", "进攻", "防守", "撤退", "围攻",
            "呼吸", "声音", "气息", "气氛", "气势", "力量", "压力", "重量", "光线", "光芒", "光辉"
        }
        
        # 在章节内容中查找所有可能的专有名词
        found_terms = set()
        
        # 提取2个或更多汉字组成的词，或者字母开头的词
        for term in re.findall(r'[\u4e00-\u9fa5]{2,}|[A-Za-z][A-Za-z0-9_\-]{2,}', chapter_content):
            # 只检查可能是专有名词的词汇（首字母大写的英文词或特定模式的中文词）
            if term not in common_terms and term not in allowed_terms:
                # 判断是否是专有名词的简单启发式规则
                is_proper_noun = False
                
                # 英文词首字母大写可能是专有名词
                if term[0].isupper() and term.lower() not in allowed_terms:
                    is_proper_noun = True
                
                # 中文词中包含特定字符可能是专有名词
                special_chars = ["门", "派", "宗", "帮", "会", "国", "城", "镇", "山", "河", "湖", "海", "宫", "殿", "界", "域"]
                if any(char in term for char in special_chars) and len(term) > 2:
                    is_proper_noun = True
                
                # 只将可能是专有名词的词加入检查列表
                if is_proper_noun:
                    found_terms.add(term)
        
        # 设置容错阈值 - 允许少量未知专有名词
        unknown_terms = [term for term in found_terms if term not in allowed_terms]
        max_allowed_unknown = max(3, len(allowed_terms) * 0.05)  # 允许至少3个或5%的未知词
        
        # 只有当未知词数量超过阈值时才标记为失败
        if len(unknown_terms) > max_allowed_unknown:
            passed = False
        
        # 将违规词添加到问题列表
        for term in unknown_terms:
            severity = "high" if len(term) > 3 and any(char in term for char in ["门", "派", "宗", "帮", "国"]) else "medium"
            issues.append({
                "type": "背景设定违规",
                "description": f"检测到章节内容中出现背景设定之外的概念：{term}",
                "suggestion": "请检查此概念是否符合世界观设定，必要时进行替换或在背景中添加解释",
                "severity": severity
            })
                
        return {"passed": passed, "issues": issues}

    def _extract_title_from_outline(self, chapter_outline: str) -> Optional[str]:
        """
        从章节大纲中提取标题
        
        Args:
            chapter_outline: 章节大纲
            
        Returns:
            提取的标题，如果未找到则返回None
        """
        if not chapter_outline:
            return None
            
        # 尝试匹配各种标题格式
        title_patterns = [
            r'第\d+章\s+(.+?)[\n\r]',  # 第X章 标题
            r'#\s*第\d+章\s+(.+?)[\n\r]',  # # 第X章 标题
            r'章节标题[：:]\s*(.+?)[\n\r]',  # 章节标题：标题
            r'标题[：:]\s*(.+?)[\n\r]',  # 标题：标题
        ]
        
        for pattern in title_patterns:
            match = re.search(pattern, chapter_outline)
            if match:
                title = match.group(1).strip()
                # 移除可能的格式字符
                title = re.sub(r'[#*\-\=]+', '', title).strip()
                if title:
                    return title
                    
        return None

    def generate_chapter_part_combined(self, chapter_number: int, chapter_outline: str,
                                      characters: str, scenes: str, foreshadowing: str,
                                      previous_content: Optional[str] = None,
                                      part_name: str = "",
                                      part_keywords: List[str] = []) -> Optional[str]:
        """
        生成章节的组合部分（如开端+发展或高潮+结尾）
        
        Args:
            chapter_number: 章节编号
            chapter_outline: 章节大纲
            characters: 人物信息
            scenes: 场景信息
            foreshadowing: 伏笔信息
            previous_content: 前序内容（前几章摘要或已生成的第一部分）
            part_name: 段落名称（如"开端+发展"）
            part_keywords: 段落关键词列表（如["开端", "发展"]）
            
        Returns:
            生成的段落内容，失败返回None
        """
        # 处理结构化JSON大纲
        chapter_outline_for_prompt = chapter_outline
        try:
            # 如果大纲是JSON格式，转换为更易读的文本格式
            if isinstance(chapter_outline, str) and chapter_outline.strip().startswith('{') and chapter_outline.strip().endswith('}'):
                import json
                outline_data = json.loads(chapter_outline)

                # 构建格式化的大纲文本，突出当前需要生成的段落部分
                formatted_outline = f"# 第{chapter_number}章 {outline_data.get('title', '').replace(f'第{chapter_number}章', '').strip()}\n\n"

                # 添加章节概要，突出当前需要生成的段落
                if 'chapter_summary' in outline_data and isinstance(outline_data['chapter_summary'], dict):
                    summary = outline_data['chapter_summary']
                    formatted_outline += "## 章节概要\n\n"

                    # 根据part_keywords标记需要生成的部分
                    for keyword in ["opening", "development", "climax", "ending"]:
                        chinese_keyword = {"opening": "开端", "development": "发展", "climax": "高潮", "ending": "结尾"}[keyword]
                        
                        if summary.get(keyword):
                            if chinese_keyword in part_keywords:
                                formatted_outline += f"### {chinese_keyword} 【当前需要生成部分】\n{summary[keyword]}\n\n"
                            else:
                                formatted_outline += f"### {chinese_keyword}\n{summary[keyword]}\n\n"

                # 添加出场人物
                if 'characters' in outline_data and outline_data['characters']:
                    formatted_outline += "## 出场人物\n"
                    for char in outline_data['characters']:
                        name = char.get('name', '')
                        actions = char.get('actions', '')
                        emotions = char.get('emotions', '')
                        first_appearance = char.get('firstAppearance', False)
                        
                        char_line = f"- **{name}**: {actions}"
                        if emotions:
                            char_line += f"，情感变化：{emotions}"
                        if first_appearance:
                            char_line += f" 【初次登场角色，需要适当介绍】"
                        formatted_outline += char_line + "\n"
                    formatted_outline += "\n"

                # 添加场景信息
                if 'scenes' in outline_data and outline_data['scenes']:
                    formatted_outline += "## 必须包含的场景\n"
                    for i, scene in enumerate(outline_data['scenes'], 1):
                        formatted_outline += f"{i}. {scene}\n"
                    formatted_outline += "【重要】只能使用以上场景，不允许出现任何其他场景！\n\n"

                # 添加关键情节点
                if 'key_points' in outline_data and outline_data['key_points']:
                    formatted_outline += "## 关键情节点\n"
                    for i, point in enumerate(outline_data['key_points'], 1):
                        formatted_outline += f"{i}. {point}\n"
                    formatted_outline += "【重要】必须包含所有关键情节点，不允许添加其他情节！\n\n"

                # 添加伏笔安排
                if 'foreshadowings' in outline_data and isinstance(outline_data['foreshadowings'], dict):
                    fs = outline_data['foreshadowings']
                    formatted_outline += "## 伏笔管理\n"

                    if 'planted' in fs and fs['planted']:
                        formatted_outline += "### 必须埋下的伏笔：\n"
                        for plant in fs['planted']:
                            formatted_outline += f"- ID:{plant.get('id', '')} 内容：{plant.get('content', '')} 埋下方式：{plant.get('method', '')}\n"

                    if 'revealed' in fs and fs['revealed']:
                        formatted_outline += "### 必须回收的伏笔：\n"
                        for reveal in fs['revealed']:
                            formatted_outline += f"- ID:{reveal.get('id', '')} 内容：{reveal.get('content', '')} 回收效果：{reveal.get('effect', '')}\n"
                    
                    if fs.get('planted') or fs.get('revealed'):
                        formatted_outline += "【重要】严禁使用或提及上述伏笔之外的任何其他伏笔！\n\n"

                chapter_outline_for_prompt = formatted_outline
                print(f"已将JSON大纲转换为可读文本格式，用于生成{part_name}部分")
        except Exception as e:
            print(f"处理JSON大纲时出错: {e}，将使用原始大纲")
            chapter_outline_for_prompt = chapter_outline

        # 构建专门的提示词
        keywords_text = "、".join(part_keywords)
        previous_text = previous_content if previous_content else "（无前序内容）"
        
        prompt = f"""
        请根据以下信息，创作一篇高质量的男频网络小说章节的{part_name}部分：

        【章节信息】
        章节号：第{chapter_number}章

        【章节大纲】
        {chapter_outline_for_prompt}

        【人物信息】
        {characters}

        【场景限制】
        {scenes}

        【伏笔管理】
        {foreshadowing}

        【前序内容】
        {previous_text}

        创作要求：
        1. 【关键要求】专注于{keywords_text}部分的内容创作，严格按照大纲中对应的{keywords_text}概要进行展开
        2. 【关键要求-情节限制】严格按照关键情节点（key_points）进行创作，必须包含所有关键情节点，严禁添加关键情节点之外的任何其他情节
        3. 【关键要求-场景限制】只能使用场景限制中列出的场景，严禁出现任何其他场景或地点
        4. 【关键要求-人物处理】严格按照人物信息中的设定，对于标记为【初次登场角色】的人物，需要适当介绍其外貌、身份或特征
        5. 【关键要求-伏笔管理】严格按照伏笔管理要求执行：
           - 必须埋下所有"必须埋下的伏笔"，按照指定的埋下方式
           - 必须回收所有"必须回收的伏笔"，达到指定的回收效果
           - 严禁使用、提及或创造任何伏笔管理之外的伏笔内容
        6. 【关键要求-背景限制】只能使用已有的概念、地点、组织、物品、种族、能力等，严禁创造任何新元素
        7. 【字数要求-重要】本部分必须达到1300-1500字！内容要详实充分，丰富对话和描写
        8. 【字数要求】每个情节点都要详细展开，不能简略概括，必须达到1300-1500字的篇幅
        9. 【字数要求】请注意：这是章节的重要组成部分，需要1300-1500字的详细内容
        10. 文风要符合男频网络小说特点，富有代入感和爽感
        11. 使用第三人称视角，以主角为中心展开叙述
        12. 如果是第一部分（开端+发展），必须提供完整的章节标题，格式为"第{chapter_number}章 标题"
        13. 如果有前序内容，必须确保与前序内容自然连贯衔接
        14. 不要在正文中包含任何元数据标记或注释
        15. 【再次强调字数】请确保生成1300-1500字的{part_name}内容，包含丰富的对话、描写、心理活动等

        【严格遵守规则】
        - 场景：只能使用指定场景，不得添加其他场景
        - 情节：只能包含关键情节点，不得添加其他情节  
        - 伏笔：只能使用指定的伏笔，不得创造新伏笔
        - 人物：初次登场角色需要适当介绍

        请直接输出小说{part_name}部分内容，不要输出其他无关内容。生成的内容必须达到1300-1500字！
        """

        result = self.generate_content(prompt, temperature=CREATIVE_TEMPERATURE, max_tokens=12000)
        if result:
            # 清理可能的元数据标记
            result = re.sub(r'^（[^）]*）[\n\r]+', '', result)
            result = re.sub(r'^【[^】]*】[\n\r]+', '', result)
            result = re.sub(r'^---[\n\r]+', '', result)
            result = re.sub(r'\*\*[^*]+\*\*', '', result)
            result = re.sub(r'（[^）]*部分[^）]*）', '', result)
            result = re.sub(r'_(.+?)_', r'\1', result)
            result = re.sub(r'[\n\r]{3,}', '\n\n', result).strip()
            
            # 清理大纲格式标记
            result = re.sub(r'^##\s*开端[\n\r]*', '', result, flags=re.MULTILINE)
            result = re.sub(r'^##\s*发展[\n\r]*', '', result, flags=re.MULTILINE)
            result = re.sub(r'^##\s*高潮[\n\r]*', '', result, flags=re.MULTILINE)
            result = re.sub(r'^##\s*结尾[\n\r]*', '', result, flags=re.MULTILINE)
            result = re.sub(r'[\n\r]*##\s*开端[\n\r]*', '\n\n', result)
            result = re.sub(r'[\n\r]*##\s*发展[\n\r]*', '\n\n', result)
            result = re.sub(r'[\n\r]*##\s*高潮[\n\r]*', '\n\n', result)
            result = re.sub(r'[\n\r]*##\s*结尾[\n\r]*', '\n\n', result)
            
            # 清理"未完待续"等标记
            result = re.sub(r'（未完待续）', '', result)
            result = re.sub(r'\(未完待续\)', '', result)
            result = re.sub(r'【未完待续】', '', result)
            result = re.sub(r'未完待续\.{3,}', '', result)
            result = re.sub(r'未完待续', '', result)
            
            # 清理其他可能的格式标记
            result = re.sub(r'（[^）]*修复[^）]*）', '', result)
            result = re.sub(r'-{3,}[\n\r]+', '', result)
            result = re.sub(r'【[^】]*修复[^】]*】[\n\r]+', '', result)
            result = re.sub(r'【[^】]*调整[^】]*】[\n\r]+', '', result)
            
            # 清理结尾标记和元数据
            result = re.sub(r'（待续）', '', result)
            result = re.sub(r'\(待续\)', '', result)
            result = re.sub(r'【待续】', '', result)
            result = re.sub(r'（全文完）', '', result)
            result = re.sub(r'\(全文完\)', '', result)
            result = re.sub(r'【全文完】', '', result)
            result = re.sub(r'（字数[：:]\s*\d+字？?\）', '', result)
            result = re.sub(r'\(字数[：:]\s*\d+字？?\)', '', result)
            result = re.sub(r'【字数[：:]\s*\d+字？?】', '', result)
            result = re.sub(r'### 开端部分[\n\r]*', '', result)
            result = re.sub(r'### 发展部分[\n\r]*', '', result)
            result = re.sub(r'### 高潮部分[\n\r]*', '', result)
            result = re.sub(r'### 结尾部分[\n\r]*', '', result)
            
            # 清理伏笔相关元数据标记
            result = re.sub(r'（伏笔[^）]*）', '', result)
            result = re.sub(r'\(伏笔[^)]*\)', '', result)
            result = re.sub(r'【伏笔[^】]*】', '', result)
            result = re.sub(r'（伏笔[^）]*埋下[^）]*）', '', result)
            result = re.sub(r'（伏笔[^）]*回收[^）]*）', '', result)
            result = re.sub(r'【伏笔[^】]*埋下[^】]*】', '', result)
            result = re.sub(r'【伏笔[^】]*回收[^】]*】', '', result)
            
            # 重新清理多余换行
            result = re.sub(r'[\n\r]{3,}', '\n\n', result).strip()

            # 检查内容是否完整
            if result.strip() and result.strip()[-1] not in '。！？.!?"\'》）)':
                result = result.strip() + "。"

            return result

        return None
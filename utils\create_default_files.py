#!/usr/bin/env python3
"""
创建默认的foreshadowings目录和JSON文件
"""

import os
import json
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import OUTPUT_DIR

def create_default_foreshadowings():
    """创建默认的foreshadowings目录和JSON文件"""
    print("创建默认的foreshadowings文件...")
    
    # 创建目录
    foreshadowings_dir = os.path.join(OUTPUT_DIR, "foreshadowings")
    os.makedirs(foreshadowings_dir, exist_ok=True)
    
    # 默认空伏笔数据
    empty_data = {"foreshadowings": []}
    
    # 创建all_foreshadowings.json
    all_path = os.path.join(foreshadowings_dir, "all_foreshadowings.json")
    with open(all_path, 'w', encoding='utf-8') as f:
        json.dump(empty_data, f, ensure_ascii=False, indent=2)
    
    # 创建revealed.json
    revealed_path = os.path.join(foreshadowings_dir, "revealed.json")
    with open(revealed_path, 'w', encoding='utf-8') as f:
        json.dump(empty_data, f, ensure_ascii=False, indent=2)
    
    # 创建unrevealed.json
    unrevealed_path = os.path.join(foreshadowings_dir, "unrevealed.json")
    with open(unrevealed_path, 'w', encoding='utf-8') as f:
        json.dump(empty_data, f, ensure_ascii=False, indent=2)
    
    print(f"成功创建以下文件:")
    print(f"- {all_path}")
    print(f"- {revealed_path}")
    print(f"- {unrevealed_path}")
    return True

def check_and_fix_main_storyline():
    """检查并修复main_storyline.json文件"""
    print("检查main_storyline.json文件...")
    
    file_path = os.path.join(OUTPUT_DIR, "main_storyline.json")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        create_empty_storyline(file_path)
        return True
    
    # 尝试解析现有文件
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 如果文件为空，创建一个空的结构
        if not content.strip():
            print("文件存在但为空，创建默认结构")
            create_empty_storyline(file_path)
            return True
            
        # 尝试解析JSON
        try:
            data = json.loads(content)
            print("成功解析main_storyline.json")
            
            # 验证基本结构
            if not isinstance(data, dict):
                print("无效的JSON结构: 不是一个对象")
                create_empty_storyline(file_path)
                return True
                
            # 检查必要的字段
            if "story_title" not in data:
                print("无效的JSON结构: 缺少story_title字段")
                data["story_title"] = "未命名小说"
                
            if "outlines" not in data and "chapter_outlines" not in data:
                print("无效的JSON结构: 缺少outlines/chapter_outlines字段")
                data["outlines"] = []
                
            # 确保同时存在两个字段
            if "outlines" in data and "chapter_outlines" not in data:
                data["chapter_outlines"] = data["outlines"]
            elif "chapter_outlines" in data and "outlines" not in data:
                data["outlines"] = data["chapter_outlines"]
                
            # 保存修复后的数据
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            print("已修复并保存main_storyline.json")
            return True
            
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {str(e)}")
            print("创建默认结构")
            create_empty_storyline(file_path)
            return True
            
    except Exception as e:
        print(f"检查文件时出错: {str(e)}")
        create_empty_storyline(file_path)
        return True

def create_empty_storyline(file_path):
    """创建空的故事主线结构"""
    empty_data = {
        "story_title": "未命名小说",
        "outlines": [],
        "chapter_outlines": []
    }
    
    # 创建文件
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(empty_data, f, ensure_ascii=False, indent=2)
    
    print(f"已创建默认的故事主线文件: {file_path}")
    return True

def main():
    """主函数"""
    # 确保output目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 创建默认的foreshadowings文件
    create_default_foreshadowings()
    
    # 检查并修复main_storyline.json
    check_and_fix_main_storyline()
    
    print("所有默认文件检查完成")

if __name__ == "__main__":
    main() 
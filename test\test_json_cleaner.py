"""
测试JSON清理和解析功能
"""

import sys
import os
import unittest
import json
import re
import importlib.util

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 模拟config模块
import types
config_module = types.ModuleType('config')
config_module.DEEPSEEK_API_URL = "https://example.com/api"
config_module.DEEPSEEK_API_KEY = "fake_api_key"
config_module.MAX_RETRIES = 3
config_module.TIMEOUT = 30
config_module.DEFAULT_CHAPTER_LENGTH = 5000
config_module.TARGET_NOVEL_LENGTH = 100000
sys.modules['config'] = config_module

# 导入要测试的函数
from llm.deepseek_all import DeepSeekAllAPI

class TestJSONCleaner(unittest.TestCase):
    """测试JSON清理和解析功能"""

    def setUp(self):
        """设置测试环境"""
        self.api = DeepSeekAllAPI()

    def test_clean_json_string_basic(self):
        """测试基本的JSON清理功能"""
        # 测试正常的JSON
        normal_json = '{"title": "测试标题", "content": "测试内容"}'
        cleaned = self.api._clean_json_string(normal_json)
        self.assertEqual(cleaned, normal_json)

        # 测试解析结果
        data = json.loads(cleaned)
        self.assertEqual(data["title"], "测试标题")
        self.assertEqual(data["content"], "测试内容")

    def test_clean_json_string_newline_title(self):
        """测试特殊的换行+空格+引号格式"""
        # 测试有问题的JSON: '\n          "title"'
        problem_json = '{"title\\n          "title": "测试标题", "content": "测试内容"}'
        cleaned = self.api._clean_json_string(problem_json)

        # 验证清理后的JSON可以被解析
        try:
            data = json.loads(cleaned)
            self.assertTrue(True)  # 如果能解析，测试通过
        except json.JSONDecodeError:
            self.fail("清理后的JSON仍然无法解析")

    def test_clean_json_string_unquoted_keys(self):
        """测试没有引号的属性名"""
        # 测试属性名没有引号的JSON
        unquoted_keys_json = '{title: "测试标题", content: "测试内容"}'
        cleaned = self.api._clean_json_string(unquoted_keys_json)

        # 验证清理后的JSON可以被解析
        try:
            data = json.loads(cleaned)
            self.assertEqual(data["title"], "测试标题")
            self.assertEqual(data["content"], "测试内容")
        except json.JSONDecodeError:
            self.fail("清理后的JSON仍然无法解析")

    def test_clean_json_string_trailing_commas(self):
        """测试多余的逗号"""
        # 测试对象末尾多余逗号的JSON
        trailing_comma_json = '{"title": "测试标题", "content": "测试内容", }'
        cleaned = self.api._clean_json_string(trailing_comma_json)

        # 验证清理后的JSON可以被解析
        try:
            data = json.loads(cleaned)
            self.assertEqual(data["title"], "测试标题")
            self.assertEqual(data["content"], "测试内容")
        except json.JSONDecodeError:
            self.fail("清理后的JSON仍然无法解析")

        # 测试数组末尾多余逗号的JSON
        trailing_comma_array_json = '{"items": ["item1", "item2", ]}'
        cleaned = self.api._clean_json_string(trailing_comma_array_json)

        # 验证清理后的JSON可以被解析
        try:
            data = json.loads(cleaned)
            self.assertEqual(data["items"][0], "item1")
            self.assertEqual(data["items"][1], "item2")
        except json.JSONDecodeError:
            self.fail("清理后的JSON仍然无法解析")

    def test_clean_json_string_single_quotes(self):
        """测试单引号问题"""
        # 测试使用单引号的JSON
        single_quotes_json = "{'title': '测试标题', 'content': '测试内容'}"
        cleaned = self.api._clean_json_string(single_quotes_json)

        # 验证清理后的JSON可以被解析
        try:
            data = json.loads(cleaned)
            self.assertEqual(data["title"], "测试标题")
            self.assertEqual(data["content"], "测试内容")
        except json.JSONDecodeError:
            self.fail("清理后的JSON仍然无法解析")

    def test_clean_json_string_aggressive(self):
        """测试激进清理模式"""
        # 测试需要激进清理的JSON
        messy_json = """
        {
            title: "测试标题",
            "content": "测试内容",
            "items": [
                "item1",
                "item2",
            ],
        }
        """
        cleaned = self.api._clean_json_string(messy_json, aggressive=True)

        # 验证清理后的JSON可以被解析
        try:
            data = json.loads(cleaned)
            self.assertEqual(data["title"], "测试标题")
            self.assertEqual(data["content"], "测试内容")
            self.assertEqual(data["items"][0], "item1")
            self.assertEqual(data["items"][1], "item2")
        except json.JSONDecodeError:
            self.fail("清理后的JSON仍然无法解析")

if __name__ == "__main__":
    unittest.main()

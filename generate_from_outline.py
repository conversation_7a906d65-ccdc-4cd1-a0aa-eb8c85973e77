#!/usr/bin/env python3
"""
从大纲文件中直接生成章节
"""

import os
import json
import re 
import sys
import time
import shutil
import textwrap
import traceback 
from typing import Dict, Any, List, Optional, Union, Tuple

from llm.deepseek_all import DeepSeekAllAPI
# from utils.file_manager import read_chapter_file # 本地有同名函数定义，此导入将被覆盖

# 最大重试次数常量
MAX_RETRIES = 3

def read_chapter_file(chapter_number: int, output_dir: str = 'output_new') -> Optional[str]:
    """
    读取指定章节的内容文件
    
    参数:
        chapter_number: 章节编号
        output_dir: 输出目录
        
    返回:
        章节内容字符串，如果文件不存在则返回None
    """
    chapter_file = os.path.join(output_dir, f'chapter_{chapter_number}.txt')
    if os.path.exists(chapter_file):
        try:
            with open(chapter_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"读取章节文件出错: {str(e)}")
    return None

class ChapterGenerator:
    """直接从大纲文件生成章节"""
    
    def __init__(self):
        """初始化章节生成器"""
        self.api = DeepSeekAllAPI()
        self.output_dir = 'output_new'
        
    def generate_novel_from_outline(self, genre: str, output_dir: str = 'output_new') -> bool:
        """
        生成写作风格和背景设定，并保存到指定目录
        
        参数:
            genre: 小说流派
            output_dir: 保存生成内容的目录（默认: output_new）
            
        返回:
            生成是否成功
        """
        try:
            self.output_dir = output_dir
            os.makedirs(output_dir, exist_ok=True)
            
            print(f"正在为{genre}流派小说生成风格提示词...")
            style_guide = self.api.generate_novel_style(
                genre, 
                target_length=None, 
                total_chapters=None 
            )
            
            if not style_guide:
                print("生成风格提示词失败")
                return False
                
            style_guide_path = os.path.join(output_dir, "style_guide.txt")
            try:
                with open(style_guide_path, "w", encoding="utf-8") as f:
                    f.write(style_guide)
                print(f"风格提示词已保存至：{style_guide_path}")
            except Exception as e:
                print(f"保存风格提示词时出错: {str(e)}")
                return False
                
            print("风格提示词生成成功")
            
            print("正在生成背景设定...")
            background_data = self.api.generate_background(genre, style_guide)
            
            if not background_data:
                print("生成背景设定失败")
                return False
                
            background_path = os.path.join(output_dir, "background.json")
            try:
                with open(background_path, "w", encoding="utf-8") as f:
                    json.dump(background_data, f, ensure_ascii=False, indent=2)
                print(f"背景设定已保存至：{background_path}")
            except Exception as e:
                print(f"保存背景设定时出错: {str(e)}")
                return False
                
            print("背景设定生成成功")
            
            novel_info = {
                "genre": genre,
                "author": "AI作家",
                "title": f"{genre}小说", 
                "description": "基于大纲生成的小说",
                "create_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "update_time": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            novel_info_path = os.path.join(output_dir, "novel_info.json")
            try:
                with open(novel_info_path, "w", encoding="utf-8") as f:
                    json.dump(novel_info, f, ensure_ascii=False, indent=2)
                print(f"小说信息已保存至：{novel_info_path}")
            except Exception as e:
                print(f"保存小说信息时出错: {str(e)}")
                return False
            
            print(f"\n风格和背景设定生成完成，已保存到{output_dir}目录！")
            print("您可以继续生成小说大纲，或直接从大纲文件生成小说内容。")
            
            return True
            
        except Exception as e:
            print(f"生成风格和背景设定时出错: {str(e)}")
            traceback.print_exc()
            return False
        
    def generate_all_chapters(self, outline_path: str, output_dir: str = 'output_new') -> None:
        """
        从大纲文件生成所有章节并保存到输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        self.output_dir = output_dir
        
        output_outline_path = os.path.join(output_dir, 'outline.json')
        if os.path.abspath(outline_path) != os.path.abspath(output_outline_path):
            shutil.copy(outline_path, output_outline_path)
            print(f"已将大纲文件复制到: {output_outline_path}")
        
        with open(outline_path, 'r', encoding='utf-8') as f:
            outline_data = json.load(f)
        
        style_guide_path = os.path.join(self.output_dir, 'style_guide.txt') 
        if not os.path.exists(style_guide_path):
            style_guide_path = os.path.join(os.path.dirname(outline_path), 'style_guide.txt')
        
        style_guide = ""
        if os.path.exists(style_guide_path):
            with open(style_guide_path, 'r', encoding='utf-8') as f:
                style_guide = f.read()
            print(f"已加载风格指南: {style_guide_path}")
        else:
            print("未找到风格指南文件，将使用空风格指南")
        
        chapters = outline_data.get('outlines', [])
        total_chapters = len(chapters)
        
        print(f"在大纲文件中找到 {total_chapters} 个章节")
        print(f"输出目录: {output_dir}")
        
        for i, chapter_data in enumerate(chapters, 1): 
            chapter_number = chapter_data.get('index', i)
            print(f"\n正在生成第 {chapter_number} 章 ({i}/{total_chapters})...")
            
            previous_chapters_data = chapters[:i-1] if i > 1 else None
            chapter_content = self._generate_chapter(chapter_data, chapter_number, total_chapters, 
                                                     previous_chapters_data, style_guide)
            
            if chapter_content:
                chapter_file = os.path.join(output_dir, f'chapter_{chapter_number}.txt')
                
                title_match = re.search(r'^(第.+?章\s*.+?)[\n\r]', chapter_content)
                if title_match:
                    title = title_match.group(1).strip()
                    clean_content = re.sub(r'^第.+?章\s*.+?[\n\r]+', '', chapter_content, 1)
                    
                    markdown_title_pattern = re.compile(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]')
                    clean_content = re.sub(markdown_title_pattern, '', clean_content)
                    clean_content = re.sub(r'[\n\r]+第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]+', '\n\n', clean_content)
                    clean_content = re.sub(r'[\n\r]{3,}', '\n\n', clean_content).strip() 
                    chapter_content = f"{title}\n\n{clean_content}"
                
                with open(chapter_file, 'w', encoding='utf-8') as f:
                    f.write(chapter_content)
                
                print(f"第 {chapter_number} 章已生成并保存到 {chapter_file}")
            else:
                print(f"生成第 {chapter_number} 章失败")
        
        print("\n所有章节已成功生成！")

    def _generate_chapter(self, chapter_data: Dict[str, Any], chapter_number: int, 
                          total_chapters: int, previous_chapters_data: Optional[List[Dict[str, Any]]] = None,
                          style_guide: str = "") -> Optional[str]:
        try:
            chapter_title = chapter_data.get('title', f'第{chapter_number}章')
            if not chapter_title.startswith(f'第{chapter_number}章'):
                chapter_title = f'第{chapter_number}章 {chapter_title}'
            
            characters = chapter_data.get('characters', [])
            foreshadowings = chapter_data.get('foreshadowings', {})
            
            # 准备前序章节摘要
            previous_chapter_summary_text = None
            if previous_chapters_data and len(previous_chapters_data) > 0:
                prev_summaries_list = []
                for prev_ch_data in previous_chapters_data[-3:]:
                    prev_num = prev_ch_data.get('index', 0)
                    prev_title = prev_ch_data.get('title', f'第{prev_num}章')
                    prev_summary_content = prev_ch_data.get('chapter_summary', {})
                    summary_line = f"第{prev_num}章 {prev_title}："
                    if prev_summary_content:
                        opening = prev_summary_content.get('opening', '')
                        if opening: summary_line += f" 开端：{opening[:100]}..."
                        ending = prev_summary_content.get('ending', '')
                        if ending: summary_line += f" 结尾：{ending[:100]}..."
                    prev_summaries_list.append(summary_line)
                previous_chapter_summary_text = "\n".join(prev_summaries_list)
            
            # 获取前一章的结尾内容以确保章节连贯性
            previous_chapter_ending_text = None
            if chapter_number > 1:
                previous_content_full = read_chapter_file(chapter_number - 1, self.output_dir)
                if previous_content_full:
                    # 获取前一章的最后几段作为结尾内容
                    paragraphs = previous_content_full.split('\n\n')
                    last_paragraphs_list = []
                    current_total_length = 0
                    for p_text in reversed(paragraphs):
                        if current_total_length + len(p_text) <= 500:
                            last_paragraphs_list.insert(0, p_text)
                            current_total_length += len(p_text)
                        else: 
                            break
                    previous_chapter_ending_text = '\n\n'.join(last_paragraphs_list)
                    if previous_chapter_ending_text:
                        print(f"已获取第{chapter_number-1}章结尾内容，长度：{len(previous_chapter_ending_text)}字符")
            
            # 将前一章结尾内容整合到前序内容摘要中
            if previous_chapter_ending_text:
                if previous_chapter_summary_text:
                    previous_chapter_summary_text += f"\n\n【第{chapter_number-1}章结尾内容】：\n{previous_chapter_ending_text}"
                else:
                    previous_chapter_summary_text = f"【第{chapter_number-1}章结尾内容】：\n{previous_chapter_ending_text}"
            
            # 格式化章节信息
            formatted_outline = self._format_chapter_outline(chapter_data, chapter_number, total_chapters)
            formatted_characters = self._format_characters(characters)
            formatted_scenes = self._format_scenes(chapter_data.get('scenes', []))
            formatted_foreshadowings = self._format_foreshadowings(foreshadowings)

            print(f"开始分两段生成第{chapter_number}章内容（generate_from_outline模式）...")
            
            # 分两段生成章节内容
            first_part = self._generate_chapter_part_combined(
                chapter_number, formatted_outline, formatted_characters, 
                formatted_scenes, formatted_foreshadowings, previous_chapter_summary_text, 
                "开端+发展", ["开端", "发展"]
            )
            
            if not first_part:
                print(f"第 {chapter_number} 章第一部分（开端+发展）生成失败")
                return None
            
            # 生成第二段时，将第一段作为前序内容
            second_part = self._generate_chapter_part_combined(
                chapter_number, formatted_outline, formatted_characters, 
                formatted_scenes, formatted_foreshadowings, first_part, 
                "高潮+结尾", ["高潮", "结尾"]
            )
            
            if not second_part:
                print(f"第 {chapter_number} 章第二部分（高潮+结尾）生成失败")
                return None
            
            # 合并两部分内容
            # 提取章节标题
            title_match = re.search(r'^(第.+?章\s*.+?)[\n\r]', first_part)
            if title_match:
                title = title_match.group(1).strip()
                # 移除第一部分的标题
                first_part_content = re.sub(r'^第.+?章\s*.+?[\n\r]+', '', first_part, 1)
            else:
                title = chapter_title
                first_part_content = first_part
            
            # 清理第二部分可能的标题
            second_part_content = re.sub(r'^第.+?章\s*.+?[\n\r]+', '', second_part, 1)
            
            # 合并内容
            full_content = f"{title}\n\n{first_part_content.strip()}\n\n{second_part_content.strip()}"
            
            # 清理可能残留的格式标记
            full_content = re.sub(r'##\s*开端[\n\r]*', '', full_content)
            full_content = re.sub(r'##\s*发展[\n\r]*', '', full_content)
            full_content = re.sub(r'##\s*高潮[\n\r]*', '', full_content)
            full_content = re.sub(r'##\s*结尾[\n\r]*', '', full_content)
            full_content = re.sub(r'（未完待续）', '', full_content)
            full_content = re.sub(r'\(未完待续\)', '', full_content)
            full_content = re.sub(r'【未完待续】', '', full_content)
            full_content = re.sub(r'未完待续\.{3,}', '', full_content)
            full_content = re.sub(r'未完待续', '', full_content)
            
            # 清理其他结尾标记和元数据
            full_content = re.sub(r'（待续）', '', full_content)
            full_content = re.sub(r'\(待续\)', '', full_content)
            full_content = re.sub(r'【待续】', '', full_content)
            full_content = re.sub(r'（全文完）', '', full_content)
            full_content = re.sub(r'\(全文完\)', '', full_content)
            full_content = re.sub(r'【全文完】', '', full_content)
            full_content = re.sub(r'（字数[：:]\s*\d+字？?\）', '', full_content)
            full_content = re.sub(r'\(字数[：:]\s*\d+字？?\)', '', full_content)
            full_content = re.sub(r'【字数[：:]\s*\d+字？?】', '', full_content)
            full_content = re.sub(r'### 开端部分[\n\r]*', '', full_content)
            full_content = re.sub(r'### 发展部分[\n\r]*', '', full_content)
            full_content = re.sub(r'### 高潮部分[\n\r]*', '', full_content)
            full_content = re.sub(r'### 结尾部分[\n\r]*', '', full_content)
            
            # 清理伏笔相关元数据标记
            full_content = re.sub(r'（伏笔[^）]*）', '', full_content)
            full_content = re.sub(r'\(伏笔[^)]*\)', '', full_content)
            full_content = re.sub(r'【伏笔[^】]*】', '', full_content)
            full_content = re.sub(r'（伏笔[^）]*埋下[^）]*）', '', full_content)
            full_content = re.sub(r'（伏笔[^）]*回收[^）]*）', '', full_content)
            full_content = re.sub(r'【伏笔[^】]*埋下[^】]*】', '', full_content)
            full_content = re.sub(r'【伏笔[^】]*回收[^】]*】', '', full_content)
            full_content = re.sub(r'[\n\r]{3,}', '\n\n', full_content).strip()
            
            print(f"第 {chapter_number} 章两段生成完成并合并成功")
            
            # # 调用_check_chapter函数检查章节内容
            # print("开始检查章节内容是否符合要求...")
            # check_result = self._check_chapter(
            #     full_content, 
            #     formatted_outline, 
            #     formatted_characters, 
            #     formatted_foreshadowings, 
            #     style_guide
            # )
            
            # # 如果检查未通过，尝试修复内容
            # if not check_result.get("passed", False):
            #     issues = check_result.get("issues", [])
            #     if issues:
            #         print(f"章节内容检查未通过，发现{len(issues)}个问题，尝试修复...")
            #         for issue in issues:
            #             issue_type = issue.get("type", "未知问题")
            #             print(f"- 问题类型: {issue_type}")
            #             print(f"  描述: {issue.get('description', '无描述')}")
            #             print(f"  建议: {issue.get('suggestion', '无建议')}")
                    
            #         # 尝试修复内容
            #         if "字数不足" in [issue.get("type", "") for issue in issues]:
            #             fixed_content = self.fix_chapter(
            #                 full_content, issues, formatted_outline, 
            #                 formatted_characters, formatted_foreshadowings, "字数问题"
            #             )
            #             if fixed_content:
            #                 print("章节字数问题已修复")
            #                 full_content = fixed_content
                    
            #         # 检查大纲冲突
            #         outline_issues = [i for i in issues if "大纲" in i.get("type", "")]
            #         if outline_issues:
            #             fixed_content = self.fix_chapter(
            #                 full_content, outline_issues, formatted_outline,
            #                 formatted_characters, formatted_foreshadowings, "大纲冲突"
            #             )
            #             if fixed_content:
            #                 print("章节大纲冲突已修复")
            #                 full_content = fixed_content
                
            #     # 修复后再次检查
            #     print("修复后再次检查章节内容...")
            #     check_result = self._check_chapter(
            #         full_content, 
            #         formatted_outline, 
            #         formatted_characters, 
            #         formatted_foreshadowings, 
            #         style_guide
            #     )
                
            #     if not check_result.get("passed", False):
            #         print("警告：章节内容在修复后仍未通过检查，但将继续使用当前内容")
            #     else:
            #         print("修复后的章节内容通过检查")
            # else:
            #     print("章节内容检查通过")
            
            return full_content
            
        except Exception as e:
            print(f"生成第 {chapter_number} 章时出错: {str(e)}")
            traceback.print_exc()
            return None

    def _generate_chapter_part_combined(self, chapter_number: int, 
                                       formatted_outline: str, 
                                       formatted_characters: str, 
                                       formatted_scenes: str, 
                                       formatted_foreshadowings: str, 
                                       previous_summary_text: Optional[str] = None,
                                       part_name: str = "",
                                       part_keywords: List[str] = []) -> Optional[str]:
        """
        生成章节的组合部分（如开端+发展或高潮+结尾）
        
        Args:
            chapter_number: 章节编号
            formatted_outline: 格式化的章节大纲
            formatted_characters: 格式化的人物信息
            formatted_scenes: 格式化的场景信息
            formatted_foreshadowings: 格式化的伏笔信息
            previous_summary_text: 前序内容摘要
            part_name: 段落名称（如"开端+发展"）
            part_keywords: 段落关键词列表（如["开端", "发展"]）
            
        Returns:
            生成的段落内容，失败返回None
        """
        try:
            MAX_RETRIES = 3
            retries = 0
            
            while retries < MAX_RETRIES:
                print(f"生成第 {chapter_number} 章{part_name}部分...（尝试 {retries + 1}/{MAX_RETRIES}）")
                
                # 构建特殊的提示词，专门用于生成组合段落
                content = self.api.generate_chapter_part_combined(
                    chapter_number, formatted_outline, formatted_characters, 
                    formatted_scenes, formatted_foreshadowings, previous_summary_text, 
                    part_name, part_keywords
                )
                
                if not content:
                    print(f"第 {chapter_number} 章{part_name}部分生成失败，尝试重试...")
                    retries += 1
                    continue
                
                # 检查字数
                clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', content)
                word_count = len(clean_text)
                print(f"{part_name}部分字数: {word_count}字")
                
                # 字数要求：1300-1500字
                if word_count < 1300:
                    print(f"{part_name}部分字数不足（{word_count}字），重试...")
                    retries += 1
                    if retries >= MAX_RETRIES:
                        print(f"达到最大重试次数，使用当前结果...")
                        return content
                    continue
                elif word_count > 1800:  # 给一些容忍度
                    print(f"{part_name}部分字数过多（{word_count}字），但仍然接受")
                
                print(f"第 {chapter_number} 章{part_name}部分生成成功")
                return content
            
            print(f"经过 {MAX_RETRIES} 次重试后，第 {chapter_number} 章{part_name}部分仍未能生成符合要求的内容")
            return None
            
        except Exception as e:
            print(f"生成第 {chapter_number} 章{part_name}部分时出错: {str(e)}")
            traceback.print_exc()
            return None
    
    def _format_chapter_outline(self, chapter_data: Dict[str, Any], chapter_number: int, total_chapters: int) -> str:
        title = chapter_data.get('title', f'第{chapter_number}章')
        chapter_summary_data = chapter_data.get('chapter_summary', {})
        formatted_outline_str = f"# 第{chapter_number}章 {title.replace(f'第{chapter_number}章', '').strip()}\n\n"
        formatted_outline_str += "## 章节概要\n\n"
        if chapter_summary_data.get('opening'): formatted_outline_str += f"### 开端\n{chapter_summary_data['opening']}\n\n"
        if chapter_summary_data.get('development'): formatted_outline_str += f"### 发展\n{chapter_summary_data['development']}\n\n"
        if chapter_summary_data.get('climax'): formatted_outline_str += f"### 高潮\n{chapter_summary_data['climax']}\n\n"
        if chapter_summary_data.get('ending'): formatted_outline_str += f"### 结尾\n{chapter_summary_data['ending']}\n\n"
        
        if chapter_data.get('characters'):
            formatted_outline_str += "## 出场人物\n"
            for char in chapter_data['characters']: 
                name = char.get('name', '')
                actions = char.get('actions', '')
                emotions = char.get('emotions', '')
                first_appearance = char.get('firstAppearance', False)
                
                char_line = f"- **{name}**: {actions}"
                if emotions:
                    char_line += f"，情感变化：{emotions}"
                if first_appearance:
                    char_line += f" 【初次登场角色，需要适当介绍】"
                formatted_outline_str += char_line + "\n"
            formatted_outline_str += "\n"
            
        if chapter_data.get('scenes'):
            formatted_outline_str += "## 必须包含的场景\n"
            for i, scene in enumerate(chapter_data['scenes'], 1): 
                formatted_outline_str += f"{i}. {scene}\n"
            formatted_outline_str += "【重要】只能使用以上场景，不允许出现任何其他场景！\n\n"
            
        if chapter_data.get('key_points'):
            formatted_outline_str += "## 关键情节点\n"
            for i, point in enumerate(chapter_data['key_points'], 1): 
                formatted_outline_str += f"{i}. {point}\n"
            formatted_outline_str += "【重要】必须包含所有关键情节点，不允许添加其他情节！\n\n"
            
        if chapter_data.get('foreshadowings') and isinstance(chapter_data['foreshadowings'], dict):
            fs = chapter_data['foreshadowings']
            formatted_outline_str += "## 伏笔管理\n"
            if fs.get('planted'):
                formatted_outline_str += "### 必须埋下的伏笔：\n"
                for plant in fs['planted']: 
                    formatted_outline_str += f"- ID:{plant.get('id', '')} 内容：{plant.get('content', '')} 埋下方式：{plant.get('method', '')}\n"
            if fs.get('revealed'):
                formatted_outline_str += "### 必须回收的伏笔：\n"
                for reveal in fs['revealed']: 
                    formatted_outline_str += f"- ID:{reveal.get('id', '')} 内容：{reveal.get('content', '')} 回收效果：{reveal.get('effect', '')}\n"
            if fs.get('planted') or fs.get('revealed'):
                formatted_outline_str += "【重要】严禁使用或提及上述伏笔之外的任何其他伏笔！\n\n"
                
        return formatted_outline_str
    
    def _format_characters(self, characters: List[Dict[str, Any]]) -> str:
        if not characters: return "无"
        formatted_chars_str = "本章出场人物：\n"
        for char in characters: 
            name = char.get('name', '')
            actions = char.get('actions', '')
            emotions = char.get('emotions', '')
            first_appearance = char.get('firstAppearance', False)
            
            char_line = f"- {name}: {actions}"
            if emotions:
                char_line += f"，情感变化：{emotions}"
            if first_appearance:
                char_line += f" 【初次登场角色，需要适当介绍】"
            formatted_chars_str += char_line + "\n"
        return formatted_chars_str
    
    def _format_scenes(self, scenes: List[str]) -> str:
        if not scenes: return "无特定场景要求"
        formatted_scenes_str = "必须包含的场景：\n"
        for i, scene in enumerate(scenes, 1):
            formatted_scenes_str += f"{i}. {scene}\n"
        formatted_scenes_str += "\n【重要】只能使用以上场景，不允许出现任何其他场景！"
        return formatted_scenes_str
    
    def _format_foreshadowings(self, foreshadowings: Dict[str, Any]) -> str:
        if not foreshadowings: return "无伏笔要求"
        formatted_fs_str = "伏笔管理：\n"
        
        has_content = False
        if foreshadowings.get('planted'):
            formatted_fs_str += "【必须埋下的伏笔】：\n"
            for plant in foreshadowings['planted']: 
                formatted_fs_str += f"- ID:{plant.get('id', '')} 内容：{plant.get('content', '')} 埋下方式：{plant.get('method', '')}\n"
            has_content = True
            
        if foreshadowings.get('revealed'):
            formatted_fs_str += "\n【必须回收的伏笔】：\n"
            for reveal in foreshadowings['revealed']: 
                formatted_fs_str += f"- ID:{reveal.get('id', '')} 内容：{reveal.get('content', '')} 回收效果：{reveal.get('effect', '')}\n"
            has_content = True
            
        if has_content:
            formatted_fs_str += "\n【重要】严禁使用或提及上述伏笔之外的任何其他伏笔！"
        else:
            formatted_fs_str = "无伏笔要求"
            
        return formatted_fs_str
    
    def _check_chapter(self, chapter_content: str, chapter_outline: str, 
                       characters: str, foreshadowing: str, style_guide: str = "") -> Dict[str, Any]:
        try:
            print("检查分两段生成的章节字数...")
            # 手动计算字数
            clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', chapter_content)
            word_count = len(clean_text)
            print(f"分两段生成章节总字数: {word_count}字")
            
            # 分两段生成的字数要求：2600-3000字（每段1300-1500字）
            if word_count < 2600:
                print(f"字数检查未通过：章节字数为{word_count}字，少于分两段生成最低要求的2600字")
                return {
                    "passed": False, 
                    "word_count": word_count,
                    "issues": [{
                        "type": "字数不足", 
                        "description": f"章节字数为{word_count}字，少于分两段生成最低要求的2600字", 
                        "suggestion": "请确保每段都达到1300-1500字"
                    }]
                }
            elif word_count > 3500:  # 给一些容忍度
                print(f"字数检查通过，但字数偏多：{word_count}字")
            else:
                print(f"分两段生成字数检查通过：{word_count}字")
            
            word_count_result = {"passed": True, "word_count": word_count}

            # if style_guide:
            #     print("检查章节风格...")
            #     style_result = self.api.check_chapter_style(chapter_content, style_guide)
            #     if style_result is None:
            #         print("警告: 风格检查返回为空，可能是API调用失败")
            #         style_result = {"passed": True, "issues": []}
            #     elif not style_result.get("passed", False):
            #         print(f"风格检查未通过: {style_result.get('issues', [])}"); return style_result
            # else: print("跳过风格检查，因为没有提供风格指南...")
            
            print("检查大纲冲突...")
            outline_result_json = self.api.check_chapter_outline(chapter_content, chapter_outline, foreshadowing)
            if outline_result_json is None:
                print("警告: 大纲冲突检查返回为空，可能是API调用失败")
                outline_result = {"passed": True, "issues": []}
            else:
                try: 
                    outline_result = json.loads(outline_result_json)
                except (json.JSONDecodeError, TypeError) as e:
                    print(f"解析大纲检查结果时出错: {str(e)}")
                    return {"passed": False, "issues": [{"type": "解析错误", "description": f"无法解析大纲检查结果: {str(e)}", "suggestion": "请重新检查"}]}
            
            if not outline_result.get("passed", False):
                print(f"大纲冲突检查未通过: {outline_result.get('issues', [])}"); return outline_result
            
            # 检查是否有任何问题，如果有，则即使不严重也不通过
            final_issues = []
            if outline_result.get("issues"):
                final_issues.extend(outline_result.get("issues"))
            
            if final_issues:
                print(f"章节验证发现{len(final_issues)}个问题，章节验证未通过!")
                return {"passed": False, "issues": final_issues}
            else:
                print("分两段生成章节验证通过!")
                return {"passed": True, "issues": []}
        except Exception as e:
            print(f"章节验证过程中出错: {str(e)}"); traceback.print_exc()
            return {"passed": False, "issues": [{"type": "验证错误", "description": f"验证过程中出错: {str(e)}", "suggestion": "请检查章节内容并重试"}]}
    
    def fix_chapter(self, chapter_content: str, issues: List[Dict[str, Any]], 
                    chapter_outline: str, characters: str, foreshadowing: str, 
                    issue_type: str) -> Optional[str]:
        try:
            fix_suggestions = []
            for issue in issues:
                sugg, desc = issue.get("suggestion", ""), issue.get("description", "")
                detail = f"问题：{desc}\n建议：{sugg}\n" if desc or sugg else ""
                if detail: fix_suggestions.append(detail)
            
            if not fix_suggestions:
                if "背景" in issue_type: fix_suggestions = ["确保章节内容中的世界观、设定与背景信息保持一致"]
                elif "人物" in issue_type: fix_suggestions = ["确保人物行为、能力与已有人物设定一致"]
                elif "大纲" in issue_type: fix_suggestions = ["确保章节情节与大纲保持一致"]
                else: fix_suggestions = [f"修正{issue_type}中存在的问题"]
            
            print(f"正在修复章节中的{issue_type}问题... 修复建议: {fix_suggestions}")
            fixed_content_str = self.api.fix_chapter_content(chapter_content, fix_suggestions, chapter_outline, "", characters, foreshadowing, issue_type)
            if not fixed_content_str: 
                print(f"修复{issue_type}问题失败"); 
                return None
            
            # 直接使用原始内容防止返回None导致处理失败
            fixed_content_str = re.sub(r'^\(注意：.*?\)\s*[\n\r]*', '', fixed_content_str, flags=re.MULTILINE)
            fixed_content_str = re.sub(r'^【.*?】\s*[\n\r]*', '', fixed_content_str, flags=re.MULTILINE)
            fixed_content_str = re.sub(r'^（[^）]*）[\n\r]+', '', fixed_content_str)
            fixed_content_str = re.sub(r'^---[\n\r]+', '', fixed_content_str)
            fixed_content_str = re.sub(r'\*\*[^*]+\*\*', '', fixed_content_str) 
            fixed_content_str = re.sub(r'（[^）]*部分[^）]*）', '', fixed_content_str)
            fixed_content_str = re.sub(r'（[^）]*修复[^）]*）', '', fixed_content_str)
            fixed_content_str = re.sub(r'_(.+?)_', r'\1', fixed_content_str)
            fixed_content_str = re.sub(r'-{3,}[\n\r]+', '', fixed_content_str)
            fixed_content_str = re.sub(r'【[^】]*修复[^】]*】[\n\r]+', '', fixed_content_str)
            fixed_content_str = re.sub(r'【[^】]*调整[^】]*】[\n\r]+', '', fixed_content_str)

            cleaned_content_str = fixed_content_str.strip()
            if cleaned_content_str and cleaned_content_str[-1] not in '。！？.!?"\'》）)':
                print(f"警告：修复后的章节内容可能不完整，最后一个字符是：{cleaned_content_str[-1]}")
                cleaned_content_str += "。"
            
            print(f"章节中的{issue_type}问题已修复")
            return cleaned_content_str
        except Exception as e:
            print(f"修复章节内容时出错: {str(e)}"); traceback.print_exc()
            return None

    def continue_novel_generation(self, outline_path: str, output_dir: str = 'output_new') -> None:
        os.makedirs(output_dir, exist_ok=True)
        self.output_dir = output_dir
        
        output_outline_path = os.path.join(output_dir, 'outline.json')
        if os.path.abspath(outline_path) != os.path.abspath(output_outline_path):
            shutil.copy(outline_path, output_outline_path); print(f"已将大纲文件复制到: {output_outline_path}")
        
        with open(outline_path, 'r', encoding='utf-8') as f: outline_data = json.load(f)
        
        style_guide_path = os.path.join(self.output_dir, 'style_guide.txt')
        if not os.path.exists(style_guide_path): style_guide_path = os.path.join(os.path.dirname(outline_path), 'style_guide.txt')
        
        style_guide = ""
        if os.path.exists(style_guide_path):
            with open(style_guide_path, 'r', encoding='utf-8') as f: style_guide = f.read()
            print(f"已加载风格指南: {style_guide_path}")
        else: print("未找到风格指南文件，将使用空风格指南")
        
        chapters = outline_data.get('outlines', [])
        total_chapters = len(chapters)
        start_chapter_idx = 0 
        for i, ch_info in enumerate(chapters):
            ch_num_check = ch_info.get('index', i + 1)
            ch_file_check = os.path.join(output_dir, f'chapter_{ch_num_check}.txt')
            if os.path.exists(ch_file_check): start_chapter_idx = i + 1 
        
        if start_chapter_idx >= len(chapters):
            print(f"所有章节都已生成完成，共{total_chapters}章"); return
            
        print(f"在大纲文件中找到 {total_chapters} 个章节")
        next_chapter_to_generate_num = chapters[start_chapter_idx].get('index', start_chapter_idx + 1)
        print(f"已生成 {start_chapter_idx} 章，将从第 {next_chapter_to_generate_num} 章继续生成")
        print(f"输出目录: {output_dir}")
        
        for current_idx_in_chapters_list in range(start_chapter_idx, len(chapters)):
            current_chapter_data = chapters[current_idx_in_chapters_list]
            chapter_number = current_chapter_data.get('index', current_idx_in_chapters_list + 1)
            processing_chapter_count = current_idx_in_chapters_list - start_chapter_idx + 1 
            total_to_process = len(chapters) - start_chapter_idx
            print(f"\n正在生成第 {chapter_number} 章 (继续生成队列中的第 {processing_chapter_count}/{total_to_process})...")
            
            previous_chapters_list_data = chapters[:current_idx_in_chapters_list] if current_idx_in_chapters_list > 0 else None
            chapter_content = self._generate_chapter(current_chapter_data, chapter_number, total_chapters, previous_chapters_list_data, style_guide)
            
            if chapter_content:
                chapter_file = os.path.join(output_dir, f'chapter_{chapter_number}.txt')
                title_match = re.search(r'^(第.+?章\s*.+?)[\n\r]', chapter_content)
                if title_match:
                    title = title_match.group(1).strip()
                    clean_content = re.sub(r'^第.+?章\s*.+?[\n\r]+', '', chapter_content, 1)
                    md_title_pattern = re.compile(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]')
                    clean_content = re.sub(md_title_pattern, '', clean_content)
                    clean_content = re.sub(r'[\n\r]+第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]+', '\n\n', clean_content)
                    clean_content = re.sub(r'[\n\r]{3,}', '\n\n', clean_content).strip()
                    chapter_content = f"{title}\n\n{clean_content}"
                
                with open(chapter_file, 'w', encoding='utf-8') as f: f.write(chapter_content)
                print(f"第 {chapter_number} 章已生成并保存到 {chapter_file}")
            else: print(f"生成第 {chapter_number} 章失败")
        
        print("\n所有剩余章节已成功生成！")

def generate_chapters_from_outline(outline_path: str, output_dir: str = 'output_new') -> None:
    generator = ChapterGenerator()
    generator.generate_all_chapters(outline_path, output_dir)

def generate_novel_style_and_background(genre: str, output_dir: str = 'output_new') -> bool:
    generator = ChapterGenerator()
    return generator.generate_novel_from_outline(genre, output_dir)

def continue_novel_generation_entry(outline_path: str, output_dir: str = 'output_new') -> None:
    generator = ChapterGenerator()
    generator.continue_novel_generation(outline_path, output_dir)

if __name__ == "__main__":
    # import sys # No need to re-import if it's at module top, but harmless
    
    if len(sys.argv) > 1 and sys.argv[1] == "generate_style":
        genre_arg = "玄幻" if len(sys.argv) <= 2 else sys.argv[2]
        output_dir_arg = "output_new" if len(sys.argv) <= 3 else sys.argv[3]
        print(f"生成{genre_arg}流派小说的风格和背景设定...")
        print(f"输出目录: {output_dir_arg}")
        generate_novel_style_and_background(genre_arg, output_dir_arg)
    elif len(sys.argv) > 1 and sys.argv[1] == "continue":
        outline_path_arg = "output_new/outline.json" if len(sys.argv) <= 2 else sys.argv[2]
        output_dir_arg = "output_new" if len(sys.argv) <= 3 else sys.argv[3]
        print(f"从当前章节继续生成小说: {outline_path_arg}")
        print(f"输出目录: {output_dir_arg}")
        continue_novel_generation_entry(outline_path_arg, output_dir_arg)
    else:
        outline_path_arg = "output_new/outline.json" 
        output_dir_arg = "output_new" 
        
        if len(sys.argv) > 1: outline_path_arg = sys.argv[1]
        if len(sys.argv) > 2: output_dir_arg = sys.argv[2]
        
        print(f"从大纲生成章节: {outline_path_arg}")
        print(f"输出目录: {output_dir_arg}")
        generate_chapters_from_outline(outline_path_arg, output_dir_arg)
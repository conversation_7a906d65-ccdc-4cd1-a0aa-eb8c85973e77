"""
配置文件，包含API密钥和URL
"""

import os

# API配置
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"
GEMINI_API_KEY = "AIzaSyDk8l7ZAMG5Lto4VUdno6jBi86JuJPqU6w"  # 请替换为您的实际密钥

DEEPSEEK_API_URL = "https://llm.chutes.ai/v1/chat/completions"
DEEPSEEK_API_KEY = "cpk_58e3e3495ba943879f542cc50e58d722.876b2db21b375ffb9da24de737a59649.LinjKLd1Wl6p7El2zy34Dgn8E2A2lR7G"  # 请替换为您的实际密钥

# LLM生成参数配置
DEFAULT_TEMPERATURE = 0.5  # 默认温度参数
# 不同任务类型的温度参数
CREATIVE_TEMPERATURE = 0.7  # 创意性任务（如章节内容、大纲生成）
ANALYTICAL_TEMPERATURE = 0.3  # 分析性任务（如检查冲突、分析结构）
FACTUAL_TEMPERATURE = 0.1  # 事实性任务（如修复JSON格式）

# 输出配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
OUTPUT_DIR = os.path.join(BASE_DIR, "output")

# 创建输出目录（如果不存在）
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

# 系统参数
MAX_RETRIES = 10  # API调用最大重试次数
TIMEOUT = 600  # API调用超时时间（秒）
MIN_CHAPTER_LENGTH = 4000  # 最小章节长度（字）- 传统模式
MIN_CHAPTER_LENGTH_OUTLINE = 2500  # 最小章节长度（字）- generate_from_outline模式
MAX_CHAPTER_LENGTH_OUTLINE = 3000  # 最大章节长度（字）- generate_from_outline模式
DEFAULT_TOTAL_CHAPTERS = 100  # 默认总章节数
DEFAULT_CHAPTER_LENGTH = 5000  # 默认章节长度（字）
TARGET_NOVEL_LENGTH = 450000  # 目标小说总长度（字，45万字左右）
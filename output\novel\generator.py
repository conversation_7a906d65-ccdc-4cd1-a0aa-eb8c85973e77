"""
小说生成控制模块
"""

import os
import json
import math
import traceback
from typing import Dict, Any, List, Optional, Tuple, Union

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import OUTPUT_DIR, DEFAULT_CHAPTER_LENGTH, TARGET_NOVEL_LENGTH, MAX_RETRIES, DEFAULT_TOTAL_CHAPTERS
from llm.deepseek_all import DeepSeekAllAPI
from models.background import Background
from models.outline import NovelOutline, ChapterOutline
from models.character import CharacterManager, Character
from models.foreshadowing import ForeshadowingManager, Foreshadowing
from utils.file_manager import read_chapter, save_chapter, load_novel_info, save_novel_info, export_novel_as_txt
from utils.json_helper import parse_json_safely
from novel.checker import NovelChecker
import re
import logging
from datetime import datetime

class NovelGenerator:
    """小说生成控制器"""

    def __init__(self):
        """初始化小说生成器"""
        self.init_apis()
        self.genre = ""
        self.style_guide = ""
        self._background = None
        self._background_locked = False
        self.total_chapters = DEFAULT_TOTAL_CHAPTERS
        self.current_chapter = 0
        self.outline = None
        self.character_manager = CharacterManager()
        self.foreshadowing_manager = ForeshadowingManager()
        self.main_storyline = None
        self.main_storyline_check = {"checked": False}
        # 用于控制完全重新生成
        self.force_regenerate = False

        # 创建输出目录
        if not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)

    @property
    def background(self):
        """获取背景设定"""
        return self._background

    @background.setter
    def background(self, value):
        """设置背景设定，在锁定后不允许修改"""
        if hasattr(self, '_background_locked') and self._background_locked and self._background is not None:
            print("警告：背景设定已锁定，不允许修改")
            return
        self._background = value

    def init_project(self, genre: str, title: str = "", author: str = "AI作家") -> bool:
        """
        初始化项目

        Args:
            genre: 小说流派
            title: 小说标题（已废弃，将自动生成）
            author: 作者

        Returns:
            初始化是否成功
        """
        try:
            self.genre = genre

            # 生成风格提示词
            print(f"正在生成{genre}流派的风格提示词...")
            self.style_guide = self.api.generate_novel_style(
                genre,
                target_length=TARGET_NOVEL_LENGTH,
                total_chapters=self.total_chapters
            )
            if not self.style_guide:
                print("生成风格提示词失败")
                return False

            # 保存风格提示词到文件
            from utils.file_manager import save_style_guide
            save_style_guide(self.style_guide)
            print("风格提示词生成成功")

            # 生成背景设定
            print("正在生成背景设定...")
            background_data = self.api.generate_background(genre, self.style_guide)
            if not background_data:
                print("生成背景设定失败")
                return False

            self.background = Background(
                genre,
                background_data.get("content", ""),
                background_data.get("categories", {})
            )
            self.background.save()
            print("背景设定生成成功")

            # 锁定背景设定，防止后续修改
            self._background_locked = True

            # 预估章节数
            self.total_chapters = math.ceil(TARGET_NOVEL_LENGTH / DEFAULT_CHAPTER_LENGTH)
            print(f"预计总章节数: {self.total_chapters}")

            # 生成故事主线概述
            print("正在生成故事主线概述...")
            main_storyline = None
            max_retries = MAX_RETRIES
            for attempt in range(max_retries):
                # 如果是第一次尝试，完全重新生成
                if attempt == 0 or main_storyline is None:
                    main_storyline = self.api.generate_main_storyline(
                        genre, self.style_guide, str(self.background), self.total_chapters
                    )
                else:
                    # 如果是重试，基于上一次生成的内容进行修改
                    print(f"正在重新生成故事主线 ({attempt + 1}/{max_retries})，基于上一次生成的内容进行修改...")
                    main_storyline = self.api.generate_main_storyline(
                        genre, self.style_guide, str(self.background), self.total_chapters,
                        original_storyline=main_storyline  # 传入上一次生成的内容
                    )

                if not main_storyline:
                    print("生成故事主线概述失败")
                    if attempt < max_retries - 1:
                        print(f"正在重试 ({attempt + 1}/{max_retries})...")
                        continue
                    return False

                # 解析故事主线数据以进行质量检查
                try:
                    storyline_data = json.loads(main_storyline)
                    # 从utils.fix_storyline导入质量检查函数
                    from utils.fix_storyline import validate_storyline_quality

                    # 进行质量检查
                    print("正在检查故事主线质量...")
                    quality_check = validate_storyline_quality(storyline_data)

                    if not quality_check["valid"]:
                        print(f"故事主线质量检查未通过，问题: {quality_check['issues']}")
                        if attempt < max_retries - 1:
                            print(f"正在重新生成故事主线 ({attempt + 1}/{max_retries})...")
                            continue
                        else:
                            print(f"已达到最大重试次数({max_retries})，使用最后一次生成的故事主线")
                    else:
                        print(f"故事主线质量检查通过，总字数: {quality_check['total_length']}")
                        if quality_check.get('character_names'):
                            print(f"检测到的角色名称: {', '.join(quality_check['character_names'])}")
                except Exception as e:
                    print(f"故事主线质量检查失败: {str(e)}")
                    # 失败不阻止流程，继续使用当前生成的故事主线

                # 直接保存故事主线，不进行校验
                from utils.file_manager import save_main_storyline
                save_main_storyline(main_storyline)
                print("故事主线概述生成成功")

                # 初始化大纲，并包含主线信息
                self.outline = NovelOutline(total_chapters=self.total_chapters, main_storyline=main_storyline)
                self.outline.save()
                break

            if not main_storyline:
                print("生成故事主线概述失败")
                return False

            # 生成人物卡片
            print("正在生成人物卡片...")
            characters_content = None
            max_retries = MAX_RETRIES
            for attempt in range(max_retries):
                characters_content = self.api.generate_characters(
                    genre, self.style_guide, str(self.background), main_storyline
                )
                if not characters_content:
                    print("生成人物卡片失败")
                    if attempt < max_retries - 1:
                        print(f"正在重试 ({attempt + 1}/{max_retries})...")
                        continue
                    return False

                # 解析人物卡片
                try:
                    # 尝试提取JSON部分
                    import re
                    json_match = re.search(r'```json\s*(.*?)\s*```', characters_content, re.DOTALL)
                    if json_match:
                        characters_content = json_match.group(1)

                    # 清理可能导致解析错误的内容
                    # 移除注释
                    characters_content = re.sub(r'//.*?(\n|$)', '\n', characters_content)
                    # 修复可能的格式问题
                    characters_content = characters_content.replace("'", '"')  # 将单引号替换为双引号
                    characters_content = re.sub(r',\s*}', '}', characters_content)  # 移除对象末尾多余的逗号
                    characters_content = re.sub(r',\s*]', ']', characters_content)  # 移除数组末尾多余的逗号

                    # 尝试解析JSON
                    try:
                        characters_data = json.loads(characters_content)
                    except json.JSONDecodeError as e:
                        print(f"JSON解析错误: {str(e)}")
                        print("尝试修复JSON格式...")

                        # 尝试修复relationships字段的格式问题
                        # 查找类似 "relationships": {"角色名": "关系描述"} 的模式
                        rel_pattern = r'"relationships"\s*:\s*{([^}]*)}'
                        rel_matches = re.finditer(rel_pattern, characters_content)

                        for match in rel_matches:
                            rel_content = match.group(1)
                            # 检查是否有字符串值而不是数组
                            str_rel_pattern = r'"([^"]*)"\s*:\s*"([^"]*)"'
                            str_rel_matches = re.finditer(str_rel_pattern, rel_content)

                            for str_match in str_rel_matches:
                                char_name = str_match.group(1)
                                rel_desc = str_match.group(2)
                                # 将字符串值替换为数组
                                old_str = f'"{char_name}": "{rel_desc}"'
                                new_str = f'"{char_name}": ["{rel_desc}"]'
                                characters_content = characters_content.replace(old_str, new_str)

                        # 再次尝试解析
                        try:
                            characters_data = json.loads(characters_content)
                        except json.JSONDecodeError as e:
                            print(f"修复后仍然无法解析JSON: {str(e)}")
                            # 如果仍然失败，重新生成人物卡片
                            if attempt < max_retries - 1:
                                print(f"正在重新生成人物卡片 ({attempt + 1}/{max_retries})...")
                                continue
                            else:
                                print(f"已达到最大重试次数({max_retries})，使用最后一次生成的人物卡片")
                                raise

                    # 检查人物是否与故事主线冲突
                    print("检查人物是否与故事主线冲突...")
                    storyline_check = self.api.check_characters_storyline(
                        json.dumps(characters_data, ensure_ascii=False),
                        main_storyline
                    )
                    if not storyline_check.get("passed", False):
                        print(f"人物与故事主线冲突，问题: {storyline_check.get('issues', [])}")
                        if attempt < max_retries - 1:
                            print(f"正在重新生成人物 ({attempt + 1}/{max_retries})...")
                            continue
                        else:
                            print(f"已达到最大重试次数({max_retries})，使用最后一次生成的人物")

                    # 如果检查通过或已达到最大重试次数，创建人物管理器
                    self.character_manager = CharacterManager()

                    for char_data in characters_data.get("characters", []):
                        # 确保relationships字段的值是字典，且字典的值是列表
                        relationships = char_data.get("relationships", {})
                        processed_relationships = {}

                        for rel_name, rel_value in relationships.items():
                            if isinstance(rel_value, str):
                                # 如果是字符串，转换为列表
                                processed_relationships[rel_name] = [rel_value]
                            elif isinstance(rel_value, list):
                                # 如果是列表，保持不变
                                processed_relationships[rel_name] = rel_value
                            else:
                                # 其他情况，忽略
                                print(f"警告：角色 '{char_data.get('name', '')}' 的关系数据格式不正确")

                        # 更新关系数据
                        char_data["relationships"] = processed_relationships

                        # 添加：确保appearance_chapters字段存在且格式正确
                        if "appearance_chapters" not in char_data or not char_data["appearance_chapters"]:
                            # 如果不存在，根据角色生成默认值
                            role = char_data.get("role", "").strip().lower()
                            first_appearance = char_data.get("first_appearance_chapter", 1)

                            if role == "主角":
                                # 主角默认出现在所有章节
                                char_data["appearance_chapters"] = list(range(1, self.total_chapters + 1))
                            elif role in ["女主", "主要配角"]:
                                # 女主和主要配角出现在更多章节
                                # 默认从首次出场开始，每隔1-2章出场一次
                                appearances = [first_appearance]
                                for i in range(first_appearance + 2, self.total_chapters + 1, 2):
                                    appearances.append(i)
                                char_data["appearance_chapters"] = appearances
                            elif role in ["主要反派"]:
                                # 主要反派断续出场
                                appearances = [first_appearance]
                                for i in range(first_appearance + 3, self.total_chapters + 1, 3):
                                    appearances.append(i)
                                # 反派通常会在结局前出场
                                appearances.extend([self.total_chapters-1, self.total_chapters])
                                char_data["appearance_chapters"] = sorted(list(set(appearances)))
                            else:
                                # 次要角色只出现在少量章节
                                char_data["appearance_chapters"] = [first_appearance]
                                # 随机选择2-3章额外出场
                                import random
                                extra_chapters = random.sample(
                                    range(first_appearance + 1, self.total_chapters + 1),
                                    min(3, self.total_chapters - first_appearance)
                                )
                                char_data["appearance_chapters"].extend(extra_chapters)

                            print(f"为角色 '{char_data.get('name', '')}' 自动生成出场章节: {char_data['appearance_chapters']}")
                        else:
                            # 确保appearance_chapters是数字列表
                            if isinstance(char_data["appearance_chapters"], list):
                                # 转换所有元素为整数
                                try:
                                    char_data["appearance_chapters"] = [int(ch) for ch in char_data["appearance_chapters"]]
                                    print(f"角色 '{char_data.get('name', '')}' 的出场章节: {char_data['appearance_chapters']}")
                                except (ValueError, TypeError):
                                    print(f"警告：角色 '{char_data.get('name', '')}' 的出场章节数据格式不正确，使用默认值")
                                    char_data["appearance_chapters"] = [char_data.get("first_appearance_chapter", 1)]
                            else:
                                print(f"警告：角色 '{char_data.get('name', '')}' 的出场章节数据格式不正确，使用默认值")
                                char_data["appearance_chapters"] = [char_data.get("first_appearance_chapter", 1)]

                        # 使用add_character_from_dict方法，而不是直接创建Character对象
                        # 这样会自动根据角色类型和关系设置relevance_to_main_plot
                        self.character_manager.add_character_from_dict(char_data)

                    self.character_manager.save()
                    print(f"人物卡片生成成功，共{len(self.character_manager.characters)}个角色")
                    break
                except Exception as e:
                    print(f"解析人物卡片时出错: {str(e)}")
                    if attempt < max_retries - 1:
                        print(f"正在重新生成人物 ({attempt + 1}/{max_retries})...")
                        continue
                    else:
                        print(f"已达到最大重试次数({max_retries})，使用原始人物卡片文本")

                        # 如果解析失败，将整个文本作为人物卡片内容
                        self.character_manager = CharacterManager([
                            Character(
                                name="原始人物卡片",
                                role="集合",
                                basic_info={},
                                background=characters_content,
                                current_power="未知"
                            )
                        ])
                        self.character_manager.save()
                        break

            if not characters_content:
                print("生成人物卡片失败")
                return False

            # 生成初始伏笔管理（在大纲生成之前）
            print("正在生成初始伏笔管理...")
            foreshadowing_content = self.api.generate_foreshadowing(
                genre, self.style_guide, str(self.background),
                json.dumps(self.character_manager.to_dict(), ensure_ascii=False),
                main_storyline,  # 使用故事主线代替章节大纲
                self.total_chapters  # 添加总章节数参数
            )
            if not foreshadowing_content:
                print("生成初始伏笔管理失败")
                return False

            # 解析伏笔管理，使用增强的JSON解析工具
            try:
                # 使用安全的JSON解析函数
                foreshadowing_data = parse_json_safely(foreshadowing_content, "foreshadowing")

                if not foreshadowing_data:
                    raise Exception("无法从伏笔内容中解析JSON数据")

                self.foreshadowing_manager = ForeshadowingManager()

                for fs_data in foreshadowing_data.get("foreshadowings", []):
                    foreshadowing = Foreshadowing(
                        id=fs_data.get("id", ""),
                        type=fs_data.get("type", ""),
                        content=fs_data.get("content", ""),
                        plant_chapter=fs_data.get("plant_chapter", 0),
                        reveal_chapter=fs_data.get("reveal_chapter", 0),
                        importance=fs_data.get("importance", ""),
                        plant_method=fs_data.get("plant_method", ""),
                        reveal_effect=fs_data.get("reveal_effect", ""),
                        status=fs_data.get("status", "未埋下"),
                        related_characters=fs_data.get("related_characters", [])
                    )
                    # 传递总章节数参数
                    self.foreshadowing_manager.add_foreshadowing(foreshadowing, self.total_chapters)

                self.foreshadowing_manager.save()
                print(f"初始伏笔管理生成成功，共{len(self.foreshadowing_manager.foreshadowings)}个伏笔")
            except Exception as e:
                print(f"解析初始伏笔管理时出错: {str(e)}")
                print("尝试重新生成初始伏笔管理...")

                # 最大重试次数
                max_retries = MAX_RETRIES
                for attempt in range(max_retries):
                    try:
                        # 重新生成伏笔管理
                        print(f"重新生成初始伏笔管理 (尝试 {attempt+1}/{max_retries})...")
                        foreshadowing_content = self.api.generate_foreshadowing(
                            genre, self.style_guide, str(self.background),
                            json.dumps(self.character_manager.to_dict(), ensure_ascii=False),
                            main_storyline,  # 使用故事主线代替章节大纲
                            self.total_chapters  # 添加总章节数参数
                        )

                        if not foreshadowing_content:
                            print("重新生成初始伏笔管理失败")
                            if attempt < max_retries - 1:
                                continue
                            else:
                                raise Exception("多次尝试生成初始伏笔管理均失败")

                        # 使用新的JSON解析工具进行解析
                        foreshadowing_data = parse_json_safely(foreshadowing_content, "foreshadowing")

                        if not foreshadowing_data:
                            raise Exception("无法从伏笔内容中解析JSON数据")

                        # 如果成功解析，创建伏笔管理器
                        self.foreshadowing_manager = ForeshadowingManager()

                        for fs_data in foreshadowing_data.get("foreshadowings", []):
                            foreshadowing = Foreshadowing(
                                id=fs_data.get("id", ""),
                                type=fs_data.get("type", ""),
                                content=fs_data.get("content", ""),
                                plant_chapter=fs_data.get("plant_chapter", 0),
                                reveal_chapter=fs_data.get("reveal_chapter", 0),
                                importance=fs_data.get("importance", ""),
                                plant_method=fs_data.get("plant_method", ""),
                                reveal_effect=fs_data.get("reveal_effect", ""),
                                status=fs_data.get("status", "未埋下"),
                                related_characters=fs_data.get("related_characters", [])
                            )
                            # 传递总章节数参数
                            self.foreshadowing_manager.add_foreshadowing(foreshadowing, self.total_chapters)

                        self.foreshadowing_manager.save()
                        print(f"初始伏笔管理重新生成成功，共{len(self.foreshadowing_manager.foreshadowings)}个伏笔")
                        break
                    except Exception as retry_error:
                        print(f"重新生成初始伏笔管理时出错 (尝试 {attempt+1}/{max_retries}): {str(retry_error)}")
                        if attempt < max_retries - 1:
                            print(f"将进行第{attempt+2}次尝试...")
                        else:
                            print(f"已达到最大重试次数({max_retries})，初始伏笔管理生成失败")
                            # 创建一个空的伏笔管理器以避免后续错误
                            self.foreshadowing_manager = ForeshadowingManager()
                            self.foreshadowing_manager.save()
                            print("创建了一个空的伏笔管理器")

            # 生成完整的小说大纲
            print("正在生成完整的小说大纲...")
            for chapter_number in range(1, self.total_chapters + 1):
                print(f"正在生成第{chapter_number}章大纲 ({chapter_number}/{self.total_chapters})...")
                success = self.generate_chapter_outline(chapter_number)
                if not success:
                    print(f"生成第{chapter_number}章大纲失败")
                    return False

                # 每生成5章暂停一下，避免API限制
                if chapter_number % 5 == 0 and chapter_number < self.total_chapters:
                    print(f"已完成{chapter_number}章大纲生成，暂停5秒...")
                    import time
                    time.sleep(5)

            # 伏笔已根据主线一次性生成，无需再次生成
            print("已根据主线一次性生成所有伏笔，无需再次生成伏笔")

            print(f"完整小说大纲生成成功，共{self.total_chapters}章")

            # 保存小说信息
            # 自动生成标题，不使用传入的标题参数
            # 尝试从人物和背景中提取合适的标题
            main_character = self.character_manager.get_main_character()
            if main_character:
                title = f"{main_character.name}的{self.genre}之旅"
            else:
                title = f"{self.genre}奇缘"

            save_novel_info(
                title=title,
                author=author,
                genre=genre,
                description=f"一部{genre}风格的男频网络小说，预计{self.total_chapters}章，约{TARGET_NOVEL_LENGTH/10000}万字。"
            )

            print(f"项目初始化成功：《{title}》")
            
            # 修复大纲中未成功生成的章节
            print("\n开始检查和修复大纲中未成功生成的章节...")
            self.fix_outline()
            
            return True
        except Exception as e:
            print(f"初始化项目时出错: {str(e)}")
            traceback.print_exc()
            return False

    def load_project(self) -> bool:
        """
        加载现有项目数据
        """
        try:
            # 加载小说信息
            print("正在加载项目信息...")
            info_path = os.path.join(OUTPUT_DIR, "novel_info.json")
            if not os.path.exists(info_path):
                print(f"错误: 项目信息文件{info_path}不存在")
                return False
            with open(info_path, "r", encoding="utf8") as f:
                info = json.load(f)

            # 检查信息完整性
            required_fields = ["title", "author", "genre"]
            for field in required_fields:
                if field not in info:
                    print(f"错误: 项目信息缺少{field}字段")
                    return False

            # 设置项目信息
            self.title = info["title"]
            self.author = info["author"]
            self.genre = info["genre"]

            # 加载小说风格
            style_path = os.path.join(OUTPUT_DIR, "style_guide.txt")
            if os.path.exists(style_path):
                with open(style_path, "r", encoding="utf8") as f:
                    self.style_guide = f.read()
                print("加载风格信息成功")
            else:
                print("警告: 风格文件不存在，将使用默认风格")
                self.style_guide = ""

            # 加载背景信息
            background_path = os.path.join(OUTPUT_DIR, "background.json")
            if os.path.exists(background_path):
                with open(background_path, "r", encoding="utf8") as f:
                    self.background = json.load(f)
                print("加载背景信息成功")
            else:
                print("警告: 背景文件不存在，后续将生成")
                self._background = None

            # 加载角色信息
            characters_path = os.path.join(OUTPUT_DIR, "characters.json")
            self.characters = CharacterManager()
            if os.path.exists(characters_path):
                character_loaded = self.characters.load(characters_path)
                if character_loaded:
                    print("加载角色信息成功")
                else:
                    print("警告: 角色信息加载失败")
            else:
                print("警告: 角色文件不存在，后续将生成")

            # 加载故事主线
            print("正在加载故事主线...")
            main_storyline_path = os.path.join(OUTPUT_DIR, "main_storyline.json")
            if not os.path.exists(main_storyline_path):
                print("故事主线文件不存在，需要生成")
                self.main_storyline = None
                return True

            # 读取故事主线文件内容
            with open(main_storyline_path, "r", encoding="utf8") as f:
                main_storyline_content = f.read()

            # 首先尝试直接解析
            try:
                main_storyline = json.loads(main_storyline_content)
                print("故事主线文件可以直接解析，无需修复")
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {str(e)}，尝试使用修复机制")

                # 1. 优先使用LLM修复
                fixed = False
                try:
                    print("尝试使用LLM修复故事主线JSON...")
                    from utils.json_helper import llm_fix_json
                    from llm.deepseek_all import DeepSeekAllAPI

                    # 创建API实例
                    api = self.api or DeepSeekAllAPI()

                    # 使用LLM修复
                    fixed_json_str = llm_fix_json(main_storyline_content, "故事主线", api)
                    if fixed_json_str:
                        try:
                            main_storyline = json.loads(fixed_json_str)
                            print("使用LLM成功修复故事主线JSON")
                            fixed = True
                        except json.JSONDecodeError:
                            print("LLM修复后的JSON仍然存在问题，尝试其他方法")
                except Exception as e:
                    print(f"LLM修复失败: {str(e)}，尝试使用代码修复")

                # 2. 如果LLM修复失败，使用代码修复工具
                if not fixed:
                    try:
                        from utils.fix_storyline import fix_storyline_json
                        print("尝试使用代码工具修复故事主线JSON...")
                        main_storyline = fix_storyline_json(main_storyline_content)
                        print(f"使用代码工具修复故事主线成功，共{len(main_storyline.get('outlines', []))}章")
                        fixed = True
                    except Exception as e:
                        print(f"代码修复失败: {str(e)}")

                # 3. 如果所有修复方法都失败，返回错误
                if not fixed:
                    print("所有修复方法都失败，无法加载故事主线")
                    return False

                # 保存修复后的故事主线，确保数据标准化
                fixed_path = os.path.join(OUTPUT_DIR, "main_storyline_fixed.json")
                with open(fixed_path, "w", encoding="utf8") as f:
                    json.dump(main_storyline, f, ensure_ascii=False, indent=2)
                print(f"已将修复后的故事主线保存到 {fixed_path}")

            self.main_storyline = main_storyline

            # 加载章节总数
            self.total_chapters = len(main_storyline.get("outlines", []))
            print(f"故事总章节数: {self.total_chapters}")

            # 检查故事主线质量
            try:
                from utils.fix_storyline import validate_storyline_quality
                print("正在检查故事主线质量...")
                quality_check = validate_storyline_quality(main_storyline)

                if not quality_check["valid"]:
                    print(f"故事主线质量检查未通过，问题: {quality_check['issues']}")
                    print("建议使用 'python main.py generate_all --force' 重新生成所有内容")

                    # 询问是否要立即重新生成故事主线
                    if input("是否立即重新生成故事主线? (y/n): ").lower() == 'y':
                        print("正在重新生成故事主线...")
                        if self.regenerate_main_storyline():
                            print("故事主线重新生成成功")
                            # 更新main_storyline变量
                            with open(main_storyline_path, "r", encoding="utf8") as f:
                                self.main_storyline = json.load(f)
                                # 更新章节总数
                                self.total_chapters = len(self.main_storyline.get("outlines", []))
                                print(f"更新后的故事总章节数: {self.total_chapters}")
                        else:
                            print("故事主线重新生成失败，将继续使用现有故事主线")
                else:
                    print(f"故事主线质量检查通过，总字数: {quality_check['total_length']}")
                    if quality_check.get('character_names'):
                        print(f"检测到的角色名称: {', '.join(quality_check['character_names'])}")
            except Exception as e:
                print(f"故事主线质量检查失败: {str(e)}")
                # 失败不阻止流程，继续使用当前已加载的故事主线

            # 加载伏笔
            foreshadowing_path = os.path.join(OUTPUT_DIR, "foreshadowings.json")
            self.foreshadowing = ForeshadowingManager()
            if os.path.exists(foreshadowing_path):
                self.foreshadowing.load(foreshadowing_path)
                print("加载伏笔信息成功")
            else:
                print("警告: 伏笔文件不存在，后续将生成")

            # 加载大纲
            print("正在加载章节大纲...")
            from models.outline import ChapterOutlineManager
            self.outline = ChapterOutlineManager.load(self.total_chapters)
            if not self.outline:
                print("章节大纲加载失败或不存在，将创建新的大纲管理器")
                self.outline = ChapterOutlineManager(self.total_chapters)

            # 确定当前章节
            self.current_chapter = self.outline.get_last_generated_chapter() or 0
            print(f"当前进度: {self.current_chapter}/{self.total_chapters}章")

            return True
        except Exception as e:
            print(f"加载项目时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def check_outline_similarity(self, outline_content: str, chapter_number: int) -> tuple:
        """
        检查大纲与前几章的相似度

        Args:
            outline_content: 当前大纲内容
            chapter_number: 当前章节号

        Returns:
            (是否重复, 重复章节号, 相似度类型, 相似度值)
        """
        if chapter_number <= 1:
            return False, None, None, 0

        # 获取所有已有章节的大纲内容，确保全面检查避免重复
        previous_outlines = []
        # 检查所有已有章节（从第1章到当前章节的前一章）
        for i in range(1, chapter_number):
            prev_chapter = self.outline.get_chapter(i)
            if prev_chapter:
                previous_outlines.append((i, prev_chapter.content))

        # 从当前大纲中提取场景和伏笔信息
        current_scenes = self._extract_outline_scenes(outline_content)
        current_foreshadowings = self._extract_outline_foreshadowings(outline_content)

        # 检查重复度
        for chapter_idx, prev_outline in previous_outlines:
            # 检查场景重复
            prev_scenes = self._extract_outline_scenes(prev_outline)
            if prev_scenes and current_scenes:
                # 计算场景重复率
                common_scenes = set(current_scenes).intersection(set(prev_scenes))
                if common_scenes:
                    # 对于相邻章节，允许一个场景重复（可能是延续场景）
                    # 对于非相邻章节，不应有重复场景除非是重要地点
                    chapter_distance = abs(chapter_number - chapter_idx)
                    if chapter_distance <= 1 and len(common_scenes) > 1:
                        scene_similarity = len(common_scenes) / len(current_scenes)
                        if scene_similarity > 0.4:  # 相邻章节的场景重复率阈值
                            return True, chapter_idx, "场景重复(相邻章节)", scene_similarity
                    elif chapter_distance > 1 and len(common_scenes) > 0:
                        scene_similarity = len(common_scenes) / len(current_scenes)
                        if scene_similarity > 0.25:  # 非相邻章节的场景重复率阈值
                            return True, chapter_idx, "场景重复(非相邻章节)", scene_similarity

            # 检查伏笔重复埋设
            prev_foreshadowings = self._extract_outline_foreshadowings(prev_outline)
            if prev_foreshadowings and current_foreshadowings:
                # 检查是否有重复埋下的伏笔
                for fs_id in current_foreshadowings:
                    if "埋下" in current_foreshadowings[fs_id] and fs_id in prev_foreshadowings and "埋下" in prev_foreshadowings[fs_id]:
                        # 同一个伏笔不应该在多个章节中被埋下
                        return True, chapter_idx, f"伏笔重复埋设({fs_id})", 1.0

            # 1. 关键词匹配相似度
            # 提取关键词（名词、动词、地点等）
            import re

            # 提取关键词的正则表达式（中文）
            keyword_pattern = r'[一-龥]{2,}'  # 匹配至少两个连续的中文字符

            # 从当前大纲中提取关键词
            current_keywords = re.findall(keyword_pattern, outline_content)
            # 从前一章大纲中提取关键词
            prev_keywords = re.findall(keyword_pattern, prev_outline)

            # 计算关键词重复率
            if current_keywords and prev_keywords:
                # 转换为集合计算交集
                current_keywords_set = set(current_keywords)
                prev_keywords_set = set(prev_keywords)
                common_keywords = current_keywords_set.intersection(prev_keywords_set)

                # 关键词相似度 = 共同关键词数量 / 当前大纲关键词数量
                keyword_similarity = len(common_keywords) / len(current_keywords_set)

                # 如果关键词相似度超过阈值
                # 对于相邻章节，允许稍高的相似度（因为情节可能连续）
                # 对于相距较远的章节，使用更严格的阈值
                chapter_distance = abs(chapter_number - chapter_idx)
                if chapter_distance <= 1:
                    threshold = 0.45  # 降低相邻章节允许的关键词重复率阈值
                elif chapter_distance <= 3:
                    threshold = 0.4   # 降低相距2-3章允许的关键词重复率阈值
                else:
                    threshold = 0.3   # 降低相距较远章节允许的关键词重复率阈值

                if keyword_similarity > threshold:
                    return True, chapter_idx, f"关键词相似度(阈值{threshold:.0%})", keyword_similarity

            # 注意：不再检查段落结构相似度，因为大纲是结构化的数据，段落结构本身就很相似

            # 3. 主题和情节相似度检测
            # 提取可能的主题和情节关键词
            import re
            # 提取"主题"、"情节"、"高潮"等相关内容
            theme_pattern = r'主题[：:]\s*(.+?)[\n\r]'
            plot_pattern = r'情节[：:]\s*(.+?)[\n\r]'
            climax_pattern = r'高潮[：:]\s*(.+?)[\n\r]'

            # 从当前大纲和前一章大纲中提取这些内容
            current_themes = re.findall(theme_pattern, outline_content)
            prev_themes = re.findall(theme_pattern, prev_outline)
            current_plots = re.findall(plot_pattern, outline_content)
            prev_plots = re.findall(plot_pattern, prev_outline)
            current_climax = re.findall(climax_pattern, outline_content)
            prev_climax = re.findall(climax_pattern, prev_outline)

            # 如果找到了这些内容，检查它们的相似度
            if (current_themes and prev_themes) or (current_plots and prev_plots) or (current_climax and prev_climax):
                # 计算主题相似度
                theme_similarity = 0
                if current_themes and prev_themes:
                    # 简化内容用于比较
                    current_theme = ''.join(c for c in current_themes[0] if not c.isspace() and c not in '，。！？,.!?')
                    prev_theme = ''.join(c for c in prev_themes[0] if not c.isspace() and c not in '，。！？,.!?')

                    # 计算共同字符
                    common_chars = sum(1 for a, b in zip(current_theme, prev_theme) if a == b)
                    if min(len(current_theme), len(prev_theme)) > 0:
                        theme_similarity = common_chars / min(len(current_theme), len(prev_theme))

                # 计算情节相似度
                plot_similarity = 0
                if current_plots and prev_plots:
                    current_plot = ''.join(c for c in current_plots[0] if not c.isspace() and c not in '，。！？,.!?')
                    prev_plot = ''.join(c for c in prev_plots[0] if not c.isspace() and c not in '，。！？,.!?')

                    common_chars = sum(1 for a, b in zip(current_plot, prev_plot) if a == b)
                    if min(len(current_plot), len(prev_plot)) > 0:
                        plot_similarity = common_chars / min(len(current_plot), len(prev_plot))

                # 计算高潮相似度
                climax_similarity = 0
                if current_climax and prev_climax:
                    current_climax_text = ''.join(c for c in current_climax[0] if not c.isspace() and c not in '，。！？,.!?')
                    prev_climax_text = ''.join(c for c in prev_climax[0] if not c.isspace() and c not in '，。！？,.!?')

                    common_chars = sum(1 for a, b in zip(current_climax_text, prev_climax_text) if a == b)
                    if min(len(current_climax_text), len(prev_climax_text)) > 0:
                        climax_similarity = common_chars / min(len(current_climax_text), len(prev_climax_text))

                # 计算总体相似度（取最大值）
                theme_plot_similarity = max(theme_similarity, plot_similarity, climax_similarity)

                # 根据章节距离调整阈值
                chapter_distance = abs(chapter_number - chapter_idx)
                if chapter_distance <= 1:
                    theme_threshold = 0.5  # 相邻章节允许50%的主题/情节相似度
                elif chapter_distance <= 3:
                    theme_threshold = 0.45  # 相距2-3章允许45%的主题/情节相似度
                else:
                    theme_threshold = 0.35  # 相距较远的章节只允许35%的主题/情节相似度

                if theme_plot_similarity > theme_threshold:
                    similarity_type = "主题相似度" if theme_similarity == theme_plot_similarity else \
                                     "情节相似度" if plot_similarity == theme_plot_similarity else "高潮相似度"
                    return True, chapter_idx, f"{similarity_type}(阈值{theme_threshold:.0%})", theme_plot_similarity

            # 4. 字符级别相似度（改进版）
            # 简化内容用于比较（去除空格和标点）
            simplified_current = ''.join(c for c in outline_content if not c.isspace() and c not in '，。！？,.!?')
            simplified_prev = ''.join(c for c in prev_outline if not c.isspace() and c not in '，。！？,.!?')

            # 计算多个样本的相似度，而不仅仅是前100个字符
            # 从不同位置取样本
            sample_positions = [0, len(simplified_current) // 3, 2 * len(simplified_current) // 3]
            sample_size = 150  # 增加样本大小

            # 计算每个样本位置的相似度
            position_similarities = []
            for pos in sample_positions:
                if pos + sample_size <= len(simplified_current) and pos + sample_size <= len(simplified_prev):
                    current_sample = simplified_current[pos:pos + sample_size]
                    prev_sample = simplified_prev[pos:pos + sample_size]

                    # 计算重复字符的比例
                    common_chars = sum(1 for a, b in zip(current_sample, prev_sample) if a == b)
                    sample_similarity = common_chars / sample_size
                    position_similarities.append(sample_similarity)

            # 如果有足够的样本进行比较
            if position_similarities:
                # 计算平均相似度
                avg_similarity = sum(position_similarities) / len(position_similarities)

                # 根据章节距离调整内容相似度阈值
                chapter_distance = abs(chapter_number - chapter_idx)
                if chapter_distance <= 1:
                    content_threshold = 0.4  # 降低相邻章节允许的内容相似度阈值
                elif chapter_distance <= 3:
                    content_threshold = 0.35  # 降低相距2-3章允许的内容相似度阈值
                else:
                    content_threshold = 0.3   # 降低相距较远章节允许的内容相似度阈值

                # 如果是开头或结尾章节，使用更严格的阈值
                if chapter_number <= 3 or chapter_number >= self.total_chapters - 2:
                    content_threshold -= 0.05  # 开头和结尾章节降低5%的阈值

                if avg_similarity > content_threshold:
                    return True, chapter_idx, f"内容相似度(阈值{content_threshold:.0%})", avg_similarity

        # 如果没有发现相似度过高的情况
        return False, None, None, 0

    def _extract_outline_scenes(self, outline_content: str) -> list:
        """
        从大纲中提取场景信息

        Args:
            outline_content: 大纲内容

        Returns:
            场景列表
        """
        import re

        # 场景通常在"开端"、"发展"、"高潮"、"结尾"部分描述
        # 尝试提取地点和环境描述
        scenes = []

        # 查找地点描述（通常包含"谷"、"山"、"城"、"殿"等词）
        location_patterns = [
            r'在([^，。！？\n]+谷[^，。！？\n]*)',
            r'在([^，。！？\n]+山[^，。！？\n]*)',
            r'在([^，。！？\n]+城[^，。！？\n]*)',
            r'在([^，。！？\n]+殿[^，。！？\n]*)',
            r'在([^，。！？\n]+宫[^，。！？\n]*)',
            r'在([^，。！？\n]+洞[^，。！？\n]*)',
            r'([^，。！？\n]+谷外[^，。！？\n]*)',
            r'([^，。！？\n]+谷内[^，。！？\n]*)',
            r'([^，。！？\n]+谷中[^，。！？\n]*)',
            r'来到([^，。！？\n]+)',
            r'抵达([^，。！？\n]+)',
            r'前往([^，。！？\n]+)'
        ]

        for pattern in location_patterns:
            matches = re.findall(pattern, outline_content)
            scenes.extend(matches)

        # 特别检查"开端"部分，因为它通常描述场景
        opening_match = re.search(r'开端[：:]\s*(.+?)(?=发展[：:]|$)', outline_content, re.DOTALL)
        if opening_match:
            opening_content = opening_match.group(1)
            # 提取第一句话，通常描述场景
            first_sentence = re.match(r'^[^，。！？\n]+[，。！？]', opening_content)
            if first_sentence:
                scenes.append(first_sentence.group(0))

        # 移除重复场景和过于简短的场景描述
        unique_scenes = []
        for scene in scenes:
            scene = scene.strip()
            if len(scene) > 3 and scene not in unique_scenes:
                unique_scenes.append(scene)

        return unique_scenes

    def _extract_outline_foreshadowings(self, outline_content: str) -> dict:
        """
        从大纲中提取伏笔ID及其状态

        Args:
            outline_content: 大纲内容

        Returns:
            伏笔字典 {伏笔ID: 状态}
        """
        import re

        foreshadowings = {}

        # 查找伏笔ID及其状态（埋下/回收）
        # 常见的格式: f1、F1、伏笔1等
        patterns = [
            r'([fF]\d+)[^\n]*?([埋下|回收])',
            r'伏笔(\d+)[^\n]*?([埋下|回收])'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, outline_content)
            for match in matches:
                # 标准化伏笔ID格式为"f数字"
                fs_id = match[0].lower() if match[0].lower().startswith('f') else f"f{match[0]}"
                status = match[1]
                foreshadowings[fs_id] = status

        return foreshadowings

    def generate_new_foreshadowings(self, chapter_number: int) -> bool:
        """
        根据章节大纲生成新伏笔

        Args:
            chapter_number: 章节号

        Returns:
            生成是否成功
        """
        try:
            # 检查章节号是否合法
            if chapter_number < 1 or chapter_number > self.total_chapters:
                print(f"无效的章节号: {chapter_number}")
                return False

            # 获取章节大纲
            chapter_outline = self.outline.get_chapter(chapter_number)
            if not chapter_outline:
                print(f"未找到第{chapter_number}章大纲")
                return False

            # 生成新伏笔
            print(f"正在为第{chapter_number}章生成新伏笔...")
            foreshadowing_content = self.api.generate_chapter_foreshadowing(
                self.genre,
                self.style_guide,
                str(self.background),
                json.dumps(self.character_manager.to_dict(), ensure_ascii=False),
                chapter_outline.content,
                chapter_number,
                self.total_chapters
            )

            if not foreshadowing_content:
                print(f"为第{chapter_number}章生成新伏笔失败")
                return False

            # 解析伏笔
            try:
                # 尝试提取JSON部分
                import re
                json_match = re.search(r'```json\s*(.*?)\s*```', foreshadowing_content, re.DOTALL)
                if json_match:
                    foreshadowing_content = json_match.group(1)

                # 清理可能导致解析错误的内容
                # 去除可能存在的注释
                foreshadowing_content = re.sub(r'//.*?(\n|$)', '\n', foreshadowing_content)
                # 去除可能存在的注释（多行注释）
                foreshadowing_content = re.sub(r'/\*.*?\*/', '', foreshadowing_content, flags=re.DOTALL)

                # 处理转义字符问题
                # 检查是否有不必要的转义反斜杠（特别是在属性名前的双引号）
                if '\\' in foreshadowing_content:
                    print("检测到转义字符，尝试修复...")

                    # 修复属性名中的转义引号问题 - 更精确的方法
                    # 先处理开头的大括号后面的属性名
                    foreshadowing_content = re.sub(r'{\s*\\"', '{ "', foreshadowing_content)
                    # 处理逗号后面的属性名
                    foreshadowing_content = re.sub(r',\s*\\"', ', "', foreshadowing_content)

                    # 处理属性值中不必要的转义
                    foreshadowing_content = re.sub(r':\s*\\"', ': "', foreshadowing_content)

                    # 处理整个JSON字符串，确保所有不必要的转义都被移除
                    # 但保留必要的转义，如字符串内的引号 \"
                    foreshadowing_content = foreshadowing_content.replace('\\\"', '\"')
                    foreshadowing_content = foreshadowing_content.replace('\\n', '\n')

                    # 特别处理双重转义的情况
                    if '\\\\' in foreshadowing_content:
                        foreshadowing_content = foreshadowing_content.replace('\\\\', '\\')

                    # 处理错误的转义引号
                    if '\\"' in foreshadowing_content:
                        foreshadowing_content = foreshadowing_content.replace('\\"', '"')

                    print(f"转义字符修复后的前30个字符: {foreshadowing_content[:30]}...")

                # 修复可能的格式问题
                foreshadowing_content = foreshadowing_content.replace("'", '"')  # 将单引号替换为双引号
                foreshadowing_content = re.sub(r',\s*}', '}', foreshadowing_content)  # 移除对象末尾多余的逗号
                foreshadowing_content = re.sub(r',\s*]', ']', foreshadowing_content)  # 移除数组末尾多余的逗号

                # 修复特定的JSON格式问题
                # 修复缺少逗号的问题
                foreshadowing_content = re.sub(r'}\s*{', '},{', foreshadowing_content)
                # 修复字段名没有引号的问题
                foreshadowing_content = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', foreshadowing_content)

                # 修复常见的JSON格式错误
                # 修复缺少逗号的问题（在属性之间）
                foreshadowing_content = re.sub(r'"\s*}\s*"', '","', foreshadowing_content)
                # 修复字符串中的未转义引号
                foreshadowing_content = re.sub(r'(?<!\\)"(?=.*?[^\\]":\s*)', '\\"', foreshadowing_content)
                # 修复数字和字符串之间缺少逗号的问题
                foreshadowing_content = re.sub(r'(\d+)\s*"', r'\1,"', foreshadowing_content)
                # 修复字符串和数字之间缺少逗号的问题
                foreshadowing_content = re.sub(r'"\s*(\d+)', r'",\1', foreshadowing_content)

                # 特别处理 "Expecting property name enclosed in double quotes" 错误
                # 这通常发生在JSON的开头部分
                if foreshadowing_content.startswith('{') and not foreshadowing_content.startswith('{"'):
                    print("检测到属性名缺少双引号问题，尝试修复...")
                    # 确保第一个属性名正确格式化
                    foreshadowing_content = re.sub(r'{\s*([^"{\s][^:]*?):', r'{ "\1":', foreshadowing_content)
                    # 检查修复后的内容
                    print(f"修复后的JSON开头: {foreshadowing_content[:30]}...")

                # 特别处理反斜杠问题
                if '\\' in foreshadowing_content:
                    print("检测到反斜杠问题，尝试简单修复...")

                    # 直接替换所有反斜杠，这是最简单但通常最有效的方法
                    foreshadowing_content = foreshadowing_content.replace('\\', '')
                    print(f"替换后的JSON开头: {foreshadowing_content[:30]}...")

                    # 确保JSON格式正确
                    # 修复属性名缺少双引号的问题
                    foreshadowing_content = re.sub(r'{\s*([^"{\s][^:]*?):', r'{ "\1":', foreshadowing_content)
                    foreshadowing_content = re.sub(r',\s*([^"{\s][^:]*?):', r', "\1":', foreshadowing_content)

                    # 修复可能的格式问题
                    foreshadowing_content = foreshadowing_content.replace("'", '"')  # 将单引号替换为双引号
                    foreshadowing_content = re.sub(r',\s*}', '}', foreshadowing_content)  # 移除对象末尾多余的逗号
                    foreshadowing_content = re.sub(r',\s*]', ']', foreshadowing_content)  # 移除数组末尾多余的逗号

                # 打印修复后的JSON内容，便于调试
                print("修复后的JSON内容前50个字符:", foreshadowing_content[:50] + "...")
                print("修复后的JSON内容最后50个字符:", "..." + foreshadowing_content[-50:])

                # 尝试解析JSON
                try:
                    foreshadowing_data = json.loads(foreshadowing_content)
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {str(e)}")
                    print("尝试修复JSON格式...")

                    # 尝试修复特定位置的错误
                    error_msg = str(e)
                    if "Expecting ',' delimiter" in error_msg:
                        # 提取错误位置
                        match = re.search(r'line (\d+) column (\d+)', error_msg)
                        if match:
                            line_num = int(match.group(1))
                            col_num = int(match.group(2))

                            # 将内容分割成行
                            lines = foreshadowing_content.split('\n')
                            if 1 <= line_num <= len(lines):
                                # 在错误位置前插入逗号
                                line = lines[line_num - 1]
                                if col_num <= len(line):
                                    fixed_line = line[:col_num] + ',' + line[col_num:]
                                    lines[line_num - 1] = fixed_line
                                    foreshadowing_content = '\n'.join(lines)
                                    print(f"在第{line_num}行第{col_num}列插入了逗号")

                    # 再次尝试解析
                    try:
                        foreshadowing_data = json.loads(foreshadowing_content)
                    except json.JSONDecodeError as e:
                        print(f"修复后仍然无法解析JSON: {str(e)}")

                        # 打印出错误位置附近的内容，便于调试
                        error_msg = str(e)
                        if "char " in error_msg:
                            char_match = re.search(r'char (\d+)', error_msg)
                            if char_match:
                                error_pos = int(char_match.group(1))
                                start_pos = max(0, error_pos - 30)
                                end_pos = min(len(foreshadowing_content), error_pos + 30)
                                print(f"错误位置附近的内容: ...{foreshadowing_content[start_pos:error_pos]}|ERROR HERE|{foreshadowing_content[error_pos:end_pos]}...")

                        # 针对特定错误进行修复
                        if "Expecting ',' delimiter" in error_msg:
                            # 提取错误位置
                            match = re.search(r'line (\d+) column (\d+)', error_msg)
                            if match:
                                line_num = int(match.group(1))
                                col_num = int(match.group(2))
                                print(f"尝试修复第{line_num}行第{col_num}列的逗号缺失问题")

                                # 将内容分割成行
                                lines = foreshadowing_content.split('\n')
                                if 1 <= line_num <= len(lines):
                                    # 打印出错行及其前后行，便于调试
                                    print(f"错误行前一行: {lines[line_num-2] if line_num > 1 else '(无前一行)'}")
                                    print(f"错误行: {lines[line_num-1]}")
                                    print(f"错误行后一行: {lines[line_num] if line_num < len(lines) else '(无后一行)'}")

                                    # 在错误位置前插入逗号
                                    line = lines[line_num - 1]
                                    if col_num <= len(line):
                                        # 检查错误位置前后的字符，以便更精确地修复
                                        before_char = line[col_num-1] if col_num > 0 else ''
                                        after_char = line[col_num] if col_num < len(line) else ''
                                        print(f"错误位置前的字符: '{before_char}', 错误位置的字符: '{after_char}'")

                                        # 特别处理第10行第60列的错误（您反复遇到的问题）
                                        if line_num == 10 and col_num == 60:
                                            print("检测到特定的第10行第60列错误，尝试特殊修复")
                                            # 打印整行内容，便于分析
                                            print(f"完整的第10行内容: {line}")

                                            # 尝试更精确的修复
                                            # 检查是否是属性之间缺少逗号的情况
                                            if re.search(r'"[^"]*"\s*"[^"]*"', line):
                                                fixed_line = re.sub(r'("(?:[^"\\]|\\.)*")\s*("(?:[^"\\]|\\.)*")', r'\1,\2', line)
                                                print(f"检测到属性之间缺少逗号，修复后: {fixed_line}")
                                            else:
                                                # 常规修复
                                                fixed_line = line[:col_num] + ',' + line[col_num:]
                                                print(f"应用常规修复，在第{col_num}列插入逗号")
                                        else:
                                            # 常规修复
                                            fixed_line = line[:col_num] + ',' + line[col_num:]

                                        lines[line_num - 1] = fixed_line
                                        foreshadowing_content = '\n'.join(lines)
                                        print(f"在第{line_num}行第{col_num}列插入了逗号")
                                        print(f"修复后的行: {fixed_line}")

                                        # 再次尝试解析
                                        try:
                                            foreshadowing_data = json.loads(foreshadowing_content)
                                            print("第二次修复成功！")
                                        except json.JSONDecodeError as e2:
                                            print(f"第二次修复后仍然无法解析JSON: {str(e2)}")
                                            raise
                        else:
                            raise

                # 添加新伏笔
                new_foreshadowings_count = 0
                for fs_data in foreshadowing_data.get("foreshadowings", []):
                    # 创建新伏笔
                    self.foreshadowing_manager.add_foreshadowing_from_dict(fs_data, self.total_chapters)
                    new_foreshadowings_count += 1

                self.foreshadowing_manager.save()
                print(f"为第{chapter_number}章生成了{new_foreshadowings_count}个新伏笔")
                return True
            except Exception as e:
                print(f"解析第{chapter_number}章新伏笔时出错: {str(e)}")
                print(f"尝试重新生成第{chapter_number}章的伏笔...")

                # 最大重试次数
                max_retries = MAX_RETRIES
                for attempt in range(max_retries):
                    try:
                        # 重新生成伏笔
                        print(f"重新生成第{chapter_number}章伏笔 (尝试 {attempt+1}/{max_retries})...")
                        foreshadowing_content = self.api.generate_chapter_foreshadowing(
                            self.genre,
                            self.style_guide,
                            str(self.background),
                            json.dumps(self.character_manager.to_dict(), ensure_ascii=False),
                            chapter_outline.content,
                            chapter_number,
                            self.total_chapters
                        )

                        if not foreshadowing_content:
                            print(f"重新生成第{chapter_number}章伏笔失败")
                            if attempt < max_retries - 1:
                                continue
                            else:
                                return False

                        # 尝试提取JSON部分
                        json_match = re.search(r'```json\s*(.*?)\s*```', foreshadowing_content, re.DOTALL)
                        if json_match:
                            foreshadowing_content = json_match.group(1)

                        # 清理和修复JSON格式
                        # 去除可能存在的注释
                        foreshadowing_content = re.sub(r'//.*?(\n|$)', '\n', foreshadowing_content)
                        # 去除可能存在的注释（多行注释）
                        foreshadowing_content = re.sub(r'/\*.*?\*/', '', foreshadowing_content, flags=re.DOTALL)

                        # 处理转义字符问题
                        # 检查是否有不必要的转义反斜杠（特别是在属性名前的双引号）
                        if r'\"' in foreshadowing_content:
                            print("检测到不必要的转义字符，尝试修复...")
                            # 修复属性名中的转义引号问题
                            foreshadowing_content = re.sub(r'{\s*\\+"', '{  "', foreshadowing_content)
                            foreshadowing_content = re.sub(r',\s*\\+"', ', "', foreshadowing_content)
                            # 全局替换不必要的转义字符
                            foreshadowing_content = foreshadowing_content.replace(r'\"', '"')

                        # 修复可能的格式问题
                        foreshadowing_content = foreshadowing_content.replace("'", '"')  # 将单引号替换为双引号
                        foreshadowing_content = re.sub(r',\s*}', '}', foreshadowing_content)  # 移除对象末尾多余的逗号
                        foreshadowing_content = re.sub(r',\s*]', ']', foreshadowing_content)  # 移除数组末尾多余的逗号

                        # 修复特定的JSON格式问题
                        # 修复缺少逗号的问题
                        foreshadowing_content = re.sub(r'}\s*{', '},{', foreshadowing_content)
                        # 修复字段名没有引号的问题
                        foreshadowing_content = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', foreshadowing_content)

                        # 修复常见的JSON格式错误
                        # 修复缺少逗号的问题（在属性之间）
                        foreshadowing_content = re.sub(r'"\s*}\s*"', '","', foreshadowing_content)
                        # 修复字符串中的未转义引号
                        foreshadowing_content = re.sub(r'(?<!\\)"(?=.*?[^\\]":\s*)', '\\"', foreshadowing_content)
                        # 修复数字和字符串之间缺少逗号的问题
                        foreshadowing_content = re.sub(r'(\d+)\s*"', r'\1,"', foreshadowing_content)
                        # 修复字符串和数字之间缺少逗号的问题
                        foreshadowing_content = re.sub(r'"\s*(\d+)', r'",\1', foreshadowing_content)

                        # 特别处理 "Expecting property name enclosed in double quotes" 错误
                        # 这通常发生在JSON的开头部分
                        if foreshadowing_content.startswith('{') and not foreshadowing_content.startswith('{"'):
                            print("检测到属性名缺少双引号问题，尝试修复...")
                            # 确保第一个属性名正确格式化
                            foreshadowing_content = re.sub(r'{\s*([^"{\s][^:]*?):', r'{ "\1":', foreshadowing_content)
                            # 检查修复后的内容
                            print(f"修复后的JSON开头: {foreshadowing_content[:30]}...")

                        # 特别处理反斜杠问题
                        if '\\' in foreshadowing_content:
                            print("检测到反斜杠问题，尝试完全重构JSON...")

                            # 尝试提取JSON结构并重新构建
                            try:
                                # 首先，直接替换所有反斜杠
                                clean_content = foreshadowing_content.replace('\\', '')

                                # 尝试匹配foreshadowings数组
                                fs_match = re.search(r'["\']?foreshadowings["\']?\s*:\s*\[(.*)\]', clean_content, re.DOTALL)
                                if fs_match:
                                    fs_array = fs_match.group(1).strip()

                                    # 手动构建一个全新的、格式正确的JSON
                                    new_json = '{\n  "foreshadowings": [\n'

                                    # 提取每个伏笔对象
                                    fs_objects = []
                                    bracket_count = 0
                                    current_obj = ""

                                    for char in fs_array:
                                        if char == '{':
                                            bracket_count += 1
                                            current_obj += char
                                        elif char == '}':
                                            bracket_count -= 1
                                            current_obj += char
                                            if bracket_count == 0:
                                                fs_objects.append(current_obj)
                                                current_obj = ""
                                        elif bracket_count > 0:
                                            current_obj += char

                                    # 处理每个伏笔对象
                                    for i, obj in enumerate(fs_objects):
                                        # 提取所有键值对
                                        pairs = []
                                        for key_match in re.finditer(r'["\']?([a-zA-Z_][a-zA-Z0-9_]*)["\']?\s*:\s*([^,}]+)', obj):
                                            key = key_match.group(1)
                                            value = key_match.group(2).strip()

                                            # 处理值
                                            if value.startswith('"') and value.endswith('"'):
                                                # 已经是字符串
                                                clean_value = value
                                            elif value.startswith("'") and value.endswith("'"):
                                                # 单引号字符串，转换为双引号
                                                clean_value = '"' + value[1:-1] + '"'
                                            elif value.lower() in ['true', 'false', 'null']:
                                                # 布尔值或null
                                                clean_value = value.lower()
                                            elif re.match(r'^-?\d+(\.\d+)?$', value):
                                                # 数字
                                                clean_value = value
                                            elif value.startswith('[') and value.endswith(']'):
                                                # 数组，需要处理内部元素
                                                array_items = re.findall(r'["\']([^"\']+)["\']', value)
                                                clean_value = '["' + '", "'.join(array_items) + '"]'
                                            else:
                                                # 其他情况，当作字符串处理
                                                clean_value = f'"{value}"'

                                            pairs.append(f'    "{key}": {clean_value}')

                                        # 添加到新JSON
                                        new_json += '    {\n' + ',\n'.join(pairs) + '\n    }'
                                        if i < len(fs_objects) - 1:
                                            new_json += ',\n'

                                    new_json += '\n  ]\n}'
                                    foreshadowing_content = new_json
                                    print("JSON完全重构成功")
                                else:
                                    # 如果无法提取foreshadowings数组，尝试更简单的修复
                                    print("无法提取foreshadowings数组，尝试简单修复...")
                                    foreshadowing_content = clean_content

                                    # 确保JSON格式正确
                                    # 修复属性名缺少双引号的问题
                                    foreshadowing_content = re.sub(r'{\s*([^"{\s][^:]*?):', r'{ "\1":', foreshadowing_content)
                                    foreshadowing_content = re.sub(r',\s*([^"{\s][^:]*?):', r', "\1":', foreshadowing_content)

                                    # 修复可能的格式问题
                                    foreshadowing_content = foreshadowing_content.replace("'", '"')  # 将单引号替换为双引号
                                    foreshadowing_content = re.sub(r',\s*}', '}', foreshadowing_content)  # 移除对象末尾多余的逗号
                                    foreshadowing_content = re.sub(r',\s*]', ']', foreshadowing_content)  # 移除数组末尾多余的逗号
                            except Exception as e:
                                print(f"JSON重构失败: {str(e)}")
                                # 如果重构失败，尝试最简单的方法
                                foreshadowing_content = foreshadowing_content.replace('\\', '')
                                foreshadowing_content = foreshadowing_content.replace('"{', '{')
                                foreshadowing_content = foreshadowing_content.replace('}"', '}')
                                foreshadowing_content = foreshadowing_content.replace("'", '"')

                            print(f"处理后的JSON开头: {foreshadowing_content[:30]}...")

                        # 打印修复后的JSON内容，便于调试
                        print("重试修复后的JSON内容前50个字符:", foreshadowing_content[:50] + "...")
                        print("重试修复后的JSON内容最后50个字符:", "..." + foreshadowing_content[-50:])

                        # 尝试解析JSON
                        foreshadowing_data = json.loads(foreshadowing_content)

                        # 添加新伏笔
                        new_foreshadowings_count = 0
                        for fs_data in foreshadowing_data.get("foreshadowings", []):
                            # 创建新伏笔
                            self.foreshadowing_manager.add_foreshadowing_from_dict(fs_data, self.total_chapters)
                            new_foreshadowings_count += 1

                        self.foreshadowing_manager.save()
                        print(f"为第{chapter_number}章重新生成了{new_foreshadowings_count}个新伏笔")
                        return True

                    except Exception as retry_error:
                        print(f"重新生成第{chapter_number}章伏笔时出错: {str(retry_error)}")
                        if attempt < max_retries - 1:
                            print(f"将进行第{attempt+2}次尝试...")
                        else:
                            print(f"已达到最大重试次数，无法为第{chapter_number}章生成伏笔")
                            return False

                return False
        except Exception as e:
            print(f"生成第{chapter_number}章新伏笔时出错: {str(e)}")
            return False

    def generate_chapter_outline(self, chapter_number: int) -> bool:
        """
        生成章节大纲

        Args:
            chapter_number: 章节号

        Returns:
            生成是否成功
        """
        try:
            # 检查章节号是否合法
            if chapter_number < 1 or chapter_number > self.total_chapters:
                print(f"无效的章节号: {chapter_number}")
                return False

            # 获取前几章摘要
            previous_summary = self.outline.get_previous_chapters_summary(chapter_number)

            # 获取故事主线概述
            from utils.file_manager import load_main_storyline
            main_storyline_data = load_main_storyline(self.total_chapters)
            
            # 如果返回的是字典，转换为JSON字符串
            if isinstance(main_storyline_data, dict):
                import json
                main_storyline = json.dumps(main_storyline_data, ensure_ascii=False)
            else:
                main_storyline = main_storyline_data
                
            if not main_storyline:
                print("警告：未找到故事主线概述，将不参考主线生成大纲")
                main_storyline = ""

            # 获取应该在本章出场的人物
            from models.character import determine_character_appearance
            appearing_characters = []
            for character in self.character_manager.characters:
                if determine_character_appearance(character, chapter_number, self.total_chapters):
                    appearing_characters.append(character)

            # 格式化出场人物信息
            appearing_characters_str = ""
            if appearing_characters:
                appearing_characters_str = "应在本章出场的人物：\n"
                for character in appearing_characters:
                    appearing_characters_str += f"- {character.name} ({character.role}): {character.basic_info.get('identity', '未知身份')}\n"

            # 获取最近几章使用的场景以避免重复
            recent_scenes = []
            for i in range(chapter_number - 1, max(0, chapter_number - 4), -1):
                chapter = self.outline.get_chapter(i)
                if chapter and chapter.content:
                    # 简单提取场景信息（可以根据实际情况优化提取逻辑）
                    content = chapter.content.lower()
                    scenes = []
                    scene_indicators = ["山脉", "洞府", "大殿", "广场", "密室", "城市", "森林", "湖泊", "岛屿", "山谷", "河流", "集市"]
                    for indicator in scene_indicators:
                        if indicator in content:
                            scenes.append(indicator)
                    if scenes:
                        recent_scenes.extend(scenes)

            # 获取伏笔管理器中已埋下的伏笔
            already_planted_foreshadowings = []

            # 新增：获取应该在本章埋下和回收的伏笔
            foreshadowings_to_plant = self.foreshadowing_manager.get_foreshadowings_to_plant(chapter_number)
            foreshadowings_to_reveal = self.foreshadowing_manager.get_foreshadowings_to_reveal(chapter_number)

            # 格式化应该埋下的伏笔列表
            to_plant_str = ""
            if foreshadowings_to_plant:
                to_plant_str = "本章需要埋下的伏笔：\n"
                for fs in foreshadowings_to_plant:
                    to_plant_str += f"- ID: {fs.id}, 内容: {fs.content}, 类型: {fs.type}, 重要性: {fs.importance}\n"

            # 格式化应该回收的伏笔列表
            to_reveal_str = ""
            if foreshadowings_to_reveal:
                to_reveal_str = "本章需要回收的伏笔：\n"
                for fs in foreshadowings_to_reveal:
                    to_reveal_str += f"- ID: {fs.id}, 内容: {fs.content}, 类型: {fs.type}, 重要性: {fs.importance}\n"

            # 获取伏笔管理器中已埋下的伏笔
            for foreshadowing in self.foreshadowing_manager.foreshadowings:
                if foreshadowing.status in ["已埋下未回收", "已回收"] and foreshadowing.id not in already_planted_foreshadowings:
                    already_planted_foreshadowings.append(foreshadowing.id)

            # 构建场景和伏笔提示
            recent_scenes_str = ""
            if recent_scenes:
                recent_scenes_str = "最近章节出现的场景（避免重复使用）：\n- " + "\n- ".join(recent_scenes[:10])  # 限制数量

            already_planted_foreshadowings_str = ""
            if already_planted_foreshadowings:
                already_planted_foreshadowings_str = "已埋下的伏笔（避免重复埋下）：\n- " + "\n- ".join(already_planted_foreshadowings)

            # 生成标题和大纲
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    print(f"生成第 {chapter_number} 章大纲（尝试 {attempt + 1}/{max_retries}）...")
                    outline_content = self.api.generate_outline(
                        self.genre,
                        self.style_guide,
                        str(self.background),
                        chapter_number,
                        self.total_chapters,
                        previous_summary,
                        str(self.character_manager),
                        str(self.foreshadowing_manager),
                        appearing_characters=appearing_characters_str,  # 添加出场人物参数
                        main_storyline=main_storyline,  # 添加故事主线概述参数
                        recent_scenes=recent_scenes_str,  # 添加最近场景参数
                        already_planted_foreshadowings=already_planted_foreshadowings_str,  # 添加已埋下伏笔参数
                        foreshadowings_to_plant=to_plant_str,  # 添加需要埋下的伏笔参数
                        foreshadowings_to_reveal=to_reveal_str  # 添加需要回收的伏笔参数
                    )

                    if not outline_content:
                        print("生成大纲失败，尝试重试...")
                        continue

                    # 尝试解析结构化的JSON大纲数据
                    try:
                        # 如果返回的是字符串格式的JSON
                        if isinstance(outline_content, str) and (outline_content.strip().startswith('{') and outline_content.strip().endswith('}')):
                            import json
                            # 打印原始JSON内容（截断以避免日志过长）
                            if len(outline_content) > 500:
                                print(f"原始JSON（前500字符）: {outline_content[:500]}...")
                                print(f"原始JSON（后500字符）: {outline_content[-500:]}...")
                            else:
                                print(f"原始JSON: {outline_content}")

                            # 使用我们改进的fix_json_comprehensive方法修复JSON
                            try:
                                # 尝试直接解析
                                outline_data = json.loads(outline_content)
                                print("JSON格式检查通过")
                            except json.JSONDecodeError:
                                print("解析JSON失败，使用fix_json_comprehensive修复...")
                                # 使用改进后的修复方法
                                fixed_content = self.fix_json_comprehensive(outline_content)
                                
                                try:
                                    outline_data = json.loads(fixed_content)
                                    print("使用fix_json_comprehensive修复成功")
                                    # 使用修复后的内容替换原始内容
                                    outline_content = fixed_content
                                except json.JSONDecodeError as e:
                                    print(f"使用fix_json_comprehensive修复后仍解析失败: {e}")
                                    print("尝试使用非JSON方式处理内容")
                                    raise e  # 抛出错误，会继续处理
                            
                            # 提取标题
                            title = outline_data.get("title", f"第{chapter_number}章")
                            # 标题可能包含"第X章"前缀，需要提取实际标题部分
                            import re
                            title_match = re.search(r'第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*(.+)', title)
                            if title_match:
                                title = title_match.group(1).strip()

                            # 创建ChapterOutline对象并使用结构化数据
                            chapter_outline = ChapterOutline(
                                chapter_number=chapter_number,
                                title=title,
                                content=outline_content,  # 保存原始JSON作为content
                                chapter_summary=outline_data.get("chapter_summary", {}),
                                characters=outline_data.get("characters", []),
                                key_points=outline_data.get("key_points", []),
                                foreshadowings=outline_data.get("foreshadowings", {})
                            )
                        else:
                            # 如果不是JSON格式，使用传统处理方式
                            # 提取标题
                            import re
                            title_match = re.search(r'第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*(.+?)[\n\r]', outline_content)
                            title = title_match.group(1).strip() if title_match else f"未命名章节{chapter_number}"

                            # 创建ChapterOutline对象
                            chapter_outline = ChapterOutline(
                                chapter_number=chapter_number,
                                title=title,
                                content=outline_content
                            )

                    except Exception as e:
                        print(f"解析大纲数据出错: {e}，使用传统方式处理")
                        # 如果解析失败，回退到传统处理方式
                        # 提取标题
                        import re
                        title_match = re.search(r'第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*(.+?)[\n\r]', outline_content)
                        title = title_match.group(1).strip() if title_match else f"未命名章节{chapter_number}"

                        # 创建ChapterOutline对象
                        chapter_outline = ChapterOutline(
                            chapter_number=chapter_number,
                            title=title,
                            content=outline_content
                        )

                    # 验证大纲质量
                    if not self._validate_outline_quality(chapter_outline):
                        print("大纲质量检查未通过，尝试重新生成...")
                        if attempt < max_retries - 1:
                            outline_content = self.api.regenerate_chapter_outline(
                                self.genre,
                                self.style_guide,
                                str(self.background),
                                chapter_number,
                                self.total_chapters,
                                previous_summary,
                                str(self.character_manager),
                                str(self.foreshadowing_manager),
                                appearing_characters=appearing_characters_str,
                                main_storyline=main_storyline,
                                original_outline=outline_content,  # 传入上一次生成的内容
                                recent_scenes=recent_scenes_str,  # 添加最近场景参数
                                already_planted_foreshadowings=already_planted_foreshadowings_str,  # 添加已埋下伏笔参数
                                foreshadowings_to_plant=to_plant_str,  # 添加需要埋下的伏笔参数
                                foreshadowings_to_reveal=to_reveal_str  # 添加需要回收的伏笔参数
                            )
                            continue
                        else:
                            print("达到最大重试次数，使用当前最佳结果...")

                    # 添加大纲到outline
                    self.outline.add_chapter(chapter_outline)
                    self.outline.save()

                    print(f"第{chapter_number}章大纲生成成功：{title}")

                    # 添加背景设定检查
                    print("正在检查大纲是否与背景设定冲突...")
                    outline_content_str = chapter_outline.content
                    background_check_result = self.api.check_chapter_background(outline_content_str, str(self.background))
                    
                    if not background_check_result.get("passed", False):
                        issues = background_check_result.get("issues", [])
                        print(f"大纲背景设定检查未通过，发现{len(issues)}个问题:")
                        for issue in issues:
                            print(f"- {issue.get('description', '')}")
                            print(f"  建议: {issue.get('suggestion', '')}")
                        
                        # 如果是最后一次尝试且检查失败，返回失败
                        if attempt >= max_retries - 1:
                            print(f"已尝试{max_retries}次生成大纲，但背景设定检查仍未通过，放弃生成")
                            # 移除刚保存的大纲
                            self.outline.remove_chapter(chapter_number)
                            return False
                        
                        print("大纲背景设定检查未通过，将重试生成...")
                        continue  # 重试生成大纲
                    
                    # 检查是否有任何其他问题，即使轻微问题也不通过
                    if background_check_result.get("issues") and len(background_check_result.get("issues", [])) > 0:
                        issues = background_check_result.get("issues", [])
                        print(f"大纲存在{len(issues)}个问题，即使不严重也需要修复:")
                        for issue in issues:
                            print(f"- {issue.get('description', '')}")
                            
                        # 如果是最后一次尝试且检查失败，返回失败
                        if attempt >= max_retries - 1:
                            print(f"已尝试{max_retries}次生成大纲，但仍有问题，放弃生成")
                            self.outline.remove_chapter(chapter_number)
                            return False
                            
                        print("大纲检查发现问题，将重试生成...")
                        continue  # 重试生成大纲
                    
                    # 检查通过，更新伏笔状态
                    print("大纲背景设定检查通过")
                    self.update_foreshadowing_status(chapter_number, chapter_outline)
                    self.outline.save()
                    return True
                except Exception as e:
                    print(f"生成大纲时发生错误: {str(e)}")
                    if attempt == max_retries - 1:
                        print("达到最大重试次数，生成大纲失败")
                        return False
            return False
        except Exception as e:
            print(f"生成章节大纲时发生错误：{str(e)}")
            return False

    def update_foreshadowing_status(self, chapter_number: int, chapter_outline) -> None:
        """
        根据章节大纲更新伏笔状态

        Args:
            chapter_number: 章节号
            chapter_outline: 章节大纲对象
        """
        # 如果有伏笔管理器，更新伏笔状态
        if self.foreshadowing_manager:
            self.foreshadowing_manager.update_chapter_foreshadowings(chapter_number)

            # 如果大纲有结构化的伏笔信息，验证其正确性
            if hasattr(chapter_outline, 'foreshadowings') and chapter_outline.foreshadowings:
                planted_ids = []
                revealed_ids = []

                # 从大纲中提取伏笔ID
                if "planted" in chapter_outline.foreshadowings:
                    for fs in chapter_outline.foreshadowings["planted"]:
                        if isinstance(fs, dict) and "id" in fs:
                            planted_ids.append(fs["id"])
                        elif isinstance(fs, str) and fs.startswith('f'):
                            planted_ids.append(fs)

                if "revealed" in chapter_outline.foreshadowings:
                    for fs in chapter_outline.foreshadowings["revealed"]:
                        if isinstance(fs, dict) and "id" in fs:
                            revealed_ids.append(fs["id"])
                        elif isinstance(fs, str) and fs.startswith('f'):
                            revealed_ids.append(fs)

                # 验证伏笔是否正确处理
                for fs in self.foreshadowing_manager.get_foreshadowings_to_plant(chapter_number):
                    if fs.id not in planted_ids:
                        print(f"警告: 伏笔 {fs.id} 应在本章埋下但未在大纲中找到")

                for fs in self.foreshadowing_manager.get_foreshadowings_to_reveal(chapter_number):
                    if fs.id not in revealed_ids:
                        print(f"警告: 伏笔 {fs.id} 应在本章回收但未在大纲中找到")

# ... existing code ...
# 由于文件过大，这里仅展示关键修复区域，实际应用时请用此修正版覆盖原文件

    def generate_chapter_content(self, chapter_number: int, max_retries: int = MAX_RETRIES) -> bool:
        """
        生成章节内容，支持多次重试和自动修复
        """
        try:
            chapter_outline = self.outline.get_chapter(chapter_number)
            if not chapter_outline:
                print(f"未找到第{chapter_number}章大纲")
                return False
                
            total_retry_count = 0
            MAX_TOTAL_RETRIES = max_retries
            while total_retry_count < MAX_TOTAL_RETRIES:
                # ... existing code ...
                check_success, check_result = self.check_chapter(chapter_number)
                if check_success and check_result.get('passed', True):
                    print(f"第{chapter_number}章验证通过")
                    chapter_outline.is_generated = True
                    chapter_outline.is_completed = True
                    self.outline.save()
                    return True
                else:
                    issues = check_result.get('issues', [])
                    word_count_issue = False
                    for issue in issues:
                        issue_type = issue.get('type', '')
                        if '字数不足' in issue_type:
                            word_count_issue = True
                            print(f"字数不足: {issue.get('description', '')}")
                            break
                    if word_count_issue:
                        print(f"由于字数不足，将重新生成第{chapter_number}章...")
                        generation_instruction = "请确保生成至少5000字的内容，详细展开情节，使字数充足。"
                        total_retry_count += 1
                        if total_retry_count >= MAX_TOTAL_RETRIES:
                            print(f"达到最大重试次数 {MAX_TOTAL_RETRIES}，尝试通过修复问题来增加字数...")
                            fix_success = self.fix_chapter_content(chapter_number, issues, "字数问题")
                            if fix_success:
                                print(f"成功修复第{chapter_number}章的字数问题")
                                recheck_success, recheck_result = self.check_chapter(chapter_number)
                                if recheck_success and recheck_result.get('passed', True):
                                    print(f"第{chapter_number}章修复后验证通过")
                                    chapter_outline.is_generated = True
                                    chapter_outline.is_completed = True
                                    self.outline.save()
                                    return True
                                else:
                                    print(f"第{chapter_number}章修复后仍未通过验证，但将继续使用")
                                    chapter_outline.is_generated = True
                                    chapter_outline.is_completed = True
                                    self.outline.save()
                                    return True
                            else:
                                print(f"无法修复第{chapter_number}章的字数问题，但将使用当前内容")
                                chapter_outline.is_generated = True
                                chapter_outline.is_completed = True
                                self.outline.save()
                                return True
                        continue
                    else:
                        if issues:
                            print(f"尝试修复第{chapter_number}章中的问题...")
                            issue_type = "大纲冲突"
                            for issue in issues:
                                type_str = issue.get('type', '')
                                if '背景' in type_str:
                                    issue_type = "背景冲突"
                                    break
                                elif '人物' in type_str:
                                    issue_type = "人物冲突"
                                    break
                            fix_success = self.fix_chapter_content(chapter_number, issues, issue_type)
                            if fix_success:
                                print(f"成功修复第{chapter_number}章的问题")
                                recheck_success, recheck_result = self.check_chapter(chapter_number)
                                if recheck_success and recheck_result.get('passed', True):
                                    print(f"第{chapter_number}章修复后验证通过")
                                    chapter_outline.is_generated = True
                                    chapter_outline.is_completed = True
                                    self.outline.save()
                                    return True
                                else:
                                    has_word_count_issue = False
                                    for issue in recheck_result.get('issues', []):
                                        if '字数不足' in issue.get('type', ''):
                                            has_word_count_issue = True
                                            break
                                    if has_word_count_issue and total_retry_count < MAX_TOTAL_RETRIES:
                                        print(f"修复后仍有字数问题，继续重试...")
                                        total_retry_count += 1
                                        continue
                                    print(f"第{chapter_number}章修复后仍有一些问题，但将继续使用")
                                    chapter_outline.is_generated = True
                                    chapter_outline.is_completed = True
                                    self.outline.save()
                                    return True
                            else:
                                print(f"无法修复第{chapter_number}章的问题，尝试重新生成")
                                total_retry_count += 1
                                if total_retry_count >= MAX_TOTAL_RETRIES:
                                    print(f"达到最大重试次数 {MAX_TOTAL_RETRIES}，使用当前生成结果...")
                                    chapter_outline.is_generated = True
                                    chapter_outline.is_completed = True
                                    self.outline.save()
                                    return True
                                continue
            print(f"经过 {MAX_TOTAL_RETRIES} 次重试后，仍未能生成符合要求的章节内容")
            return False
        except Exception as e:
            print(f"生成章节内容时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def _generate_chapter_content_traditional(self, chapter_number: int) -> bool:
        """
        使用传统方式生成章节内容（不分段）
        """
        try:
            chapter_outline = self.outline.get_chapter(chapter_number)
            if not chapter_outline:
                print(f"未找到第{chapter_number}章大纲")
                return False
            previous_summary = self.outline.get_previous_chapters_summary(chapter_number)
            MAX_TRADITIONAL_RETRIES = 5
            retries = 0
            while retries < MAX_TRADITIONAL_RETRIES:
                print(f"使用传统方式生成第 {chapter_number} 章内容...（尝试 {retries + 1}/{MAX_TRADITIONAL_RETRIES}）")
                generation_instruction = ""
                if retries > 0:
                    generation_instruction = "请确保生成至少5000字的内容，详细展开情节，使字数充足。"
                enhanced_summary = previous_summary
                if generation_instruction:
                    enhanced_summary = f"{previous_summary}\n\n【生成指示】：{generation_instruction}"
                chapter_content = self.api.generate_chapter(
                    chapter_number,
                    chapter_outline.content,
                    str(self.background),
                    json.dumps(self.character_manager.to_dict(), ensure_ascii=False),
                    json.dumps(self.foreshadowing_manager.to_dict(), ensure_ascii=False),
                    enhanced_summary
                )
                if not chapter_content:
                    print(f"生成第{chapter_number}章内容失败，尝试重试 ({retries + 1}/{MAX_TRADITIONAL_RETRIES})")
                    retries += 1
                    continue
                import re
                full_title_match = re.search(r'^(第.+?章\s*.+?)[\n\r]', chapter_content)
                if full_title_match and len(full_title_match.group(1).strip().split(' ', 1)) > 1:
                    title = full_title_match.group(1).strip()
                else:
                    chapter_only_match = re.search(r'^(第.+?章)[\n\r]', chapter_content)
                    outline_title = chapter_outline.title if chapter_outline else ""
                    if outline_title and outline_title.strip() == f"第{chapter_number}章" and chapter_outline and chapter_outline.content:
                        outline_content_title_match = re.search(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*(.+?)[\n\r]', chapter_outline.content)
                        if outline_content_title_match and outline_content_title_match.group(1).strip():
                            outline_title = f"第{chapter_number}章 {outline_content_title_match.group(1).strip()}"
                    if outline_title and outline_title.strip() and not outline_title.strip() == f"第{chapter_number}章":
                        if chapter_only_match:
                            chapter_prefix = chapter_only_match.group(1).strip()
                            title = f"{chapter_prefix} {outline_title.split(' ', 1)[1] if ' ' in outline_title else outline_title}"
                        else:
                            title = outline_title
                    else:
                        title = f"第{chapter_number}章 未命名章节{chapter_number}"
                    if chapter_only_match:
                        chapter_content = re.sub(r'^第.+?章[\n\r]', f"{title}\n\n", chapter_content, 1)
                success = save_chapter(chapter_number, title, chapter_content)
                if not success:
                    print(f"保存第{chapter_number}章内容失败")
                    retries += 1
                    continue
                check_success, check_result = self.check_chapter(chapter_number)
                if check_success and check_result.get('passed', True):
                    print(f"第{chapter_number}章验证通过")
                    from novel.checker import NovelChecker
                    checker = NovelChecker()
                    if checker.load_data():
                        checker.total_chapters = self.total_chapters
                        checker.apply_chapter_updates(chapter_number, check_result)
                        print(f"第{chapter_number}章的人物状态和战力已更新")
                    else:
                        print(f"警告：无法加载检查器数据，人物状态和战力可能未更新")
                    chapter_outline.is_generated = True
                    chapter_outline.is_completed = True
                    self.outline.save()
                    print(f"第{chapter_number}章已生成并保存")
                    return True
                else:
                    print(f"第{chapter_number}章验证失败: {check_result.get('issues', [])}")
                    issues = check_result.get('issues', [])
                    word_count_issue = False
                    for issue in issues:
                        issue_type = issue.get('type', '')
                        if '字数不足' in issue_type:
                            word_count_issue = True
                            print(f"字数不足: {issue.get('description', '')}")
                            break
                    if word_count_issue:
                        print(f"由于字数不足，将重新生成第{chapter_number}章...")
                        generation_instruction = "请务必生成至少5000字的内容，这一点非常重要。详细展开每个场景，丰富对话和描写，确保内容充实且字数充足。"
                        enhanced_summary = f"{previous_summary}\n\n【生成指示】：{generation_instruction}"
                        retries += 1
                        if retries >= MAX_TRADITIONAL_RETRIES:
                            print(f"达到最大重试次数 {MAX_TRADITIONAL_RETRIES}，尝试通过修复问题来增加字数...")
                            fix_success = self.fix_chapter_content(chapter_number, issues, "字数问题")
                            if fix_success:
                                print(f"成功修复第{chapter_number}章的字数问题")
                                recheck_success, recheck_result = self.check_chapter(chapter_number)
                                if recheck_success and recheck_result.get('passed', True):
                                    print(f"第{chapter_number}章修复后验证通过")
                                    chapter_outline.is_generated = True
                                    chapter_outline.is_completed = True
                                    self.outline.save()
                                    return True
                                else:
                                    print(f"第{chapter_number}章修复后仍未通过验证，但将继续使用")
                                    chapter_outline.is_generated = True
                                    chapter_outline.is_completed = True
                                    self.outline.save()
                                    return True
                            else:
                                print(f"无法修复第{chapter_number}章的字数问题，但将使用当前内容")
                                chapter_outline.is_generated = True
                                chapter_outline.is_completed = True
                                self.outline.save()
                                return True
                        continue
                    else:
                        if issues:
                            print(f"尝试修复第{chapter_number}章中的问题...")
                            issue_type = "大纲冲突"
                            for issue in issues:
                                type_str = issue.get('type', '')
                                if '背景' in type_str:
                                    issue_type = "背景冲突"
                                    break
                                elif '人物' in type_str:
                                    issue_type = "人物冲突"
                                    break
                            fix_success = self.fix_chapter_content(chapter_number, issues, issue_type)
                            if fix_success:
                                print(f"成功修复第{chapter_number}章的问题")
                                recheck_success, recheck_result = self.check_chapter(chapter_number)
                                if recheck_success and recheck_result.get('passed', True):
                                    print(f"第{chapter_number}章修复后验证通过")
                                    chapter_outline.is_generated = True
                                    chapter_outline.is_completed = True
                                    self.outline.save()
                                    return True
                                else:
                                    has_word_count_issue = False
                                    for issue in recheck_result.get('issues', []):
                                        if '字数不足' in issue.get('type', ''):
                                            has_word_count_issue = True
                                            break
                                    if has_word_count_issue and retries < MAX_TRADITIONAL_RETRIES:
                                        print(f"修复后仍有字数问题，继续重试...")
                                        retries += 1
                                        continue
                                    print(f"第{chapter_number}章修复后仍有一些问题，但将继续使用")
                                    chapter_outline.is_generated = True
                                    chapter_outline.is_completed = True
                                    self.outline.save()
                                    return True
                            else:
                                print(f"无法修复第{chapter_number}章的问题，尝试重新生成")
                                retries += 1
                                if retries >= MAX_TRADITIONAL_RETRIES:
                                    print(f"达到最大重试次数 {MAX_TRADITIONAL_RETRIES}，使用当前生成结果...")
                                    chapter_outline.is_generated = True
                                    chapter_outline.is_completed = True
                                    self.outline.save()
                                    return True
                                continue
            print(f"经过 {MAX_TRADITIONAL_RETRIES} 次重试后，仍未能生成符合要求的章节内容")
            return False
        except Exception as e:
            print(f"使用传统方式生成章节内容时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def expand_chapter_content(self, chapter_number: int, issues: List[Dict[str, Any]]) -> bool:
        """
        扩充章节内容

        Args:
            chapter_number: 章节号
            issues: 问题列表

        Returns:
            是否成功
        """
        try:
            import re  # 确保在使用re模块之前导入它

            # 获取章节内容
            chapter = read_chapter(chapter_number)
            if not chapter:
                print(f"未找到第{chapter_number}章内容")
                return False

            # 获取章节大纲
            chapter_outline = self.outline.get_chapter(chapter_number)
            if not chapter_outline:
                print(f"未找到第{chapter_number}章大纲")
                return False

            # 提取扩充建议
            expansion_suggestions = []
            for issue in issues:
                if issue.get("type") == "字数不足" and "suggestion" in issue:
                    suggestion = issue.get("suggestion", "")
                    # 如果建议中包含多个建议点（通常以数字编号），将其拆分
                    if "1." in suggestion and "2." in suggestion:
                        suggestions = []
                        lines = suggestion.split("\n")
                        for line in lines:
                            line = line.strip()
                            if line and (line.startswith("1.") or line.startswith("2.") or
                                        line.startswith("3.") or line.startswith("4.") or
                                        line.startswith("5.")):
                                suggestions.append(line)
                        if suggestions:
                            expansion_suggestions.extend(suggestions)
                    else:
                        expansion_suggestions.append(suggestion)

            # 如果没有提取到具体建议，添加一些默认建议
            if not expansion_suggestions:
                expansion_suggestions = [
                    "增加战斗/冲突场景的细节描写",
                    "深化主要角色的心理活动和内心独白",
                    "增加对环境和场景的描写，提升代入感",
                    "补充世界观和背景设定的相关内容",
                    "增加角色之间的互动和对话"
                ]

            # 扩充章节内容
            print(f"正在扩充第{chapter_number}章内容...")
            print(f"扩充建议: {expansion_suggestions}")
            expanded_content = self.api.expand_chapter_content(
                chapter["content"],
                expansion_suggestions,
                chapter_outline.content,
                str(self.background),
                json.dumps(self.character_manager.to_dict(), ensure_ascii=False),
                json.dumps(self.foreshadowing_manager.to_dict(), ensure_ascii=False)
            )

            if not expanded_content:
                print("扩充章节内容失败")
                return False

            # 移除内容开头的元数据和说明
            # 移除类似 "（以下是严格按您要求扩充至10000字的完整章节内容，所有新增内容均用下划线标出）" 的说明
            expanded_content = re.sub(r'^（[^）]*）[\n\r]+', '', expanded_content)
            expanded_content = re.sub(r'^【[^】]*】[\n\r]+', '', expanded_content)
            expanded_content = re.sub(r'^---[\n\r]+', '', expanded_content)

            # 移除可能存在的元数据标记
            # 移除类似 "**高潮（星陨异变）**" 的标记
            expanded_content = re.sub(r'\*\*[^*]+\*\*', '', expanded_content)
            # 移除类似 "（高潮部分）" 的标记
            expanded_content = re.sub(r'（[^）]*部分[^）]*）', '', expanded_content)
            # 移除类似 "（完整扩充版）" 的标记
            expanded_content = re.sub(r'（[^）]*扩充[^）]*）', '', expanded_content)
            # 移除下划线标记
            expanded_content = re.sub(r'_(.+?)_', r'\1', expanded_content)

            # 移除分隔线和新增内容标记
            # 移除类似 "-------------------------------------------" 的分隔线
            expanded_content = re.sub(r'-{3,}[\n\r]+', '', expanded_content)
            # 移除类似 "【新增细节描写】" 的标记
            expanded_content = re.sub(r'【[^】]*新增[^】]*】[\n\r]+', '', expanded_content)
            expanded_content = re.sub(r'【[^】]*扩充[^】]*】[\n\r]+', '', expanded_content)
            expanded_content = re.sub(r'【[^】]*补充[^】]*】[\n\r]+', '', expanded_content)
            # 移除类似 "（新增内容开始）" 和 "（新增内容结束）" 的标记
            expanded_content = re.sub(r'（[^）]*新增[^）]*）[\n\r]+', '', expanded_content)
            expanded_content = re.sub(r'（[^）]*扩充[^）]*）[\n\r]+', '', expanded_content)
            expanded_content = re.sub(r'（[^）]*补充[^）]*）[\n\r]+', '', expanded_content)

            # 提取章节标题
            # 首先检查是否有Markdown格式的标题
            markdown_title_match = re.search(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*(.+?)[\n\r]', expanded_content)
            if markdown_title_match and markdown_title_match.group(1).strip():
                # 提取Markdown标题中的实际标题部分
                title_text = markdown_title_match.group(1).strip()
                title = f"第{chapter_number}章 {title_text}"
                # 移除Markdown标题行
                expanded_content = re.sub(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]', '', expanded_content, 1)
            else:
                # 尝试从内容中提取完整标题（第X章 标题）
                title_match = re.search(r'^(第.+?章\s*.+?)[\n\r]', expanded_content)
                if title_match and len(title_match.group(1).strip().split(' ', 1)) > 1:
                    title = title_match.group(1).strip()
                else:
                    # 尝试从内容中提取章节号
                    chapter_only_match = re.search(r'^(第.+?章)[\n\r]', expanded_content)

                    # 尝试从大纲中获取标题
                    chapter_outline = self.outline.get_chapter(chapter_number)
                    outline_title = chapter_outline.title if chapter_outline else ""

                    # 如果大纲标题只是简单的"第X章"格式，尝试从大纲内容中提取
                    if outline_title and outline_title.strip() == f"第{chapter_number}章" and chapter_outline and chapter_outline.content:
                        # 尝试从大纲内容中提取Markdown格式的标题
                        import re
                        outline_content_title_match = re.search(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*(.+?)[\n\r]', chapter_outline.content)
                        if outline_content_title_match and outline_content_title_match.group(1).strip():
                            # 提取Markdown标题中的实际标题部分
                            outline_title = f"第{chapter_number}章 {outline_content_title_match.group(1).strip()}"

                    if outline_title and outline_title.strip() and not outline_title.strip() == f"第{chapter_number}章":
                        # 使用大纲标题
                        if chapter_only_match:
                            # 如果内容中有"第X章"，替换为完整标题
                            chapter_prefix = chapter_only_match.group(1).strip()
                            title = f"{chapter_prefix} {outline_title.split(' ', 1)[1] if ' ' in outline_title else outline_title}"
                        else:
                            title = outline_title
                    else:
                        # 使用默认标题
                        title = f"第{chapter_number}章 未命名章节{chapter_number}"

                    # 如果内容中只有"第X章"，替换为完整标题
                    if chapter_only_match:
                        expanded_content = re.sub(r'^第.+?章[\n\r]', f"{title}\n\n", expanded_content, 1)
                    else:
                        # 如果内容中没有章节标题，添加完整标题
                        expanded_content = f"{title}\n\n{expanded_content}"

            # 检查内容是否完整（最后一个句子是否以标点符号结尾）
            if expanded_content.strip():
                last_char = expanded_content.strip()[-1]
                if last_char not in '。！？.!?"\'》）)':
                    print(f"警告：扩充后的第{chapter_number}章内容可能不完整，最后一个字符是：{last_char}")
                    # 添加一个结束符号
                    expanded_content = expanded_content.strip() + "。"

            save_chapter(chapter_number, title, expanded_content)

            # 伏笔状态将在章节检查后统一更新

            print(f"第{chapter_number}章内容已扩充并保存")

            return True
        except Exception as e:
            print(f"修复章节内容时出错: {str(e)}")
            traceback.print_exc()
            return False

    def regenerate_main_storyline(self) -> bool:
        """
        重新生成故事主线，完全重新生成而不是基于原有内容进行修改

        Returns:
            重新生成是否成功
        """
        try:
            # 获取原有的故事主线
            from utils.file_manager import load_main_storyline
            original_storyline_data = load_main_storyline(self.total_chapters)
            
            # 如果返回的是字典，转换为JSON字符串
            if isinstance(original_storyline_data, dict):
                import json
                original_storyline = json.dumps(original_storyline_data, ensure_ascii=False)
            else:
                original_storyline = original_storyline_data

            # 检查是否需要强制完全重新生成
            # 如果章节数量不符合要求，则强制完全重新生成
            force_regenerate = self.force_regenerate  # 使用类属性中设置的值
            if original_storyline and not force_regenerate:  # 如果不是强制重新生成，检查章节数
                try:
                    storyline_data = json.loads(original_storyline)
                    current_chapters = len(storyline_data.get("outlines", []))
                    if current_chapters != self.total_chapters:
                        print(f"当前故事主线章节数({current_chapters})与要求章节数({self.total_chapters})不符，将进行完全重新生成")
                        force_regenerate = True
                    else:
                        print("找到原有故事主线且章节数正确，将基于原有内容进行修改")
                        force_regenerate = False
                except Exception as e:
                    print(f"解析原有故事主线失败: {str(e)}，将进行完全重新生成")
                    force_regenerate = True
            else:
                if force_regenerate:
                    print("设置了强制重新生成参数，将完全重新生成故事主线")
                else:
                    print("未找到原有故事主线，将进行完全重新生成")

            # 最大重试次数
            max_retries = MAX_RETRIES
            success = False
            main_storyline = None

            # 重试直到生成满足质量要求的故事主线
            for attempt in range(max_retries):
                # 重新生成故事主线
                print(f"正在生成故事主线 (尝试 {attempt+1}/{max_retries})...")
                main_storyline = self.api.generate_main_storyline(
                    self.genre,
                    self.style_guide,
                    str(self.background),
                    self.total_chapters,
                    None if force_regenerate else original_storyline,  # 如果强制重新生成，不提供原始数据
                    force_regenerate  # 传递强制重新生成标志
                )

                if not main_storyline:
                    print(f"生成故事主线失败 (尝试 {attempt+1}/{max_retries})")
                    if attempt < max_retries - 1:
                        continue
                    else:
                        return False

                # 检查故事主线质量
                try:
                    storyline_data = json.loads(main_storyline)
                    from utils.fix_storyline import validate_storyline_quality

                    print("正在检查故事主线质量...")
                    quality_check = validate_storyline_quality(storyline_data)

                    # 检查章节数量是否符合要求
                    current_chapters = len(storyline_data.get("outlines", []))
                    if current_chapters != self.total_chapters:
                        print(f"生成的章节数量({current_chapters})与要求章节数({self.total_chapters})不符，将重试")
                        if attempt < max_retries - 1:
                            continue
                        else:
                            print(f"已达到最大重试次数({max_retries})，但章节数量仍不符合要求")
                            return False

                    if not quality_check["valid"]:
                        print(f"故事主线质量检查未通过，问题: {quality_check['issues']}")
                        if attempt < max_retries - 1:
                            print(f"正在重试 ({attempt+1}/{max_retries})...")
                            continue
                        else:
                            print(f"已达到最大重试次数({max_retries})，使用最后一次生成的故事主线")
                    else:
                        print(f"故事主线质量检查通过，总字数: {quality_check['total_length']}")
                        if quality_check.get('character_names'):
                            print(f"检测到的角色名称: {', '.join(quality_check['character_names'])}")
                        success = True
                        break
                except Exception as e:
                    print(f"故事主线质量检查失败: {str(e)}")
                    # 检查失败不阻止流程，继续使用当前生成的故事主线
                    success = True
                    break

            # 如果所有尝试都失败，但至少生成了一个故事主线，仍然使用最后生成的版本
            if not success and not main_storyline:
                print("所有尝试都失败，无法生成故事主线")
                return False

            # 确保已加载大纲
            if not self.outline:
                print("加载大纲对象...")
                self.load_project()
                if not self.outline:
                    print("无法加载大纲对象，创建新大纲...")
                    from models.outline import ChapterOutlineManager
                    self.outline = ChapterOutlineManager(self.total_chapters)

            # 使用file_manager保存到JSON文件
            from utils.file_manager import save_main_storyline
            save_main_storyline(main_storyline)

            # 保存大纲
            if hasattr(self.outline, 'save'):
             self.outline.save()
                
            print("故事主线已保存到文件")

            print("故事主线重新生成成功")
            return True
        except Exception as e:
            print(f"重新生成故事主线时出错: {str(e)}")
            traceback.print_exc()
            return False

    def regenerate_chapter_outline(self, chapter_number: int) -> bool:
        """
        重新生成章节大纲，基于原有内容进行修改而不是完全重新生成

        Args:
            chapter_number: 章节号

        Returns:
            重新生成是否成功
        """
        try:
            # 检查章节号是否合法
            if chapter_number < 1 or chapter_number > self.total_chapters:
                print(f"无效的章节号: {chapter_number}")
                return False

            # 获取原有的章节大纲
            original_outline = self.outline.get_chapter(chapter_number)
            if not original_outline:
                print(f"未找到第{chapter_number}章原有大纲，将进行完全重新生成")
                return self.generate_chapter_outline(chapter_number)
            else:
                print(f"找到第{chapter_number}章原有大纲，将基于原有内容进行修改")

            # 获取前几章摘要
            previous_summary = self.outline.get_previous_chapters_summary(chapter_number)

            # 获取故事主线
            from utils.file_manager import load_main_storyline
            main_storyline_data = load_main_storyline(self.total_chapters)
            
            # 如果返回的是字典，转换为JSON字符串
            if isinstance(main_storyline_data, dict):
                import json
                main_storyline = json.dumps(main_storyline_data, ensure_ascii=False)
            else:
                main_storyline = main_storyline_data

            # 确定本章应该出场的人物
            appearing_characters = self.character_manager.get_characters_for_chapter(chapter_number)
            appearing_characters_str = ", ".join([char.name for char in appearing_characters])
            print(f"本章应出场人物: {appearing_characters_str}")

            # 重新生成章节大纲
            print(f"正在重新生成第{chapter_number}章大纲...")
            outline_content = self.api.regenerate_chapter_outline(
                self.genre,
                self.style_guide,
                str(self.background),
                chapter_number,
                self.total_chapters,
                previous_summary,
                str(self.character_manager),
                str(self.foreshadowing_manager),
                appearing_characters=appearing_characters_str,
                main_storyline=main_storyline,
                original_outline=original_outline.content  # 传入原有大纲
            )

            if not outline_content:
                print(f"重新生成第{chapter_number}章大纲失败")
                return False

            # 更新大纲
            chapter_outline = ChapterOutline(
                chapter_number=chapter_number,
                content=outline_content,
                is_generated=False,
                is_completed=False
            )
            self.outline.add_chapter(chapter_outline)
            self.outline.save()

            print(f"第{chapter_number}章大纲重新生成成功")
            return True
        except Exception as e:
            print(f"重新生成章节大纲时出错: {str(e)}")
            traceback.print_exc()
            return False

    def regenerate_chapter_content(self, chapter_number: int) -> bool:
        """
        重新生成章节内容

        Args:
            chapter_number: 章节号

        Returns:
            重新生成是否成功
        """
        try:
            # 检查是否是字数不足问题
            # 先检查章节内容
            chapter = read_chapter(chapter_number)
            if not chapter:
                print(f"未找到第{chapter_number}章内容")
                return False

            # 获取章节大纲
            chapter_outline = self.outline.get_chapter(chapter_number)
            if not chapter_outline:
                print(f"未找到第{chapter_number}章大纲")
                return False

            # 检查章节内容
            check_result = self.api.check_chapter(
                chapter["content"],
                chapter_outline.content,
                json.dumps(self.character_manager.to_dict(), ensure_ascii=False),
                str(self.background),
                json.dumps(self.foreshadowing_manager.to_dict(), ensure_ascii=False)
            )

            if check_result:
                issues = check_result.get("issues", [])

                # 检查是否只有字数不足问题
                word_count_issues_only = True
                word_count_issues = []
                for issue in issues:
                    if issue.get("type") == "字数不足":
                        word_count_issues.append(issue)
                    else:
                        word_count_issues_only = False

                # 如果只有字数不足问题，尝试使用分段生成方式重新生成
                if word_count_issues_only and word_count_issues:
                    print("检测到只有字数不足问题，尝试使用分段生成方式重新生成...")
                    
                    # 重置生成和完成状态
                    chapter_outline.is_generated = False
                    chapter_outline.is_completed = False
                    self.outline.add_chapter(chapter_outline)
                    self.outline.save()
                    
                    # 使用分段生成方式重新生成
                    success = self._generate_chapter_content_segmented(chapter_number)
                    if success:
                        print("使用分段生成方式重新生成成功")
                        # 验证章节内容
                        check_success, new_check_result = self.check_chapter(chapter_number)
                        if check_success and new_check_result and new_check_result.get("passed", True):
                            print("重新生成的章节检查通过")
                            
                            # 创建NovelChecker实例并执行apply_chapter_updates
                            from novel.checker import NovelChecker
                            checker = NovelChecker()
                            if checker.load_data():
                                # 设置总章节数（必要属性）
                                checker.total_chapters = self.total_chapters
                                # 应用章节更新（更新人物状态和战力）
                                checker.apply_chapter_updates(chapter_number, new_check_result)
                                print(f"第{chapter_number}章的人物状态和战力已更新")
                            else:
                                print(f"警告：无法加载检查器数据，人物状态和战力可能未更新")
                            
                            # 更新章节状态
                            chapter_outline.is_completed = True
                            self.outline.save()
                            return True
                        else:
                            print("重新生成的章节检查未通过，将尝试其他修复方法")
                    else:
                        print("使用分段生成方式重新生成失败，尝试扩充内容方式...")
                        # 如果分段生成失败，回退到扩充内容方式
                    return self.expand_chapter_content(chapter_number, word_count_issues)

                # 分类问题
                background_issues = []
                character_issues = []
                outline_issues = []
                other_issues = []

                for issue in issues:
                    issue_type = issue.get("type", "")
                    if "背景" in issue_type or "世界观" in issue_type or "设定" in issue_type:
                        background_issues.append(issue)
                    elif "人物" in issue_type or "角色" in issue_type:
                        character_issues.append(issue)
                    elif "大纲" in issue_type or "情节" in issue_type or "伏笔" in issue_type:
                        outline_issues.append(issue)
                    else:
                        other_issues.append(issue)

                # 尝试修复各类冲突
                max_fix_attempts = MAX_RETRIES
                fix_attempt = 0
                fixed = False

                while fix_attempt < max_fix_attempts and not fixed:
                    fix_attempt += 1
                    print(f"正在进行第{fix_attempt}/{max_fix_attempts}次修复尝试...")

                    # 先尝试修复背景冲突
                    if background_issues:
                        print(f"检测到{len(background_issues)}个背景冲突问题，尝试修复...")
                        if self.fix_chapter_content(chapter_number, background_issues, "背景冲突"):
                            # 修复后重新检查
                            success, new_check_result = self.check_chapter(chapter_number)
                            if success and new_check_result and new_check_result.get("passed", False):
                                print("背景冲突修复成功，章节检查通过")
                                fixed = True
                                break
                            elif success:
                                # 更新问题列表
                                new_issues = new_check_result.get("issues", [])
                                background_issues = []
                                for issue in new_issues:
                                    issue_type = issue.get("type", "")
                                    if "背景" in issue_type or "世界观" in issue_type or "设定" in issue_type:
                                        background_issues.append(issue)

                                if not background_issues:
                                    print("背景冲突已修复，但仍有其他问题")
                                else:
                                    print(f"背景冲突部分修复，仍有{len(background_issues)}个问题")

                    # 然后尝试修复人物冲突
                    if character_issues and not fixed:
                        print(f"检测到{len(character_issues)}个人物冲突问题，尝试修复...")
                        if self.fix_chapter_content(chapter_number, character_issues, "人物冲突"):
                            # 修复后重新检查
                            success, new_check_result = self.check_chapter(chapter_number)
                            if success and new_check_result and new_check_result.get("passed", False):
                                print("人物冲突修复成功，章节检查通过")
                                fixed = True
                                break
                            elif success:
                                # 更新问题列表
                                new_issues = new_check_result.get("issues", [])
                                character_issues = []
                                for issue in new_issues:
                                    issue_type = issue.get("type", "")
                                    if "人物" in issue_type or "角色" in issue_type:
                                        character_issues.append(issue)

                                if not character_issues:
                                    print("人物冲突已修复，但仍有其他问题")
                                else:
                                    print(f"人物冲突部分修复，仍有{len(character_issues)}个问题")

                    # 最后尝试修复大纲冲突
                    if outline_issues and not fixed:
                        print(f"检测到{len(outline_issues)}个大纲冲突问题，尝试修复...")
                        if self.fix_chapter_content(chapter_number, outline_issues, "大纲冲突"):
                            # 修复后重新检查
                            success, new_check_result = self.check_chapter(chapter_number)
                            if success and new_check_result and new_check_result.get("passed", False):
                                print("大纲冲突修复成功，章节检查通过")
                                fixed = True
                                break
                            elif success:
                                # 更新问题列表
                                new_issues = new_check_result.get("issues", [])
                                outline_issues = []
                                for issue in new_issues:
                                    issue_type = issue.get("type", "")
                                    if "大纲" in issue_type or "情节" in issue_type or "伏笔" in issue_type:
                                        outline_issues.append(issue)

                                if not outline_issues:
                                    print("大纲冲突已修复，但仍有其他问题")
                                else:
                                    print(f"大纲冲突部分修复，仍有{len(outline_issues)}个问题")

                    # 如果所有修复都失败，或者仍有其他问题，继续下一次尝试
                    if not fixed:
                        # 重新检查，获取最新的问题列表
                        success, new_check_result = self.check_chapter(chapter_number)
                        if success and new_check_result and new_check_result.get("passed", False):
                            print("所有问题已修复，章节检查通过")
                            
                            # 创建NovelChecker实例并执行apply_chapter_updates
                            from novel.checker import NovelChecker
                            checker = NovelChecker()
                            if checker.load_data():
                                # 设置总章节数（必要属性）
                                checker.total_chapters = self.total_chapters
                                # 应用章节更新（更新人物状态和战力）
                                checker.apply_chapter_updates(chapter_number, new_check_result)
                                print(f"第{chapter_number}章的人物状态和战力已更新")
                            else:
                                print(f"警告：无法加载检查器数据，人物状态和战力可能未更新")
                            
                            fixed = True
                            break
                        elif success:
                            # 更新问题列表
                            new_issues = new_check_result.get("issues", [])
                            background_issues = []
                            character_issues = []
                            outline_issues = []
                            other_issues = []

                            for issue in new_issues:
                                issue_type = issue.get("type", "")
                                if "背景" in issue_type or "世界观" in issue_type or "设定" in issue_type:
                                    background_issues.append(issue)
                                elif "人物" in issue_type or "角色" in issue_type:
                                    character_issues.append(issue)
                                elif "大纲" in issue_type or "情节" in issue_type or "伏笔" in issue_type:
                                    outline_issues.append(issue)
                                else:
                                    other_issues.append(issue)

                            print(f"修复后仍有问题: 背景({len(background_issues)})，人物({len(character_issues)})，大纲({len(outline_issues)})，其他({len(other_issues)})")

                        if fix_attempt >= max_fix_attempts:
                            print(f"已达到最大修复尝试次数({max_fix_attempts})，将尝试重新生成整个章节")

                # 如果修复成功，返回
                if fixed:
                    return True

            # 如果修复失败，重新生成整个章节
            print("修复失败，重新生成整个章节...")

            # 重置生成和完成状态
            chapter_outline.is_generated = False
            chapter_outline.is_completed = False
            self.outline.add_chapter(chapter_outline)
            self.outline.save()

            # 重新生成内容
            return self.generate_chapter_content(chapter_number)
        except Exception as e:
            print(f"重新生成章节内容时出错: {str(e)}")
            traceback.print_exc()
            return False

    def process_next_chapter(self) -> bool:
        """
        处理下一章节：生成大纲并创建内容

        Returns:
            处理是否成功
        """
        try:
            # 确定下一个章节号
            next_chapter = 1
            for i in range(1, self.total_chapters + 1):
                chapter = self.outline.get_chapter(i)
                if not chapter or not chapter.is_completed:
                    next_chapter = i
                    break
                if i == self.total_chapters:
                    print("所有章节已完成！")
                return True

            print(f"开始处理第{next_chapter}章...")

            # 生成或确认大纲
            if not self.outline.get_chapter(next_chapter):
                # 生成新大纲
                print(f"生成第{next_chapter}章大纲...")
                if not self.generate_chapter_outline(next_chapter):
                    print(f"生成第{next_chapter}章大纲失败")
                    return False

                # 验证大纲一致性
                print(f"验证第{next_chapter}章大纲一致性...")
                if not self.validate_outline_consistency(next_chapter):
                    print(f"第{next_chapter}章大纲存在一致性问题，建议重新生成")
                    # 不强制退出，仅提出建议

            else:
                print(f"第{next_chapter}章大纲已存在")

            # 生成章节内容
            print(f"生成第{next_chapter}章内容...")
            if not self.generate_chapter_content(next_chapter):
                    print(f"生成第{next_chapter}章内容失败")
                    return False

            # 检查章节是否有问题
            print(f"检查第{next_chapter}章内容...")
            success, check_result = self.check_chapter(next_chapter)
            if not success:
                print(f"检查第{next_chapter}章内容失败")
                return False

            if check_result:
                issues = check_result.get("issues", [])
                if issues:
                    print(f"第{next_chapter}章有{len(issues)}个问题需要修复")

                    # 查找冲突类型
                    conflict_types = set()
                    for issue in issues:
                        conflict_types.add(issue.get("type", ""))

                    # 存在设定冲突、逻辑矛盾或伏笔问题，则需要修复
                    if any(conflict_type in ["设定冲突", "逻辑矛盾", "伏笔问题"] for conflict_type in conflict_types):
                        print("存在需要修复的严重问题，开始修复...")
                        if not self.fix_chapter_content(next_chapter, issues, "严重问题"):
                            print("修复失败，请手动检查并修复")
                            return False
                    # 内容不充分，需要扩展
                    elif "内容不充分" in conflict_types:
                        print("内容不充分，开始扩展...")
                        if not self.expand_chapter_content(next_chapter, issues):
                            print("扩展失败，请手动检查并修复")
                            return False
                    # 其他小问题，自动修复
                    elif issues:
                        print("存在小问题，开始修复...")
                        if not self.fix_chapter_content(next_chapter, issues, "小问题"):
                            print("修复失败，请手动检查并修复")
                            return False

                # 更新人物状态和伏笔状态
                print("准备更新章节状态、人物状态和伏笔状态...")
                if check_result:
                    pass  # 人物和伏笔状态将由NovelChecker.apply_chapter_updates统一处理

                # 再次检查，确保所有问题都已修复
                print("最终检查...")
                success, check_result = self.check_chapter(next_chapter)
                if not success:
                    print("最终检查失败")
                    return False

                final_issues = check_result.get("issues", [])
                if final_issues:
                    print(f"警告：仍存在{len(final_issues)}个未解决的问题，但将继续处理")

            # 生成下一章的伏笔（如果当前章节完成）
            if next_chapter < self.total_chapters:
                print(f"生成第{next_chapter + 1}章伏笔...")
                self.generate_chapter_foreshadowing(next_chapter + 1)

            # 章节检查已通过
                print(f"第{next_chapter}章处理完成！")
                return True
        except Exception as e:
            print(f"处理章节时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def export_novel(self, output_path: Optional[str] = None) -> bool:
        """
        导出小说

        Args:
            output_path: 输出路径，如果为None则使用默认路径

        Returns:
            导出是否成功
        """
        try:
            # 确保所有元数据已保存
            if self.outline:
                self.outline.save()
            if self.character_manager:
                self.character_manager.save()
            if self.foreshadowing_manager:
                self.foreshadowing_manager.save()

            # 导出小说
            success = export_novel_as_txt(output_path)
            if success:
                print(f"小说导出成功: {output_path}")
            else:
                print("小说导出失败")

            return success
        except Exception as e:
            print(f"导出小说时出错: {str(e)}")
            return False

    def save_novel_info(self) -> bool:
        """
        保存小说信息到文件

        Returns:
            保存是否成功
        """
        try:
            # 获取小说信息
            novel_info = load_novel_info()
            if not novel_info:
                # 如果没有现有信息，尝试创建一个基本的信息
                title = ""
                author = "AI作家"

                # 尝试从人物和背景中提取合适的标题
                if self.character_manager:
                    main_character = self.character_manager.get_main_character()
                    if main_character:
                        title = f"{main_character.name}的{self.genre}之旅"

                if not title:
                    title = f"{self.genre}奇缘"

                description = f"一部{self.genre}风格的男频网络小说，预计{self.total_chapters}章，约{TARGET_NOVEL_LENGTH/10000}万字。"
            else:
                # 使用现有信息
                title = novel_info.get("title", "")
                author = novel_info.get("author", "AI作家")
                description = novel_info.get("description", "")

            # 保存小说信息
            return save_novel_info(
                title=title,
                author=author,
                genre=self.genre,
                description=description
            )
        except Exception as e:
            print(f"保存小说信息时出错: {str(e)}")
            return False

    def get_completion_progress(self) -> Tuple[int, int, float]:
        """
        获取完成进度

        Returns:
            (已完成章节数, 总章节数, 完成百分比)
        """
        if not self.outline:
            return 0, 0, 0.0

        # 从大纲中获取标记为已完成的章节数
        completed_in_outline = self.outline.get_completed_chapters_count()

        # 获取实际存在的章节文件数
        from utils.file_manager import get_all_chapters
        existing_chapters = get_all_chapters()
        completed_in_files = len(existing_chapters)

        # 取两者中的最大值作为已完成章节数
        completed = max(completed_in_outline, completed_in_files)

        # 如果检测到不一致，打印警告信息
        if completed_in_outline != completed_in_files:
            print(f"警告：大纲中标记为已完成的章节数({completed_in_outline})与实际文件数({completed_in_files})不一致")

        total = self.total_chapters
        percentage = (completed / total) * 100 if total > 0 else 0

        return completed, total, percentage

    def generate_chapter_foreshadowing(self, chapter_number: int) -> bool:
        """
        为指定章节生成新伏笔

        Args:
            chapter_number: 章节号

        Returns:
            是否成功
        """
        try:
            # 确保章节号有效
            if chapter_number < 1 or chapter_number > self.total_chapters:
                print(f"无效的章节号: {chapter_number}")
                return False

            # 确保已有章节大纲
            chapter_outline = self.outline.get_chapter(chapter_number)
            if not chapter_outline:
                print(f"未找到第{chapter_number}章的大纲")
                return False

            # 获取小说流派
            novel_info = load_novel_info()
            if not novel_info:
                print("未找到小说信息")
                return False

            genre = novel_info.get("genre", "")

            # 生成章节伏笔
            print(f"正在为第{chapter_number}章生成新伏笔...")
            foreshadowing_content = self.api.generate_chapter_foreshadowing(
                genre,
                self.style_guide,
                str(self.background),
                json.dumps(self.character_manager.to_dict(), ensure_ascii=False),
                chapter_outline.content,
                chapter_number,
                self.total_chapters
            )

            if not foreshadowing_content:
                print(f"生成第{chapter_number}章新伏笔失败")
                return False

            # 使用安全的JSON解析函数解析伏笔内容
            foreshadowing_data = parse_json_safely(foreshadowing_content, "foreshadowing")

            if not foreshadowing_data:
                raise Exception("无法从伏笔内容中解析JSON数据")

            # 添加新伏笔
            new_foreshadowings_count = 0
            for fs_data in foreshadowing_data.get("foreshadowings", []):
                # 创建新伏笔
                self.foreshadowing_manager.add_foreshadowing_from_dict(fs_data, self.total_chapters)
                new_foreshadowings_count += 1

            self.foreshadowing_manager.save()
            print(f"为第{chapter_number}章生成了{new_foreshadowings_count}个新伏笔")
            return True
        except Exception as e:
            print(f"生成第{chapter_number}章新伏笔时出错: {str(e)}")
            print(f"尝试重新生成第{chapter_number}章新伏笔...")

            # 最大重试次数
            max_retries = MAX_RETRIES
            for attempt in range(max_retries):
                try:
                    # 重新生成伏笔
                    print(f"重新生成第{chapter_number}章伏笔 (尝试 {attempt+1}/{max_retries})...")
                    foreshadowing_content = self.api.generate_chapter_foreshadowing(
                        genre,
                        self.style_guide,
                        str(self.background),
                        json.dumps(self.character_manager.to_dict(), ensure_ascii=False),
                        chapter_outline.content,
                        chapter_number,
                        self.total_chapters
                    )

                    if not foreshadowing_content:
                        print(f"重新生成第{chapter_number}章伏笔失败")
                        if attempt < max_retries - 1:
                            continue
                        else:
                            return False

                    # 使用新的JSON解析工具进行解析
                    foreshadowing_data = parse_json_safely(foreshadowing_content, "foreshadowing")

                    if not foreshadowing_data:
                        raise Exception("无法从伏笔内容中解析JSON数据")

                    # 添加新伏笔
                    new_foreshadowings_count = 0
                    for fs_data in foreshadowing_data.get("foreshadowings", []):
                        # 创建新伏笔
                        self.foreshadowing_manager.add_foreshadowing_from_dict(fs_data, self.total_chapters)
                        new_foreshadowings_count += 1

                    self.foreshadowing_manager.save()
                    print(f"为第{chapter_number}章重新生成了{new_foreshadowings_count}个新伏笔")
                    return True

                except Exception as retry_error:
                    print(f"重新生成第{chapter_number}章伏笔时出错: {str(retry_error)}")
                    if attempt < max_retries - 1:
                        print(f"将进行第{attempt+2}次尝试...")
                    else:
                        print(f"已达到最大重试次数，无法为第{chapter_number}章生成伏笔")
                        return False

            return False

    def validate_outline_consistency(self, chapter_number: int) -> bool:
        """
        验证大纲与已有内容的一致性，包括场景和伏笔

        Args:
            chapter_number: 章节号

        Returns:
            是否一致
        """
        try:
            # 获取章节大纲
            chapter_outline = self.outline.get_chapter(chapter_number)
            if not chapter_outline:
                print(f"未找到第{chapter_number}章大纲")
                return False

            # 检查大纲中的伏笔和场景
            outline_foreshadowings = self._extract_outline_foreshadowings(chapter_outline.content)
            outline_scenes = self._extract_outline_scenes(chapter_outline.content)

            # 验证伏笔状态
            updates = []
            for fs_id, status in outline_foreshadowings.items():
                fs = self.foreshadowing_manager.get_foreshadowing(fs_id)
                if fs:
                    # 检查状态是否一致
                    if "埋下" in status and fs.status == "未埋下":
                        updates.append({"id": fs_id, "status": "已埋下未回收"})
                        print(f"伏笔 {fs_id} 在大纲中被埋下，但状态仍为'未埋下'，将更新状态")
                    elif "回收" in status and fs.status in ["未埋下", "已埋下未回收"]:
                        updates.append({"id": fs_id, "status": "已回收"})
                        print(f"伏笔 {fs_id} 在大纲中被回收，但状态为'{fs.status}'，将更新状态")

            # 如果有需要更新的伏笔，进行更新
            if updates:
                updated_count = self.foreshadowing_manager.batch_update_foreshadowing_status(updates)
                self.foreshadowing_manager.save()
                print(f"已更新{updated_count}个伏笔状态")

            # 检查是否有重复场景（与前几章对比）
            if chapter_number > 1:
                # 获取前几章的场景
                previous_scenes = []
                # 检查最近的3章
                start_chapter = max(1, chapter_number - 3)
                for i in range(start_chapter, chapter_number):
                    prev_chapter = self.outline.get_chapter(i)
                    if prev_chapter:
                        prev_scenes = self._extract_outline_scenes(prev_chapter.content)
                        if prev_scenes:
                            previous_scenes.extend(prev_scenes)

                # 检查场景重复率
                if previous_scenes and outline_scenes:
                    common_scenes = set(outline_scenes).intersection(set(previous_scenes))
                    if common_scenes:
                        scene_similarity = len(common_scenes) / len(outline_scenes)
                        if scene_similarity > 0.5:  # 如果超过50%的场景重复
                            print(f"警告：第{chapter_number}章与前几章有较高的场景重复率({scene_similarity:.2%})")
                            print(f"重复场景: {', '.join(list(common_scenes))}")
                            return False

            return True
        except Exception as e:
            print(f"验证大纲一致性时出错: {str(e)}")
            return False

    def test_foreshadowing_initialization(self) -> bool:
        """
        测试方法：直接测试伏笔初始化，跳过前面的步骤

        Returns:
            初始化是否成功
        """
        try:
            print("开始直接测试伏笔初始化...")

            # 直接调用API生成伏笔
            foreshadowing_content = self.api.generate_foreshadowing(
                self.genre,
                self.style_guide,
                str(self.background),
                str(self.character_manager),
                str(self.outline),
                self.total_chapters
            )

            if not foreshadowing_content:
                print("生成伏笔失败")
                return False

            # 尝试解析JSON
            try:
                import json
                # 在解析前清理JSON字符串
                if hasattr(self.api, '_clean_json_string'):
                    cleaned_content = self.api._clean_json_string(foreshadowing_content)
                    foreshadowing_data = json.loads(cleaned_content)
                else:
                    foreshadowing_data = json.loads(foreshadowing_content)

                print("成功解析伏笔JSON")
                print(f"生成了 {len(foreshadowing_data.get('foreshadowings', []))} 个伏笔")

                # 初始化伏笔管理器
                self.foreshadowing_manager.initialize_from_json(foreshadowing_data)
                print("伏笔初始化成功")
                return True
            except json.JSONDecodeError as e:
                print(f"解析伏笔JSON失败: {e}")
                return False
            except Exception as e:
                print(f"初始化伏笔管理器时出错: {e}")
                return False
        except Exception as e:
            print(f"测试伏笔初始化时出错: {e}")
            return False

    def fix_json_comprehensive(self, json_str: str) -> str:
        """
        全面修复JSON中的常见格式错误，整合了generate_outline.py中的高级修复逻辑
        
        Args:
            json_str: 要修复的JSON字符串
        
        Returns:
            修复后的JSON字符串
        """
        # 记录处理前的JSON（用于调试）
        if len(json_str) > 200:
            print(f"原始JSON前100字符: {json_str[:100]}...")
            print(f"原始JSON后100字符: {json_str[-100:]}...")
        else:
            print(f"原始JSON: {json_str}")
        
        # 0. 移除Markdown代码块标记
        # 移除开头的```json或```等标记
        json_str = re.sub(r'^```\w*\s*', '', json_str)
        # 移除结尾的```标记
        json_str = re.sub(r'\s*```\s*$', '', json_str)
            
        # 1. 特殊处理 '\n "title"' 格式错误（这是导致KeyError的主要原因）
        json_str = re.sub(r'\\n\s+"([^"]+)":', r', "\1":', json_str)
        json_str = re.sub(r'\n\s+"([^"]+)":', r', "\1":', json_str)
        
        # 2. 先移除所有实际换行，转为一行文本，便于处理
        flattened = re.sub(r'[\r\n]+', ' ', json_str)
        
        # 3. 移除多余的空格，保持每个标记之间只有一个空格
        flattened = re.sub(r'\s+', ' ', flattened)
        
        # 4. 特殊处理属性名前缺失逗号的情况（如title前缺少逗号）
        flattened = re.sub(r'(\}|\]|"[^"]*")\s+"([^"]+)":', r'\1, "\2":', flattened)
        
        # 5. 尝试使用json模块的loads方法解析
        try:
            data = json.loads(flattened)
            print("初步修复后JSON解析成功")
            return flattened
        except json.JSONDecodeError as e:
            print(f"初步修复后仍解析失败: {str(e)}")
            
            # 记录错误位置附近的内容
            error_pos = e.pos
            start_pos = max(0, error_pos - 50)
            end_pos = min(len(flattened), error_pos + 50)
            error_context = flattened[start_pos:end_pos]
            
            # 显示错误位置的上下文
            print(f"错误位置附近的内容: {error_context}")
            print(f"错误位置指示: {' ' * (min(50, error_pos - start_pos))}^")
            
            # 继续修复
        
        # 6. 针对常见错误的更具体修复
        # 6.1 处理对象/数组结尾后没有逗号直接跟属性名的情况
        # 例如: {"items": [1, 2, 3]} "next": 4 -> {"items": [1, 2, 3]}, "next": 4
        flattened = re.sub(r'(\}|\])\s+"([^"]+)":', r'\1, "\2":', flattened)
        
        # 6.2 处理值后面没有逗号直接跟属性名的情况
        # 例如: {"name": "value" "next": 123} -> {"name": "value", "next": 123}
        flattened = re.sub(r'"[^"]*"\s+"([^"]+)":', r'", "\1":', flattened)
        
        # 6.3 处理数值、布尔值、null后面没有逗号直接跟属性名的情况
        flattened = re.sub(r'(true|false|null|[0-9]+)\s+"([^"]+)":', r'\1, "\2":', flattened)
        
        # 6.4 处理数组元素间缺少逗号的情况
        flattened = re.sub(r'("[^"]*"|[0-9]+|true|false|null)\s+("[^"]*")', r'\1, \2', flattened)
        flattened = re.sub(r'(\}|\])\s+(\{|\[)', r'\1, \2', flattened)
        
        # 6.5 修复多余的逗号（数组或对象末尾的逗号）
        flattened = re.sub(r',\s*(\}|\])', r'\1', flattened)
        
        # 6.6 确保括号匹配
        open_braces = flattened.count('{')
        close_braces = flattened.count('}')
        open_brackets = flattened.count('[')
        close_brackets = flattened.count(']')
        
        if open_braces > close_braces:
            flattened += '}' * (open_braces - close_braces)
        if open_brackets > close_brackets:
            flattened += ']' * (open_brackets - close_brackets)
        
        # 7. 最终规范化格式
        flattened = flattened.strip()
        
        # 8. 再次尝试解析修复后的JSON
        try:
            data = json.loads(flattened)
            print("深度修复后JSON解析成功")
            return flattened
        except json.JSONDecodeError as e:
            print(f"深度修复后仍解析失败: {str(e)}")
            
            # 9. 尝试使用更强大的JSON修复工具 - 类似generate_outline.py中的最终尝试
            try:
                # 导入fix_storyline_json
                try:
                    from utils.fix_storyline import fix_storyline_json
                    fixed_data = fix_storyline_json(json_str)  # 注意这里用原始json_str而不是flattened
                    
                    if fixed_data:
                        print("使用fix_storyline_json修复成功")
                        # 将字典转换回JSON字符串
                        return json.dumps(fixed_data, ensure_ascii=False)
                    else:
                        print("使用fix_storyline_json修复失败")
                except ImportError:
                    print("无法导入fix_storyline_json函数")
                except Exception as e2:
                    print(f"使用fix_storyline_json修复时出错: {e2}")
                
                # 尝试使用extract_nested_json（如果有的话）
                try:
                    from utils.json_helper import extract_nested_json
                    extracted_json = extract_nested_json(json_str)
                    if extracted_json:
                        try:
                            data = json.loads(extracted_json)
                            print("使用extract_nested_json成功提取JSON")
                            return extracted_json
                        except json.JSONDecodeError:
                            print("提取的嵌套JSON仍无法解析")
                except ImportError:
                    print("无法导入extract_nested_json函数")
                except Exception as e3:
                    print(f"使用extract_nested_json时出错: {e3}")
            except Exception as e4:
                print(f"所有额外的修复尝试都失败: {e4}")
            
            # 10. 返回最后的修复尝试，虽然可能仍然有问题
            print("所有修复方法都失败，返回最后的修复尝试结果")
            return flattened

    def test_generate_outline_directly(self, chapter_number: int = 1, max_retries: int = 3) -> bool:
        """
        测试直接生成大纲（跳过前面的步骤）
        
        Args:
            chapter_number: 章节号
            max_retries: 最大重试次数
            
        Returns:
            生成是否成功
        """
        try:
            # 初始化API（如果尚未初始化）
            if not hasattr(self, 'api') or self.api is None:
                self.init_apis()
            
            # 从主线中提取当前章节内容
            from utils.file_manager import load_main_storyline
            main_storyline_data = load_main_storyline(self.total_chapters)
            
            # 如果返回的是字典，转换为JSON字符串
            if isinstance(main_storyline_data, dict):
                import json
                main_storyline = json.dumps(main_storyline_data, ensure_ascii=False)
            else:
                main_storyline = main_storyline_data
                
            chapter_guide = None
            
            if main_storyline:
                # 尝试使用API的_extract_chapter_from_storyline方法
                if hasattr(self.api, '_extract_chapter_from_storyline'):
                    chapter_guide = self.api._extract_chapter_from_storyline(main_storyline, chapter_number)
                    print(f"提取第{chapter_number}章内容，主线类型为: {type(main_storyline)}")
                else:
                    print(f"API没有_extract_chapter_from_storyline方法，无法提取第{chapter_number}章指南")
            
            # 生成标题和大纲
            for attempt in range(max_retries):
                try:
                    print(f"生成第 {chapter_number} 章大纲（尝试 {attempt + 1}/{max_retries}）...")
                    
                    # 调用API生成大纲
                    outline_content = self.api.generate_outline(
                        self.genre,
                        self.style_guide,
                        str(self.background),
                        chapter_number,
                        self.total_chapters,
                        "",  # 空的previous_summary
                        "",  # 空的characters_info
                        "",  # 空的foreshadowing_info
                        main_storyline=main_storyline
                    )
                    
                    if not outline_content:
                        print("API返回空大纲内容")
                        continue
                    
                    # 保存原始JSON到debug文件（仅用于调试）
                    debug_path = os.path.join(OUTPUT_DIR, "debug.json")
                    with open(debug_path, 'w', encoding='utf-8') as f:
                        f.write(outline_content)
                    
                    # 应用全面的JSON修复
                    fixed_content = self.fix_json_comprehensive(outline_content)
                    
                    # 打印清理后的JSON（部分）
                    print(f"清理后JSON（前200字符）: {fixed_content[:200]}...")
                    
                    # 尝试解析修复后的JSON
                    try:
                        outline_data = json.loads(fixed_content)
                        print("标准JSON解析成功")
                        
                        # 保存修复后的JSON（仅用于调试）
                        fixed_path = os.path.join(OUTPUT_DIR, "fixed_debug.json")
                        with open(fixed_path, 'w', encoding='utf-8') as f:
                            json.dump(outline_data, f, ensure_ascii=False, indent=2)
                        print(f"修复后的JSON已保存到: {fixed_path} (仅用于调试)")
                        
                        # 创建章节大纲对象并保存到outline.json
                        # 从解析后的数据提取章节信息
                        try:
                            print("创建章节大纲对象...")
                            from models.outline import ChapterOutline
                            
                            # 提取标题
                            title = outline_data.get("title", f"第{chapter_number}章")
                            
                            # 创建章节大纲对象
                            chapter_outline = ChapterOutline(
                                chapter_number=chapter_number,
                                title=title,
                                content=fixed_content,  # 保存整个内容
                                is_generated=False,  # 这里只生成了大纲，章节内容尚未生成
                                is_completed=False
                            )
                            
                            # 如果outline_data中有结构化的数据，添加到章节大纲对象
                            if isinstance(outline_data, dict):
                                # 提取章节概要
                                if "chapter_summary" in outline_data:
                                    chapter_outline.chapter_summary = outline_data["chapter_summary"]
                                
                                # 提取关键情节点
                                if "key_points" in outline_data:
                                    chapter_outline.key_points = outline_data["key_points"]
                                
                                # 提取出场人物
                                if "characters" in outline_data:
                                    chapter_outline.characters = outline_data["characters"]
                                
                                # 提取伏笔安排
                                if "foreshadowings" in outline_data:
                                    chapter_outline.foreshadowings = outline_data["foreshadowings"]
                            
                            # 将章节大纲添加到管理器并保存
                            print(f"添加第{chapter_number}章大纲到outline.json...")
                            if self.outline:
                                self.outline.add_chapter(chapter_outline)
                                success = self.outline.save()
                                if success:
                                    print(f"成功保存第{chapter_number}章大纲到outline.json")
                                else:
                                    print("保存大纲到outline.json失败")
                            else:
                                print("找不到outline管理器，无法保存")
                                
                                # 尝试创建新的outline管理器
                                from models.outline import ChapterOutlineManager
                                self.outline = ChapterOutlineManager(self.total_chapters)
                                self.outline.add_chapter(chapter_outline)
                                success = self.outline.save()
                                if success:
                                    print(f"创建新的outline管理器并保存第{chapter_number}章大纲成功")
                                else:
                                    print("创建新的outline管理器并保存大纲失败")
                        except Exception as e:
                            print(f"创建或保存章节大纲时出错: {e}")
                            import traceback
                            traceback.print_exc()
                        
                        return True
                    except json.JSONDecodeError as e:
                        print(f"生成大纲时发生错误: {str(e)}")
                        
                        # 尝试使用更强大的JSON修复
                        try:
                            # 使用utils.fix_storyline中的函数
                            from utils.fix_storyline import fix_storyline_json
                            fixed_data = fix_storyline_json(outline_content)
                            
                            if fixed_data:
                                print("使用fix_storyline_json修复成功")
                                
                                # 保存修复后的数据（仅用于调试）
                                fixed_path = os.path.join(OUTPUT_DIR, "fixed_storyline.json")
                                with open(fixed_path, 'w', encoding='utf-8') as f:
                                    json.dump(fixed_data, f, ensure_ascii=False, indent=2)
                                print(f"storyline修复后的JSON已保存到: {fixed_path} (仅用于调试)")
                                
                                # 创建章节大纲对象并保存到outline.json
                                try:
                                    print("创建章节大纲对象(使用fix_storyline_json数据)...")
                                    from models.outline import ChapterOutline
                                    
                                    # 提取标题
                                    title = fixed_data.get("title", f"第{chapter_number}章")
                                    
                                    # 创建章节大纲对象
                                    chapter_outline = ChapterOutline(
                                        chapter_number=chapter_number,
                                        title=title,
                                        content=json.dumps(fixed_data, ensure_ascii=False),  # 将修复后的数据转为字符串
                                        is_generated=False,
                                        is_completed=False
                                    )
                                    
                                    # 如果fixed_data中有结构化的数据，添加到章节大纲对象
                                    if isinstance(fixed_data, dict):
                                        # 提取章节概要
                                        if "chapter_summary" in fixed_data:
                                            chapter_outline.chapter_summary = fixed_data["chapter_summary"]
                                        
                                        # 提取关键情节点
                                        if "key_points" in fixed_data:
                                            chapter_outline.key_points = fixed_data["key_points"]
                                        
                                        # 提取出场人物
                                        if "characters" in fixed_data:
                                            chapter_outline.characters = fixed_data["characters"]
                                        
                                        # 提取伏笔安排
                                        if "foreshadowings" in fixed_data:
                                            chapter_outline.foreshadowings = fixed_data["foreshadowings"]
                                    
                                    # 将章节大纲添加到管理器并保存
                                    print(f"添加第{chapter_number}章大纲到outline.json...")
                                    if self.outline:
                                        self.outline.add_chapter(chapter_outline)
                                        success = self.outline.save()
                                        if success:
                                            print(f"成功保存第{chapter_number}章大纲到outline.json")
                                        else:
                                            print("保存大纲到outline.json失败")
                                    else:
                                        print("找不到outline管理器，无法保存")
                                        
                                        # 尝试创建新的outline管理器
                                        from models.outline import ChapterOutlineManager
                                        self.outline = ChapterOutlineManager(self.total_chapters)
                                        self.outline.add_chapter(chapter_outline)
                                        success = self.outline.save()
                                        if success:
                                            print(f"创建新的outline管理器并保存第{chapter_number}章大纲成功")
                                        else:
                                            print("创建新的outline管理器并保存大纲失败")
                                except Exception as e:
                                    print(f"创建或保存章节大纲时出错: {e}")
                                    import traceback
                                    traceback.print_exc()
                                
                                return True
                            else:
                                print("使用fix_storyline_json修复失败")
                        except Exception as e2:
                            print(f"尝试修复JSON时出错: {e2}")
                except Exception as e:
                    print(f"尝试生成大纲时出错: {e}")
            
            print(f"达到最大重试次数，生成大纲失败")
            return False
        except Exception as e:
            print(f"test_generate_outline_directly方法出错: {e}")
            return False

    def _validate_outline_quality(self, chapter_outline) -> bool:
        """
        验证章节大纲的质量

        Args:
            chapter_outline: 章节大纲对象

        Returns:
            大纲质量是否合格
        """
        try:
            # 检查章节内容长度
            content = chapter_outline.content
            if len(content) < 100:  # 内容过短
                print(f"警告：章节大纲内容过短，仅有{len(content)}个字符")
                return False

            # 如果大纲是JSON格式（结构化数据）
            if content.strip().startswith('{') and content.strip().endswith('}'):
                try:
                    # 检查是否有必要的结构化字段
                    if not chapter_outline.chapter_summary or not any(chapter_outline.chapter_summary.values()):
                        print("警告：章节大纲缺少章节概要内容")
                        return False

                    if not chapter_outline.key_points or len(chapter_outline.key_points) < 2:
                        print("警告：章节大纲缺少足够的关键情节点")
                        return False

                    # 检查伏笔安排是否符合章节要求
                    chapter_number = chapter_outline.chapter_number
                    foreshadowings_to_plant = self.foreshadowing_manager.get_foreshadowings_to_plant(chapter_number)
                    foreshadowings_to_reveal = self.foreshadowing_manager.get_foreshadowings_to_reveal(chapter_number)

                    # 如果有需要埋下的伏笔，检查是否在大纲中有相应安排
                    if foreshadowings_to_plant:
                        if not chapter_outline.foreshadowings or "planted" not in chapter_outline.foreshadowings:
                            print(f"警告：第{chapter_number}章需要埋下伏笔，但大纲中没有相应安排")
                            return False

                        # 验证每个应该埋下的伏笔是否在大纲中
                        planned_fs_ids = [fs.get("id", "") for fs in chapter_outline.foreshadowings.get("planted", [])]
                        for fs in foreshadowings_to_plant:
                            if fs.id not in planned_fs_ids:
                                print(f"警告：第{chapter_number}章需要埋下伏笔{fs.id}，但大纲中未包含")
                                return False

                    # 如果有需要回收的伏笔，检查是否在大纲中有相应安排
                    if foreshadowings_to_reveal:
                        if not chapter_outline.foreshadowings or "revealed" not in chapter_outline.foreshadowings:
                            print(f"警告：第{chapter_number}章需要回收伏笔，但大纲中没有相应安排")
                            return False

                        # 验证每个应该回收的伏笔是否在大纲中
                        revealed_fs_ids = [fs.get("id", "") for fs in chapter_outline.foreshadowings.get("revealed", [])]
                        for fs in foreshadowings_to_reveal:
                            if fs.id not in revealed_fs_ids:
                                print(f"警告：第{chapter_number}章需要回收伏笔{fs.id}，但大纲中未包含")
                                return False

                    # 严格检查：确保没有额外的伏笔被错误地包含在大纲中
                    if chapter_outline.foreshadowings and "planted" in chapter_outline.foreshadowings:
                        # 获取应该埋下的伏笔ID列表
                        plant_fs_ids = [fs.id for fs in foreshadowings_to_plant]

                        # 检查大纲中的每个伏笔是否都在应该埋下的列表中
                        for fs in chapter_outline.foreshadowings.get("planted", []):
                            fs_id = fs.get("id", "")
                            if fs_id and fs_id not in plant_fs_ids:
                                print(f"警告：大纲中包含了不应在第{chapter_number}章埋下的伏笔{fs_id}")
                                return False

                    # 检查大纲中回收的伏笔是否都是应该在本章回收的
                    if chapter_outline.foreshadowings and "revealed" in chapter_outline.foreshadowings:
                        # 获取应该回收的伏笔ID列表
                        reveal_fs_ids = [fs.id for fs in foreshadowings_to_reveal]

                        # 检查大纲中的每个伏笔是否都在应该回收的列表中
                        for fs in chapter_outline.foreshadowings.get("revealed", []):
                            fs_id = fs.get("id", "")
                            if fs_id and fs_id not in reveal_fs_ids:
                                print(f"警告：大纲中包含了不应在第{chapter_number}章回收的伏笔{fs_id}")
                                return False

                except Exception as e:
                    print(f"验证结构化大纲时出错：{e}")
                    # 错误不一定导致验证失败，继续检查其他方面

            # 检查是否使用泛称而没有角色具体名字
            generic_names = ["男主", "女主", "主角", "男主角", "女主角"]
            for name in generic_names:
                if name in content:
                    # 在使用泛称的同时，检查是否也使用了具体名字
                    has_specific_names = False
                    for character in self.character_manager.characters:
                        if character.name in content:
                            has_specific_names = True
                            break

                    # 如果只有泛称没有具体名字，则质量不合格
                    if not has_specific_names:
                        print(f"警告：章节大纲中使用了泛称({name})而没有角色具体名字")
                        return False

            # 所有检查都通过
            return True
        except Exception as e:
            print(f"验证章节大纲质量时出错：{str(e)}")
            return False

    def init_apis(self):
        """初始化API接口"""
        # 使用已导入的DeepSeekAllAPI
        self.api = DeepSeekAllAPI()

    def generate_style_and_background(self, genre: str) -> bool:
        """
        只生成风格和背景设定，不生成其他内容
        
        Args:
            genre: 小说流派
        
        Returns:
            生成是否成功
        """
        try:
            self.genre = genre
            
            # 生成风格提示词
            print(f"正在生成{genre}流派的风格提示词...")
            self.style_guide = self.api.generate_novel_style(
                genre,
                target_length=TARGET_NOVEL_LENGTH,
                total_chapters=self.total_chapters
            )
            if not self.style_guide:
                print("生成风格提示词失败")
                return False
                
            # 保存风格提示词到文件
            from utils.file_manager import save_style_guide
            save_style_guide(self.style_guide)
            print("风格提示词生成成功")
            
            # 生成背景设定
            print("正在生成背景设定...")
            background_data = self.api.generate_background(genre, self.style_guide)
            if not background_data:
                print("生成背景设定失败")
                return False
                
            self.background = Background(
                genre,
                background_data.get("content", ""),
                background_data.get("categories", {})
            )
            self.background.save()
            print("背景设定生成成功")
            
            # 锁定背景设定，防止后续修改
            self._background_locked = True
            
            # 预估章节数
            self.total_chapters = math.ceil(TARGET_NOVEL_LENGTH / DEFAULT_CHAPTER_LENGTH)
            print(f"预计总章节数: {self.total_chapters}")
            
            # 更新小说信息
            from utils.file_manager import save_novel_info
            novel_info = {
                "genre": genre,
                "author": "AI作家",
                "title": f"{genre}小说",  # 临时标题，将在生成主线时更新
                "total_chapters": self.total_chapters,
                "current_chapter": 0,
                "status": "初始化中",
                "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            save_novel_info(novel_info)
            
            print("\n风格和背景设定生成完成！")
            print("您可以使用 'python main.py generate_characters_and_foreshadowings' 命令继续生成人物和伏笔。")
            
            return True
        except Exception as e:
            print(f"生成风格和背景设定时出错: {str(e)}")
            traceback.print_exc()
            return False
    
    def generate_characters_and_foreshadowings(self) -> bool:
        """
        只生成人物和伏笔，需要先生成风格和背景
        
        Returns:
            生成是否成功
        """
        try:
            # 检查是否已有风格和背景
            if not self.load_project():
                print("加载项目失败，请确保已经生成了风格和背景设定")
                return False
                
            if not self.style_guide or not self.background:
                print("未找到风格指南或背景设定，请先运行generate_style_and_background")
                return False
            
            # 生成故事主线概述（生成人物需要主线）
            print("正在生成故事主线概述...")
            main_storyline = None
            max_retries = MAX_RETRIES
            for attempt in range(max_retries):
                # 如果是第一次尝试，完全重新生成
                if attempt == 0 or main_storyline is None:
                    main_storyline = self.api.generate_main_storyline(
                        self.genre, self.style_guide, str(self.background), self.total_chapters
                    )
                else:
                    # 如果是重试，基于上一次生成的内容进行修改
                    print(f"正在重新生成故事主线 ({attempt + 1}/{max_retries})，基于上一次生成的内容进行修改...")
                    main_storyline = self.api.generate_main_storyline(
                        self.genre, self.style_guide, str(self.background), self.total_chapters,
                        original_storyline=main_storyline  # 传入上一次生成的内容
                    )

                if not main_storyline:
                    print("生成故事主线概述失败")
                    if attempt < max_retries - 1:
                        print(f"正在重试 ({attempt + 1}/{max_retries})...")
                        continue
                    return False
                    
                # 解析故事主线数据以进行质量检查
                try:
                    storyline_data = json.loads(main_storyline)
                    # 从utils.fix_storyline导入质量检查函数
                    from utils.fix_storyline import validate_storyline_quality
                    
                    # 进行质量检查
                    print("正在检查故事主线质量...")
                    quality_check = validate_storyline_quality(storyline_data)
                    
                    if not quality_check["valid"]:
                        print(f"故事主线质量检查未通过，问题: {quality_check['issues']}")
                        if attempt < max_retries - 1:
                            print(f"正在重新生成故事主线 ({attempt + 1}/{max_retries})...")
                            continue
                        else:
                            print(f"已达到最大重试次数({max_retries})，使用最后一次生成的故事主线")
                    else:
                        print(f"故事主线质量检查通过，总字数: {quality_check['total_length']}")
                        if quality_check.get('character_names'):
                            print(f"检测到的角色名称: {', '.join(quality_check['character_names'])}")
                except Exception as e:
                    print(f"故事主线质量检查失败: {str(e)}")
                    # 失败不阻止流程，继续使用当前生成的故事主线
                
                # 直接保存故事主线，不进行校验
                from utils.file_manager import save_main_storyline
                save_main_storyline(main_storyline)
                print("故事主线概述生成成功")
                
                # 初始化大纲，并包含主线信息
                self.outline = NovelOutline(total_chapters=self.total_chapters, main_storyline=main_storyline)
                self.outline.save()
                break
                
            if not main_storyline:
                print("生成故事主线概述失败")
                return False
            
            # 生成人物卡片
            print("正在生成人物卡片...")
            characters_content = None
            max_retries = MAX_RETRIES
            for attempt in range(max_retries):
                characters_content = self.api.generate_characters(
                    self.genre, self.style_guide, str(self.background), main_storyline
                )
                if not characters_content:
                    print("生成人物卡片失败")
                    if attempt < max_retries - 1:
                        print(f"正在重试 ({attempt + 1}/{max_retries})...")
                        continue
                    return False

                # 解析人物卡片
                try:
                    # 尝试提取JSON部分
                    import re
                    json_match = re.search(r'```json\s*(.*?)\s*```', characters_content, re.DOTALL)
                    if json_match:
                        characters_content = json_match.group(1)

                    # 清理可能导致解析错误的内容
                    # 移除注释
                    characters_content = re.sub(r'//.*?(\n|$)', '\n', characters_content)
                    # 修复可能的格式问题
                    characters_content = characters_content.replace("'", '"')  # 将单引号替换为双引号
                    characters_content = re.sub(r',\s*}', '}', characters_content)  # 移除对象末尾多余的逗号
                    characters_content = re.sub(r',\s*]', ']', characters_content)  # 移除数组末尾多余的逗号

                    # 尝试解析JSON
                    try:
                        characters_data = json.loads(characters_content)
                    except json.JSONDecodeError as e:
                        print(f"JSON解析错误: {str(e)}")
                        print("尝试修复JSON格式...")

                        # 应用更全面的JSON修复方法
                        fixed_content = self.fix_json_comprehensive(characters_content)
                        try:
                            characters_data = json.loads(fixed_content)
                            print("JSON修复成功！")
                        except json.JSONDecodeError as e:
                            print(f"修复后仍然无法解析JSON: {str(e)}")
                            # 如果仍然失败，重新生成人物卡片
                            if attempt < max_retries - 1:
                                print(f"正在重新生成人物卡片 ({attempt + 1}/{max_retries})...")
                                continue
                            else:
                                print(f"已达到最大重试次数({max_retries})，使用最后一次生成的人物卡片")
                                raise

                    # 检查人物是否与故事主线冲突
                    print("检查人物是否与故事主线冲突...")
                    storyline_check = self.api.check_characters_storyline(
                        json.dumps(characters_data, ensure_ascii=False),
                        main_storyline
                    )
                    if not storyline_check.get("passed", False):
                        print(f"人物与故事主线冲突，问题: {storyline_check.get('issues', [])}")
                        if attempt < max_retries - 1:
                            print(f"正在重新生成人物 ({attempt + 1}/{max_retries})...")
                            continue
                        else:
                            print(f"已达到最大重试次数({max_retries})，使用最后一次生成的人物")

                    # 处理并保存人物数据
                    self.character_manager = CharacterManager()
                    for char_data in characters_data.get("characters", []):
                        # 处理人物数据并添加到管理器
                        character = Character.from_dict(char_data)
                        self.character_manager.add_character(character)
                    
                    # 保存人物管理器
                    self.character_manager.save()
                    print(f"成功保存了{len(self.character_manager.characters)}个人物")
                    break
                    
                except Exception as e:
                    print(f"处理人物卡片时出错: {str(e)}")
                    if attempt < max_retries - 1:
                        print(f"正在重试 ({attempt + 1}/{max_retries})...")
                        continue
                    else:
                        print(f"已达到最大重试次数({max_retries})，生成人物卡片失败")
                        traceback.print_exc()
                        return False
            
            # 生成伏笔
            print("正在生成伏笔设定...")
            self.foreshadowing_manager = ForeshadowingManager()
            
            # 初始化伏笔
            foreshadowing_initialized = self.test_foreshadowing_initialization()
            if foreshadowing_initialized:
                print("伏笔设定生成成功")
            else:
                print("伏笔设定生成失败，将使用默认设置")
                # 创建一些基本的伏笔
                for i in range(3):
                    foreshadowing = Foreshadowing(
                        id=i+1,
                        name=f"默认伏笔{i+1}",
                        description=f"这是一个自动生成的默认伏笔{i+1}",
                        setup_chapter=min(i+1, self.total_chapters),
                        reveal_chapter=min(self.total_chapters - 2 + i, self.total_chapters)
                    )
                    self.foreshadowing_manager.add_foreshadowing(foreshadowing)
                
                # 保存伏笔管理器
                self.foreshadowing_manager.save()
            
            # 更新小说信息
            self.save_novel_info()
            
            print("\n人物和伏笔生成完成！")
            print("您可以使用 'python main.py generate_all --outline-only' 命令继续生成所有章节的大纲。")
            
            return True
        except Exception as e:
            print(f"生成人物和伏笔时出错: {str(e)}")
            traceback.print_exc()
            return False

    def fix_chapter_completion_status(self, chapter_number: Optional[int] = None) -> bool:
        """
        修复章节的完成状态
        
        Args:
            chapter_number: 要修复的章节号，如果为None则修复所有章节
            
        Returns:
            修复是否成功
        """
        try:
            print(f"开始修复章节完成状态...")
            updated_chapters = 0
            
            if chapter_number:
                # 修复单个章节
                chapter = self.outline.get_chapter(chapter_number)
                if chapter:
                    if chapter.is_generated:
                        prev_state = chapter.is_completed
                        chapter.is_completed = True
                        updated_chapters += 1
                        print(f"章节 {chapter_number} 状态: {prev_state} -> True")
                    else:
                        print(f"章节 {chapter_number} 未生成内容，跳过")
            else:
                # 修复所有已生成的章节
                for chapter in self.outline.chapters:
                    if chapter.is_generated:
                        prev_state = chapter.is_completed
                        chapter.is_completed = True
                        if not prev_state:  # 只计算发生变化的章节
                            updated_chapters += 1
                            print(f"章节 {chapter.chapter_number} 状态: {prev_state} -> True")
            
            if updated_chapters > 0:
                # 保存修改
                self.outline.save()
                print(f"已成功修复 {updated_chapters} 个章节的完成状态")
            else:
                print("没有需要修复的章节状态")
                
            return True
        except Exception as e:
            print(f"修复章节状态时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
    def check_chapter(self, chapter_number: int) -> Tuple[bool, Dict[str, Any]]:
        """
        检查指定章节
        
        Args:
            chapter_number: 章节号
            
        Returns:
            (检查是否成功, 检查结果)
        """
        # 实例化检查器
        checker = NovelChecker()
        
        # 加载检查器数据
        success = checker.load_data()
        if not success:
            print("加载检查器数据失败")
            return False, {"passed": False, "issues": [{"type": "checker_init_error", "description": "加载检查器数据失败"}]}
        
        # 为检查器设置总章节数（必需属性）
        checker.total_chapters = self.total_chapters
        
        # 调用检查器的check_chapter方法
        success, result = checker.check_chapter(chapter_number, api=self.api)
        
        # 如果检查成功且通过了验证，应用更新（更新人物状态和战力）
        if success and result and result.get("passed", True):
            print(f"\n【开始更新】第{chapter_number}章人物状态和战力...")
            # 直接应用章节更新（会更新current_status和current_power）
            update_success = checker.apply_chapter_updates(chapter_number, result)
            if update_success:
                character_updates = result.get("character_updates", [])
                character_names = [update.get("name") for update in character_updates if update.get("name")]
                print(f"✅ 第{chapter_number}章更新完成：{len(character_updates)}个角色 {', '.join(character_names)}")
            else:
                print(f"❌ 第{chapter_number}章人物状态和战力更新失败")
        
        return success, result

    def fix_chapter_content(self, chapter_number: int, issues: List[Dict[str, Any]], issue_type: str) -> bool:
        """
        修复章节内容中的特定问题

        Args:
            chapter_number: 章节号
            issues: 需要修复的问题列表
            issue_type: 问题类型（如"背景冲突"、"人物冲突"、"大纲冲突"等）

        Returns:
            修复是否成功
        """
        try:
            import re  # 确保在使用re模块之前导入它

            # 获取章节内容
            chapter = read_chapter(chapter_number)
            if not chapter:
                print(f"未找到第{chapter_number}章内容")
                return False

            # 获取章节大纲
            chapter_outline = self.outline.get_chapter(chapter_number)
            if not chapter_outline:
                print(f"未找到第{chapter_number}章大纲")
                return False

            # 提取问题修复建议
            fix_suggestions = []
            for issue in issues:
                suggestion = issue.get("suggestion", "")
                description = issue.get("description", "")
                location = issue.get("location", "")
                
                # 构建详细的修复建议
                fix_detail = ""
                if description:
                    fix_detail += f"问题：{description}\n"
                if location:
                    fix_detail += f"位置：{location}\n"
                if suggestion:
                    fix_detail += f"建议：{suggestion}\n"
                
                if fix_detail:
                    fix_suggestions.append(fix_detail)

            # 如果没有提取到具体建议，使用通用修复建议
            if not fix_suggestions:
                if "背景" in issue_type:
                    fix_suggestions = [
                        "确保章节内容中的世界观、设定与背景信息保持一致",
                        "移除或修改与已有背景设定冲突的内容",
                        "修正可能存在的设定错误或不一致之处"
                    ]
                elif "人物" in issue_type:
                    fix_suggestions = [
                        "确保人物行为、能力与已有人物设定一致",
                        "修正人物关系、动机或对话中的冲突或矛盾",
                        "调整人物能力表现，确保符合设定范围"
                    ]
                elif "大纲" in issue_type:
                    fix_suggestions = [
                        "确保章节情节与大纲保持一致",
                        "修正与伏笔、关键情节点相关的冲突",
                        "调整剧情走向，确保符合大纲规划"
                    ]
                else:
                    fix_suggestions = [
                        f"修正{issue_type}中存在的问题",
                        "确保内容的逻辑性和连贯性",
                        "修改或优化有问题的段落"
                    ]

            # 修复章节内容
            print(f"正在修复第{chapter_number}章中的{issue_type}问题...")
            print(f"修复建议: {fix_suggestions}")
            
            # 使用API修复内容
            fixed_content = self.api.fix_chapter_content(
                chapter["content"],
                fix_suggestions,
                chapter_outline.content,
                str(self.background),
                json.dumps(self.character_manager.to_dict(), ensure_ascii=False),
                json.dumps(self.foreshadowing_manager.to_dict(), ensure_ascii=False),
                issue_type
            )

            if not fixed_content:
                print(f"修复{issue_type}问题失败")
                return False

            # 清理结果，移除可能的标记和注释
            # 移除内容开头的元数据和说明
            fixed_content = re.sub(r'^（[^）]*）[\n\r]+', '', fixed_content)
            fixed_content = re.sub(r'^【[^】]*】[\n\r]+', '', fixed_content)
            fixed_content = re.sub(r'^---[\n\r]+', '', fixed_content)

            # 移除可能存在的元数据标记
            fixed_content = re.sub(r'\*\*[^*]+\*\*', '', fixed_content)  # 移除类似 "**高潮（星陨异变）**" 的标记
            fixed_content = re.sub(r'（[^）]*部分[^）]*）', '', fixed_content)  # 移除类似 "（高潮部分）" 的标记
            fixed_content = re.sub(r'（[^）]*修复[^）]*）', '', fixed_content)  # 移除类似 "（修复版）" 的标记
            fixed_content = re.sub(r'_(.+?)_', r'\1', fixed_content)  # 移除下划线标记

            # 移除修复标记
            fixed_content = re.sub(r'-{3,}[\n\r]+', '', fixed_content)  # 移除分隔线
            fixed_content = re.sub(r'【[^】]*修复[^】]*】[\n\r]+', '', fixed_content)  # 移除类似 "【修复内容】" 的标记
            fixed_content = re.sub(r'【[^】]*调整[^】]*】[\n\r]+', '', fixed_content)  # 移除类似 "【调整内容】" 的标记
            fixed_content = re.sub(r'（[^）]*修复[^）]*）[\n\r]+', '', fixed_content)  # 移除类似 "（修复内容开始）" 的标记
            fixed_content = re.sub(r'（[^）]*调整[^）]*）[\n\r]+', '', fixed_content)  # 移除类似 "（调整内容开始）" 的标记

            # 提取章节标题
            title = chapter["title"]
            if not title or title.strip() == "":
                # 尝试从内容中提取标题
                title_match = re.search(r'^(第.+?章\s*.+?)[\n\r]', fixed_content)
                if title_match and len(title_match.group(1).strip().split(' ', 1)) > 1:
                    title = title_match.group(1).strip()
                else:
                    title = f"第{chapter_number}章"

            # 检查内容是否完整
            if fixed_content.strip():
                last_char = fixed_content.strip()[-1]
                if last_char not in '。！？.!?"\'》）)':
                    print(f"警告：修复后的第{chapter_number}章内容可能不完整，最后一个字符是：{last_char}")
                    fixed_content = fixed_content.strip() + "。"  # 添加一个结束符号

            # 保存修复后的内容
            save_chapter(chapter_number, title, fixed_content)
            print(f"第{chapter_number}章中的{issue_type}问题已修复并保存")

            return True
            
        except Exception as e:
            print(f"修复章节内容时出错: {str(e)}")
            traceback.print_exc()
            return False

    def fix_outline(self) -> bool:
        """
        修复大纲文件中未成功生成的章节大纲
        
        识别格式为 {"content": "{\"story_title\": \"未命名故事\", \"outlines\": []}"} 的章节，
        从故事主线中生成对应的大纲，并放到大纲json中正确的位置
        
        Returns:
            修复是否成功
        """
        try:
            # 确保大纲已加载
            if not self.outline:
                self.load_project()
                if not self.outline:
                    print("无法加载项目，请确保项目已初始化")
                    return False
            
            # 从文件中加载故事主线
            from utils.file_manager import load_main_storyline
            
            # 获取故事主线
            main_storyline_data = load_main_storyline(self.total_chapters)
            if not main_storyline_data:
                print("未找到故事主线，无法修复大纲")
                return False
            
            # 解析主线数据
            try:
                # 如果返回的是字符串，则解析为JSON对象
                if isinstance(main_storyline_data, str):
                    storyline_data = json.loads(main_storyline_data)
                else:
                    # 如果已经是字典，直接使用
                    storyline_data = main_storyline_data
                    
                total_outlines = len(storyline_data.get("outlines", []))
                if total_outlines == 0:
                    print("故事主线中没有章节大纲数据，需要先生成故事主线")
                    return False
                    
                print(f"故事主线中包含 {total_outlines} 个章节大纲")
            except json.JSONDecodeError:
                print("解析故事主线失败，格式可能不正确")
                return False
            
            # 检查每个章节是否有内容为特定格式的
            empty_chapters = []
            default_content = "{\"story_title\": \"未命名故事\", \"outlines\": []}"
            
            for chapter_number in range(1, self.total_chapters + 1):
                chapter = self.outline.get_chapter(chapter_number)
                
                # 如果章节不存在，标记为需要修复
                if not chapter:
                    print(f"章节 {chapter_number} 不存在，需要创建")
                    empty_chapters.append(chapter_number)
                    continue
                
                # 检查内容是否为默认空内容
                if chapter.content == default_content:
                    print(f"章节 {chapter_number} 内容为默认空内容，需要修复")
                    empty_chapters.append(chapter_number)
                    continue
                
                # 检查内容是否为空或极短
                if not chapter.content or len(chapter.content) < 50:
                    print(f"章节 {chapter_number} 内容过短或为空，需要修复")
                    empty_chapters.append(chapter_number)
                    continue
                
                # 尝试解析内容，检查是否为有效的JSON
                try:
                    if chapter.content.strip().startswith('{') and chapter.content.strip().endswith('}'):
                        outline_data = json.loads(chapter.content)
                        # 检查是否为空的JSON结构
                        if not outline_data.get("chapter_summary") and not outline_data.get("key_points"):
                            print(f"章节 {chapter_number} 内容为空JSON结构，需要修复")
                            empty_chapters.append(chapter_number)
                except Exception:
                    # 如果解析失败，但不是空的默认内容，认为是有效的非JSON内容
                    pass
            
            print(f"共发现 {len(empty_chapters)} 个需要修复的章节大纲")
            
            # 如果没有需要修复的章节，直接返回成功
            if not empty_chapters:
                print("所有章节大纲都已正确生成，无需修复")
                return True
            
            # 修复每个空章节
            fixed_count = 0
            for chapter_number in empty_chapters:
                print(f"\n开始修复第 {chapter_number} 章大纲...")
                
                # 使用现有的章节大纲生成方法
                success = self.generate_chapter_outline(chapter_number)
                
                if success:
                    fixed_count += 1
                    print(f"第 {chapter_number} 章大纲修复成功")
                else:
                    print(f"第 {chapter_number} 章大纲修复失败")
            
            print(f"\n大纲修复完成，成功修复 {fixed_count}/{len(empty_chapters)} 个章节大纲")
            return fixed_count > 0
            
        except Exception as e:
            print(f"修复大纲时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def _generate_chapter_content_segmented(self, chapter_number: int) -> bool:
        """
        使用分段生成方式生成章节内容（开端、发展、高潮、结尾四段）
        
        Args:
            chapter_number: 章节号
            
        Returns:
            生成是否成功
        """
        try:
            print(f"使用分段生成方式生成第{chapter_number}章内容...")
            
            # 获取章节大纲
            chapter_outline = self.outline.get_chapter(chapter_number)
            if not chapter_outline:
                print(f"未找到第{chapter_number}章大纲")
                return False
                
            # 获取前几章摘要
            previous_summary = self.outline.get_previous_chapters_summary(chapter_number)
            
            # 如果是第一章，无需获取前一章内容
            previous_content = None
            if chapter_number > 1:
                # 获取上一章内容的结尾部分（约500字）
                prev_chapter = read_chapter(chapter_number - 1)
                if prev_chapter and "content" in prev_chapter:
                    # 提取最后500个字符作为前一章结尾内容
                    content = prev_chapter["content"]
                    if len(content) > 500:
                        previous_content = content[-500:]
                    else:
                        previous_content = content
            
            # 获取人物信息和背景设定
            characters_json = json.dumps(self.character_manager.to_dict(), ensure_ascii=False)
            foreshadowing_json = json.dumps(self.foreshadowing_manager.to_dict(), ensure_ascii=False)
            
            # 生成四个段落
            segments = []
            segment_types = ["开端", "发展", "高潮", "结尾"]
            
            # 用于传递给下一个段落的内容
            cumulative_content = ""
            
            for i, segment_type in enumerate(segment_types):
                print(f"正在生成{segment_type}部分...")
                
                # 第一个段落（开端）使用前一章结尾作为上下文
                # 后续段落使用累积的内容作为上下文
                context_content = previous_content if i == 0 else cumulative_content
                
                # 多次尝试生成，确保达到字数要求
                max_retries = 3
                segment_content = None
                
                for retry in range(max_retries):
                    segment_content = self.api.generate_chapter_segment(
                        segment_type,
                        chapter_number,
                        chapter_outline.content,
                        str(self.background),
                        characters_json,
                        foreshadowing_json,
                        context_content,
                        generation_mode="outline"
                    )
                    
                    if not segment_content:
                        print(f"第{retry+1}次尝试生成{segment_type}段落失败")
                        continue
                        
                    # 检查字数（简单估算中文字数）
                    clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', segment_content)
                    word_count = len(clean_text)
                    
                    # 确定最小字数要求
                    min_words = 1500  # 默认最小字数
                    if segment_type == "发展":
                        min_words = 2000
                    elif segment_type == "结尾" and chapter_number == self.total_chapters:
                        min_words = 2000
                        
                    if word_count >= min_words:
                        print(f"{segment_type}段落字数: {word_count}字，符合要求")
                        break
                    else:
                        print(f"{segment_type}段落字数不足: {word_count}字 < {min_words}字，重试...")
                
                if not segment_content:
                    print(f"生成{segment_type}段落失败，放弃生成")
                    return False
                
                segments.append(segment_content)
                
                # 累积内容（用于后续段落的上下文）
                # 仅保留最近生成的2000字左右，避免上下文过长
                if cumulative_content:
                    cumulative_content += "\n\n" + segment_content
                    if len(cumulative_content) > 2000:
                        cumulative_content = cumulative_content[-2000:]
                else:
                    cumulative_content = segment_content
            
            # 处理章节标题
            # 第一个段落（开端）应该包含标题，提取出来
            title_match = re.search(r'^(第.+?章\s*.+?)[\n\r]', segments[0])
            title = ""
            if title_match:
                title = title_match.group(1).strip()
                # 从第一个段落中移除标题
                segments[0] = re.sub(r'^第.+?章\s*.+?[\n\r]+', '', segments[0], 1)
            else:
                # 尝试提取Markdown格式的标题
                markdown_title_match = re.search(r'#\s*(第.+?章\s*.+?)[\n\r]', segments[0])
                if markdown_title_match:
                    title = markdown_title_match.group(1).strip()
                    # 移除Markdown标题
                    segments[0] = re.sub(r'#\s*第.+?章\s*.+?[\n\r]+', '', segments[0], 1)
                else:
                    # 使用大纲标题或默认标题
                    outline_title = chapter_outline.title if chapter_outline else ""
                    if outline_title and outline_title.strip() and not outline_title.strip() == f"第{chapter_number}章":
                        title = outline_title
                    else:
                        title = f"第{chapter_number}章 未命名章节{chapter_number}"
            
            # 合并所有段落
            content = title + "\n\n"
            for segment in segments:
                # 移除可能的额外标题
                segment = re.sub(r'^第.+?章\s*.+?[\n\r]+', '', segment, 1)
                segment = re.sub(r'#\s*第.+?章\s*.+?[\n\r]+', '', segment, 1)
                content += segment.strip() + "\n\n"
            
            # 清理内容
            # 移除可能出现的连续空行
            content = re.sub(r'[\n\r]{3,}', '\n\n', content)
            # 移除段落间可能的元数据标记
            content = re.sub(r'【.+?】', '', content)
            content = re.sub(r'\*\*[^*]+\*\*', '', content)
            
            # 保存章节内容
            success = save_chapter(chapter_number, title, content)
            if not success:
                print(f"保存第{chapter_number}章内容失败")
                return False
                
            # 更新章节状态
            chapter_outline.is_generated = True
            
            # 验证章节内容并更新人物状态
            check_success, check_result = self.check_chapter(chapter_number)
            
            if check_success and check_result.get('passed', True):
                print(f"第{chapter_number}章验证通过")
                
                # 创建NovelChecker实例并执行apply_chapter_updates
                from novel.checker import NovelChecker
                checker = NovelChecker()
                if checker.load_data():
                    # 设置总章节数（必要属性）
                    checker.total_chapters = self.total_chapters
                    print(f"\n【开始更新】第{chapter_number}章人物状态和战力(分段生成)...")
                    # 应用章节更新（更新人物状态和战力）
                    update_success = checker.apply_chapter_updates(chapter_number, check_result)
                    if update_success:
                        character_updates = check_result.get("character_updates", [])
                        character_names = [update.get("name") for update in character_updates if update.get("name")]
                        print(f"✅ 第{chapter_number}章(分段生成)更新完成：{len(character_updates)}个角色 {', '.join(character_names)}")
                    else:
                        print(f"❌ 第{chapter_number}章(分段生成)人物状态和战力更新失败")
                else:
                    print(f"警告：无法加载检查器数据，人物状态和战力可能未更新")
                
                # 更新完成状态
                chapter_outline.is_completed = True
                self.outline.save()
            else:
                print(f"第{chapter_number}章验证失败，但仍标记为已生成: {check_result.get('issues', [])}")
                # 即使验证失败也将章节标记为已完成，以便后续处理
                chapter_outline.is_completed = True
                self.outline.save()
            
            print(f"第{chapter_number}章已通过分段生成方式生成完毕")
            return True
            
        except Exception as e:
            print(f"分段生成章节内容时出错: {str(e)}")
            traceback.print_exc()
            return False
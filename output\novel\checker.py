"""
小说检查模块
"""

import os
import json
import re
from typing import Dict, Any, List, Optional, Tuple

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import OUTPUT_DIR, DEFAULT_CHAPTER_LENGTH
from llm.deepseek_all import DeepSeekAllAPI
from models.background import Background
from models.outline import NovelOutline, ChapterOutline
from models.character import CharacterManager, Character
from models.foreshadowing import ForeshadowingManager, Foreshadowing
from utils.file_manager import read_chapter, load_style_guide
from utils.json_helper import parse_json_safely

class NovelChecker:
    """小说检查器"""
    
    def __init__(self):
        """初始化小说检查器"""
        self.api = DeepSeekAllAPI()
        self.background = None
        self.outline = None
        self.character_manager = None
        self.foreshadowing_manager = None
        self.metadata = {}
        self.total_chapters = 0
    
    def load_data(self) -> bool:
        """
        加载小说数据
        
        Returns:
            加载是否成功
        """
        try:
            # 加载背景设定
            self.background = Background.load()
            if not self.background:
                print("未找到背景设定")
                return False
            
            # 加载大纲
            self.outline = NovelOutline.load()
            if not self.outline:
                print("未找到大纲")
                return False
            
            # 加载人物卡片
            self.character_manager = CharacterManager.load()
            if not self.character_manager:
                print("未找到人物卡片")
                return False
            
            # 加载伏笔管理
            self.foreshadowing_manager = ForeshadowingManager.load()
            if not self.foreshadowing_manager:
                print("未找到伏笔管理")
                return False
            
            print("小说数据加载成功")
            return True
        except Exception as e:
            print(f"加载小说数据时出错: {str(e)}")
            return False
    
    def check_chapter(self, chapter_number: int, api: Optional[Any] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        检查指定章节
        
        Args:
            chapter_number: 章节号
            api: 可选的API实例
            
        Returns:
            (检查是否成功, 检查结果)
        """
        if not api:
            api = self.api

        # 确保章节号有效
        if chapter_number < 1 or chapter_number > self.total_chapters:
            print(f"无效的章节号: {chapter_number}")
            return False, {"passed": False, "issues": [{"type": "invalid_chapter", "description": "无效的章节号"}]}

        # 检查章节文件是否存在
        # 使用read_chapter函数检查章节是否存在，该函数会通过章节索引查找文件
        chapter_data = read_chapter(chapter_number)
        if not chapter_data:
            print(f"章节{chapter_number}不存在或无法读取")
            return False, {"passed": False, "issues": [{"type": "missing_file", "description": "章节文件不存在或无法读取"}]}

        try:
            # 获取章节内容文本
            chapter_text = chapter_data.get("content", "")
            if not chapter_text:
                print(f"章节{chapter_number}内容为空")
                return False, {"passed": False, "issues": [{"type": "empty_content", "description": "章节内容为空"}]}
            
            # 获取章节大纲
            chapter_outline = self.outline.get_chapter(chapter_number)
            if not chapter_outline:
                print(f"无法获取章节大纲: chapter_{chapter_number}")
                return False, {"passed": False, "issues": [{"type": "missing_outline", "description": "无法获取章节大纲"}]}

            # 获取未回收的伏笔
            unrevealed_foreshadowings = json.dumps(self.foreshadowing_manager.to_dict(), ensure_ascii=False)

            # 加载风格指南
            style_guide = load_style_guide()
            if not style_guide:
                print("警告：无法加载风格指南，尝试直接从输出目录加载...")
                style_path = os.path.join(OUTPUT_DIR, "style.json")
                try:
                    if os.path.exists(style_path):
                        with open(style_path, 'r', encoding='utf-8') as f:
                            style_guide = f.read()
                    else:
                        # 尝试加载TXT格式的风格指南
                        style_path = os.path.join(OUTPUT_DIR, "style_guide.txt")
                        if os.path.exists(style_path):
                            with open(style_path, 'r', encoding='utf-8') as f:
                                style_guide = f.read()
                        else:
                            print("警告：所有风格指南文件路径都不存在")
                            style_guide = ""
                except Exception as e:
                    print(f"读取风格指南时出错: {str(e)}")
                    style_guide = ""
                
            # 获取所有人物信息
            characters_info = json.dumps(self.character_manager.to_dict(), ensure_ascii=False)

            print(f"正在检查第{chapter_number}章内容...")
            print("1. 检查章节内容是否符合大纲要求...")
            
            # 检查章节与大纲一致性和伏笔处理
            check_result_json = api.check_chapter_outline(
                chapter_text, 
                chapter_outline.content,
                unrevealed_foreshadowings
            )
            
            if not check_result_json:
                print(f"检查第{chapter_number}章内容失败")
                return False, {"passed": False, "issues": [{"type": "check_failed", "description": "检查章节内容失败"}]}

            # 解析检查结果
            try:
                check_result = parse_json_safely(check_result_json, "chapter_check")
                if not check_result:
                    print(f"无法解析章节检查结果，使用默认结果")
                    check_result = {"passed": False, "issues": [{"type": "解析错误", "description": "无法解析章节检查结果"}]}
                    
                print("2. 检查章节是否与风格指南冲突...")
                style_result = api.check_chapter_style(chapter_text, self.background.genre)
                
                print("3. 检查章节是否与人物设定冲突...")
                character_result = api.check_chapter_characters(chapter_text, characters_info)
                
                print("4. 检查章节是否与背景设定冲突...")
                background_result = api.check_chapter_background(chapter_text, str(self.background))
                
                print("5. 检查章节是否与伏笔设定冲突...")
                
                # 合并检查结果
                if not style_result.get("passed", True):
                    check_result["passed"] = False
                    check_result["issues"] = check_result.get("issues", []) + style_result.get("issues", [])
                
                if not character_result.get("passed", True):
                    check_result["passed"] = False
                    check_result["issues"] = check_result.get("issues", []) + character_result.get("issues", [])
                
                if not background_result.get("passed", True):
                    check_result["passed"] = False
                    check_result["issues"] = check_result.get("issues", []) + background_result.get("issues", [])
                
                # 添加人物更新信息
                check_result["character_updates"] = character_result.get("character_updates", [])
                check_result["appearing_character_names"] = character_result.get("appearing_character_names", [])
                
                # 检查是否有任何问题，如果有，则无论严重程度如何，都设置passed=false
                if check_result.get("issues") and len(check_result.get("issues", [])) > 0:
                    check_result["passed"] = False
                
                print("检查完成！")
                if check_result.get("passed", False):
                    print("章节通过所有检查！")
                    
                    # 在检查通过后，直接调用apply_chapter_updates更新状态
                    print("正在更新章节状态、人物状态和伏笔状态...")
                    self.apply_chapter_updates(chapter_number, check_result)
                else:
                    print(f"检测到{len(check_result.get('issues', []))}个问题")
                
                return True, check_result
            except Exception as e:
                print(f"处理检查结果时出错: {str(e)}")
                import traceback
                traceback.print_exc()
                check_result = {"passed": False, "issues": [{"type": "处理错误", "description": f"处理检查结果时出错: {str(e)}"}]}
            return True, check_result
        except Exception as e:
            print(f"检查章节时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False, {"passed": False, "issues": [{"type": "error", "description": f"检查章节时出错: {str(e)}"}]}
    
    def apply_chapter_updates(self, chapter_number: int, check_result: Dict[str, Any]) -> bool:
        """
        应用章节检查更新
        
        Args:
            chapter_number: 章节号
            check_result: 检查结果
            
        Returns:
            应用是否成功
        """
        try:
            if not check_result.get("passed", False):
                print(f"第{chapter_number}章检查未通过，不应用更新")
                return False
            
            # 1. 只更新现有人物状态，不创建新人物
            character_updates = check_result.get("character_updates", [])
            updated_characters_count = 0
            
            for char_update in character_updates:
                name = char_update.get("name", "")
                updates = char_update.get("updates", {})
                
                character = self.character_manager.get_character(name)
                if character:
                    current_status = updates.get("current_status")
                    current_power = updates.get("current_power")
                    
                    if current_status or current_power:
                        # 记录更新前的状态和战力
                        old_status = character.current_status
                        old_power = character.current_power
                        
                        # 使用Character的update_status方法同时更新status和power
                        character.update_status(
                            new_status=current_status if current_status else character.current_status,
                            new_power=current_power if current_power else character.current_power
                        )
                        updated_characters_count += 1
                        
                        # 构建更新日志信息
                        update_details = []
                        if current_status and current_status != old_status:
                            update_details.append(f"状态: {old_status} -> {current_status}")
                        if current_power and current_power != old_power:
                            update_details.append(f"战力: {old_power} -> {current_power}")
                        
                        # 打印详细的更新日志
                        if update_details:
                            print(f"【角色状态更新】{name}: {', '.join(update_details)}")
                        else:
                            print(f"【角色状态更新】{name}: 状态与战力未变化")
                else:
                    print(f"警告：未找到人物 {name}，无法更新其状态")
            
            # 保存人物管理器的更改
            if updated_characters_count > 0:
                self.character_manager.save()
                print(f"已更新{updated_characters_count}个人物状态")
            
            # 2. 只更新现有伏笔状态，不创建新伏笔
            foreshadowing_updates = check_result.get("foreshadowing_updates", [])
            updated_foreshadowing_count = 0
            
            for fs_update in foreshadowing_updates:
                id = fs_update.get("id", "")
                status = fs_update.get("status", "")
                
                if id and status:
                    foreshadowing = self.foreshadowing_manager.get_foreshadowing(id)
                    if foreshadowing:
                        old_status = foreshadowing.status
                        self.foreshadowing_manager.update_foreshadowing_status(id, status)
                        updated_foreshadowing_count += 1
                        print(f"已更新伏笔 {id} 的状态: {old_status} -> {status}")
                    else:
                        print(f"警告：未找到伏笔 {id}，无法更新其状态")
                
            # 保存伏笔管理器的更改
            if updated_foreshadowing_count > 0:
                self.foreshadowing_manager.save()
                print(f"已更新{updated_foreshadowing_count}个伏笔状态")
            
            # 更新大纲状态
            chapter_outline = self.outline.get_chapter(chapter_number)
            if chapter_outline:
                chapter_outline.is_completed = True
                self.outline.add_chapter(chapter_outline)
                self.outline.save()
                print(f"已更新第{chapter_number}章状态为已完成")
            
            return True
        except Exception as e:
            print(f"应用章节更新时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def check_all_chapters(self) -> bool:
        """
        检查所有已生成的章节
        
        Returns:
            全部检查是否成功
        """
        try:
            # 检查是否已加载数据
            if not all([self.background, self.outline, self.character_manager, self.foreshadowing_manager]):
                if not self.load_data():
                    return False
            
            # 获取所有已生成的章节
            generated_chapters = [
                ch.chapter_number for ch in self.outline.chapters
                if ch.is_generated and not ch.is_completed
            ]
            
            if not generated_chapters:
                print("没有需要检查的章节")
                return True
            
            print(f"需要检查的章节数: {len(generated_chapters)}")
            
            all_passed = True
            for chapter_number in sorted(generated_chapters):
                print(f"\n正在检查第{chapter_number}章...")
                success, check_result = self.check_chapter(chapter_number)
                
                if not success:
                    print(f"检查第{chapter_number}章失败")
                    all_passed = False
                    continue
                
                if not check_result or not check_result.get("passed", False):
                    print(f"第{chapter_number}章检查未通过")
                    all_passed = False
                    continue
                
                # 注意：状态更新已在check_chapter方法中处理
            
            if all_passed:
                print("\n所有章节检查通过")
            else:
                print("\n部分章节检查未通过，请查看详细信息")
            
            return all_passed
        except Exception as e:
            print(f"检查所有章节时出错: {str(e)}")
            return False
    
    def check_consistency(self) -> Dict[str, List[str]]:
        """
        检查全局一致性问题
        
        Returns:
            各类型的一致性问题列表
        """
        try:
            # 检查是否已加载数据
            if not all([self.background, self.outline, self.character_manager, self.foreshadowing_manager]):
                if not self.load_data():
                    return {
                        "character_issues": ["无法加载数据"],
                        "plot_issues": ["无法加载数据"],
                        "foreshadowing_issues": ["无法加载数据"],
                        "other_issues": ["无法加载数据"]
                    }
            
            issues = {
                "character_issues": [],
                "plot_issues": [],
                "foreshadowing_issues": [],
                "other_issues": []
            }
            
            # 检查人物一致性
            for character in self.character_manager.characters:
                if not character.current_status:
                    issues["character_issues"].append(f"角色 {character.name} 没有当前状态")
            
            # 检查伏笔一致性
            for foreshadowing in self.foreshadowing_manager.foreshadowings:
                # 检查已生成章节的伏笔状态
                completed_chapters = self.outline.get_completed_chapters_count()
                
                if foreshadowing.plant_chapter <= completed_chapters and foreshadowing.status == "未埋下":
                    issues["foreshadowing_issues"].append(
                        f"伏笔 {foreshadowing.id} 应该在第{foreshadowing.plant_chapter}章埋下，但状态仍为'未埋下'"
                    )
                
                if foreshadowing.reveal_chapter <= completed_chapters and foreshadowing.status != "已回收":
                    issues["foreshadowing_issues"].append(
                        f"伏笔 {foreshadowing.id} 应该在第{foreshadowing.reveal_chapter}章回收，但状态为'{foreshadowing.status}'"
                    )
            
            return issues
        except Exception as e:
            print(f"检查一致性时出错: {str(e)}")
            return {
                "character_issues": [f"检查出错: {str(e)}"],
                "plot_issues": [],
                "foreshadowing_issues": [],
                "other_issues": []
            }
    
    def generate_summary(self) -> str:
        """
        生成小说摘要
        
        Returns:
            小说摘要
        """
        try:
            # 检查是否已加载数据
            if not all([self.background, self.outline, self.character_manager, self.foreshadowing_manager]):
                if not self.load_data():
                    return "无法加载数据，无法生成摘要"
            
            # 使用Gemini生成小说摘要
            from utils.file_manager import load_novel_info
            novel_info = load_novel_info() or {"title": "未命名小说", "author": "佚名", "genre": "未知"}
            
            # 构建提示词
            prompt = f"""
            请为以下小说生成一个简洁的摘要（不超过500字）：
            
            标题：{novel_info.get('title', '未命名小说')}
            作者：{novel_info.get('author', '佚名')}
            流派：{novel_info.get('genre', '未知')}
            
            背景设定：
            {str(self.background)[:1000]}...
            
            主要角色：
            {str(self.character_manager)[:1000]}...
            
            已完成章节数：{self.outline.get_completed_chapters_count()}
            总章节数：{self.outline.total_chapters}
            
            请包含小说的核心冲突、主要情节和主角成长轨迹，突出其男频网络小说的特点。请不要添加任何未在原小说中出现的内容。
            """
            
            summary = self.api.generate_content(prompt)
            if not summary:
                return "生成摘要失败"
            
            return summary
        except Exception as e:
            print(f"生成摘要时出错: {str(e)}")
            return f"生成摘要时出错: {str(e)}" 
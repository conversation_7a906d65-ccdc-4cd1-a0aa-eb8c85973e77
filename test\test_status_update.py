#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
小说状态更新测试脚本
用于验证章节完成后是否正确更新章节状态、人物状态和伏笔状态
"""

import os
import sys
import json
from typing import Dict, Any, List, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from novel.generator import NovelGenerator
from novel.checker import NovelChecker
from models.character import CharacterManager
from models.foreshadowing import ForeshadowingManager
from models.outline import NovelOutline
from config import OUTPUT_DIR

def test_chapter_completion_status():
    """测试章节完成状态更新"""
    print("\n=== 测试章节完成状态更新 ===")
    generator = NovelGenerator()
    
    # 加载项目
    if not generator.load_project():
        print("加载项目失败")
        return False
    
    # 修复章节状态
    result = generator.fix_chapter_completion_status()
    
    # 重新加载大纲，确认状态已保存
    outline = NovelOutline.load()
    if not outline:
        print("重新加载大纲失败")
        return False
    
    # 检查章节状态
    completed = outline.get_completed_chapters_count()
    generated = outline.get_generated_chapters_count()
    print(f"完成的章节数: {completed}/{generated} (已完成/已生成)")
    
    return completed > 0

def test_character_status_update():
    """测试人物状态更新"""
    print("\n=== 测试人物状态更新 ===")
    checker = NovelChecker()
    
    # 加载数据
    if not checker.load_data():
        print("加载检查器数据失败")
        return False
    
    # 打印人物状态
    character_manager = checker.character_manager
    if not character_manager or not character_manager.characters:
        print("未找到人物数据")
        return False
    
    print(f"已加载 {len(character_manager.characters)} 个人物")
    for character in character_manager.characters:
        print(f"人物: {character.name}, 当前状态: {character.current_status}")
    
    return True

def test_foreshadowing_status_update():
    """测试伏笔状态更新"""
    print("\n=== 测试伏笔状态更新 ===")
    checker = NovelChecker()
    
    # 加载数据
    if not checker.load_data():
        print("加载检查器数据失败")
        return False
    
    # 打印伏笔状态
    foreshadowing_manager = checker.foreshadowing_manager
    if not foreshadowing_manager or not foreshadowing_manager.foreshadowings:
        print("未找到伏笔数据")
        return False
    
    print(f"已加载 {len(foreshadowing_manager.foreshadowings)} 个伏笔")
    for foreshadowing in foreshadowing_manager.foreshadowings:
        print(f"伏笔ID: {foreshadowing.id}, 内容: {foreshadowing.content[:30]}..., 当前状态: {foreshadowing.status}")
        print(f"  计划埋下章节: {foreshadowing.plant_chapter}, 计划回收章节: {foreshadowing.reveal_chapter}")
    
    return True

def main():
    """主函数"""
    print("开始测试章节完成后的状态更新...")
    
    # 测试章节完成状态
    chapter_status_ok = test_chapter_completion_status()
    
    # 测试人物状态
    character_status_ok = test_character_status_update()
    
    # 测试伏笔状态
    foreshadowing_status_ok = test_foreshadowing_status_update()
    
    # 汇总结果
    print("\n=== 测试结果汇总 ===")
    print(f"章节完成状态: {'成功' if chapter_status_ok else '失败'}")
    print(f"人物状态更新: {'成功' if character_status_ok else '失败'}")
    print(f"伏笔状态更新: {'成功' if foreshadowing_status_ok else '失败'}")
    
    if chapter_status_ok and character_status_ok and foreshadowing_status_ok:
        print("\n所有测试通过！系统正确处理章节完成后的状态更新。")
    else:
        print("\n测试失败！请检查相关代码。")

if __name__ == "__main__":
    main() 
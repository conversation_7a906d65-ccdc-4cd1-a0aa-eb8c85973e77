#!/usr/bin/env python3
"""
测试main_storyline.json文件的解析
"""

import os
import json
import sys
from utils.json_helper import parse_json_safely, fix_json, extract_nested_json

def test_storyline_parse():
    """测试解析main_storyline.json文件"""
    
    # 读取main_storyline.json文件
    file_path = os.path.join("output", "main_storyline.json")
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在: {file_path}")
        return False
    
    print(f"正在读取文件: {file_path}")
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 1. 尝试使用标准JSON解析
    print("\n1. 尝试标准JSON解析:")
    try:
        data = json.loads(content)
        print("✓ 标准JSON解析成功!")
        print(f"  故事标题: {data.get('story_title', '未找到')}")
        print(f"  章节大纲类型: {type(data.get('chapter_outlines', ''))}")
        
        # 检查chapter_outlines是否是字符串形式的JSON
        chapter_outlines = data.get("chapter_outlines", "")
        if isinstance(chapter_outlines, str):
            print("  章节大纲是字符串形式，尝试解析内部JSON...")
            try:
                outlines_data = json.loads(chapter_outlines)
                print(f"  ✓ 章节大纲内部JSON解析成功! 共{len(outlines_data)}章")
            except json.JSONDecodeError as e:
                print(f"  ✗ 章节大纲内部JSON解析失败: {str(e)}")
                print(f"  错误位置: 行 {e.lineno}, 列 {e.colno}, 字符位置 {e.pos}")
                # 显示错误附近的内容
                error_context = chapter_outlines[max(0, e.pos-50):min(len(chapter_outlines), e.pos+50)]
                print(f"  错误上下文: ...{error_context}...")
    except json.JSONDecodeError as e:
        print(f"✗ 标准JSON解析失败: {str(e)}")
        print(f"  错误位置: 行 {e.lineno}, 列 {e.colno}, 字符位置 {e.pos}")
        # 显示错误附近的内容
        error_context = content[max(0, e.pos-50):min(len(content), e.pos+50)]
        print(f"  错误上下文: ...{error_context}...")
    
    # 2. 使用修复函数
    print("\n2. 尝试使用fix_json修复:")
    fixed_content = fix_json(content)
    try:
        data = json.loads(fixed_content)
        print("✓ 修复后的JSON解析成功!")
        print(f"  故事标题: {data.get('story_title', '未找到')}")
    except json.JSONDecodeError as e:
        print(f"✗ 修复后仍然解析失败: {str(e)}")
    
    # 3. 使用安全解析函数
    print("\n3. 使用parse_json_safely安全解析:")
    data = parse_json_safely(content, "main_storyline")
    if data:
        print("✓ 安全解析成功!")
        print(f"  故事标题: {data.get('story_title', '未找到')}")
        
        # 检查chapter_outlines
        chapter_outlines = data.get("chapter_outlines", "")
        if isinstance(chapter_outlines, str):
            print("  章节大纲仍然是字符串形式，尝试安全解析内部JSON...")
            outlines_data = parse_json_safely(chapter_outlines, "chapter_outlines")
            if outlines_data:
                print(f"  ✓ 章节大纲内部JSON安全解析成功! 共{len(outlines_data)}章")
            else:
                print("  ✗ 章节大纲内部JSON安全解析失败")
    else:
        print("✗ 安全解析失败")
    
    # 4. 解析嵌套的JSON
    print("\n4. 专门处理嵌套JSON:")
    data = json.loads(content)  # 这应该能成功，因为外层JSON是正确的
    chapter_outlines = data.get("chapter_outlines", "")
    if isinstance(chapter_outlines, str):
        print("  尝试修复和解析嵌套的chapter_outlines...")
        fixed_outlines = fix_json(chapter_outlines)
        try:
            outlines_data = json.loads(fixed_outlines)
            print(f"  ✓ 修复后的章节大纲解析成功! 共{len(outlines_data)}章")
            
            # 保存修复后的完整JSON
            fixed_data = {
                "story_title": data.get("story_title", ""),
                "chapter_outlines": outlines_data  # 现在这是一个列表而不是字符串
            }
            
            # 将修复后的数据保存到文件
            fixed_file_path = os.path.join("output", "main_storyline_fixed.json")
            with open(fixed_file_path, "w", encoding="utf-8") as f:
                json.dump(fixed_data, f, ensure_ascii=False, indent=2)
            
            print(f"  ✓ 已将修复后的数据保存到: {fixed_file_path}")
            return True
        except json.JSONDecodeError as e:
            print(f"  ✗ 修复后的章节大纲仍然解析失败: {str(e)}")
            print(f"    错误位置: 行 {e.lineno}, 列 {e.colno}, 字符位置 {e.pos}")
            # 显示错误附近的内容
            error_context = fixed_outlines[max(0, e.pos-50):min(len(fixed_outlines), e.pos+50)]
            print(f"    错误上下文: ...{error_context}...")
    
    return False

if __name__ == "__main__":
    print("开始测试main_storyline.json解析...")
    success = test_storyline_parse()
    if success:
        print("\n测试结果: 成功修复并解析main_storyline.json")
        sys.exit(0)
    else:
        print("\n测试结果: 未能完全修复和解析main_storyline.json")
        sys.exit(1) 
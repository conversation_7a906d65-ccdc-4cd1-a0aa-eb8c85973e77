from novel.generator import NovelGenerator

def test_check_chapter():
    try:
        print("初始化NovelGenerator...")
        generator = NovelGenerator()
        
        print("测试check_chapter方法...")
        success, result = generator.check_chapter(1)
        
        print(f"检查结果: 成功={success}")
        if success:
            print(f"通过检查: {result.get('passed', False)}")
            issues = result.get('issues', [])
            print(f"发现问题: {len(issues)}个")
            for i, issue in enumerate(issues):
                print(f"  问题{i+1}: {issue.get('type')} - {issue.get('description')}")
        else:
            print("检查失败")
    except Exception as e:
        import traceback
        print(f"测试过程中出错: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    test_check_chapter() 
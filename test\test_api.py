"""
API调用测试脚本
"""

import sys
import os
from llm.deepseek_all import DeepSeekAllAPI

def test_deepseek_api():
    """测试DeepSeek API调用"""
    print("=== 测试DeepSeek API ===")
    api = DeepSeekAllAPI()
    
    # 测试基本内容生成
    prompt = "请用中文写一个短小的江湖故事，100字左右。"
    print(f"发送提示词: {prompt}")
    
    response = api.generate_content(prompt, temperature=0.7, max_tokens=200)
    
    if response:
        print("\n响应内容:")
        print(response)
        print("\nDeepSeek API测试成功！")
        return True
    else:
        print("\nDeepSeek API测试失败！")
        return False

def test_novel_style():
    """测试小说风格生成"""
    print("\n=== 测试小说风格生成 ===")
    api = DeepSeekAllAPI()
    
    # 测试风格生成
    genre = "玄幻修真"
    print(f"生成{genre}小说风格...")
    
    response = api.generate_novel_style(genre)
    
    if response:
        print("\n响应内容:")
        print(response)
        print("\n小说风格生成测试成功！")
        return True
    else:
        print("\n小说风格生成测试失败！")
        return False

if __name__ == "__main__":
    deepseek_success = test_deepseek_api()
    style_success = test_novel_style()
    
    if deepseek_success and style_success:
        print("\n所有API测试通过！")
        sys.exit(0)
    else:
        print("\n部分API测试失败！")
        sys.exit(1) 
import re

# 读取原文件
with open('novel/generator.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 查找目标位置1 - 分段生成方法中的位置
pattern1 = r'(# 验证章节内容并更新人物状态.*?check_success, check_result = self\.check_chapter\(chapter_number\).*?if check_success and check_result\.get\(\'passed\', True\):\s*?\n\s*?print\(f"第{chapter_number}章验证通过"\)\s*?\n)(\s*?# 更新完成状态)'

replacement1 = r'\1            # 创建NovelChecker实例并执行apply_chapter_updates\n            from novel.checker import NovelChecker\n            checker = NovelChecker()\n            if checker.load_data():\n                # 设置总章节数（必要属性）\n                checker.total_chapters = self.total_chapters\n                # 应用章节更新（更新人物状态和战力）\n                checker.apply_chapter_updates(chapter_number, check_result)\n                print(f"第{chapter_number}章的人物状态和战力已更新")\n            else:\n                print(f"警告：无法加载检查器数据，人物状态和战力可能未更新")\n            \n\2'

# 执行替换1
new_content = re.sub(pattern1, replacement1, content, flags=re.DOTALL)

# 查找目标位置2 - 在所有问题已修复位置
pattern2 = r'(if not fixed:\s*?\n.*?success, new_check_result = self\.check_chapter\(chapter_number\)\s*?\n.*?if success and new_check_result and new_check_result\.get\("passed", False\):\s*?\n\s*?print\("所有问题已修复，章节检查通过"\)\s*?\n)(\s*?fixed = True)'

replacement2 = r'\1            # 创建NovelChecker实例并执行apply_chapter_updates\n            from novel.checker import NovelChecker\n            checker = NovelChecker()\n            if checker.load_data():\n                # 设置总章节数（必要属性）\n                checker.total_chapters = self.total_chapters\n                # 应用章节更新（更新人物状态和战力）\n                checker.apply_chapter_updates(chapter_number, new_check_result)\n                print(f"第{chapter_number}章的人物状态和战力已更新")\n            else:\n                print(f"警告：无法加载检查器数据，人物状态和战力可能未更新")\n            \n\2'

# 执行替换2
new_content = re.sub(pattern2, replacement2, new_content, flags=re.DOTALL)

# 检查是否有变化
if new_content != content:
    # 保存修改后的文件
    with open('novel/generator.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    print("文件已更新，成功添加角色状态更新逻辑")
else:
    print("未找到匹配的代码模式，请检查正则表达式") 
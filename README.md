# 男频网络小说生成系统

一个基于DeepSeek V3大型语言模型的男频网络小说自动生成系统，完成从风格设定到章节生成的全流程。

## 功能特点

- 基于用户指定流派，自动生成完整的小说风格、背景设定
- 自动创建人物卡片，包括角色背景、能力、动机等详细信息
- 规划伏笔系统，确保小说情节连贯和吸引力
- 逐章生成大纲和内容，支持50-60万字的长篇小说
- 自动审核生成内容，确保符合大纲和设定
- 跟踪角色状态变化和伏笔使用
- 支持导出完整小说

## 技术架构

- 使用DeepSeek-R1-0528进行所有创作功能，包括风格生成、背景设定、大纲、人物卡片、内容生成和审核
- 模块化设计，分离不同功能组件
- 完整的状态管理系统，确保小说一致性
- 支持中断和继续生成

## 使用方法

### 安装

1. 克隆仓库:
```
git clone https://github.com/yourusername/novel-generator.git
cd novel-generator
```

2. 安装依赖:
```
pip install -r requirements.txt
```

### 使用

创建新项目:
```
python main.py create "玄幻修真" --title "修仙从今日开始" --author "AI小说家"
```

继续生成:
```
python main.py continue --chapters 5
```

生成特定章节:
```
python main.py chapter 10
```

检查章节:
```
python main.py check 5
```

导出小说:
```
python main.py export --output "我的小说.txt"
```

查看项目状态:
```
python main.py status
```

## 大纲生成模式

系统提供另一种基于直接大纲生成小说的方式，使用`generate_from_outline.py`和`generate_novel_from_outline.py`脚本。

### 基础命令行方式

直接使用`generate_from_outline.py`：

```bash
# 默认使用output_new/outline.json生成小说
python generate_from_outline.py

# 指定大纲文件和输出目录
python generate_from_outline.py path/to/outline.json output_dir

# 生成风格和背景设定
python generate_from_outline.py generate_style 玄幻 output_dir

# 从当前已生成章节继续生成
python generate_from_outline.py continue path/to/outline.json output_dir

```

### 推荐命令行方式

使用更灵活的`generate_novel_from_outline.py`：

```bash
# 从大纲生成章节
python -m generate_novel_from_outline generate_chapters --outline path/to/outline.json --output-dir output_dir

# 生成风格和背景设定
python -m generate_novel_from_outline generate_style --genre 玄幻 --output-dir output_dir

# 从当前已生成章节继续生成
python -m generate_novel_from_outline continue --outline path/to/outline.json --output-dir output_dir

# 导出小说为单个txt文件
python -m generate_novel_from_outline export --output-file "小说名.txt" --output-dir output_dir

# 简化模式（默认执行generate_chapters命令）
python -m generate_novel_from_outline --outline path/to/outline.json --output-dir output_dir
```

参数说明：
- `--outline`/`-o`: 大纲文件的路径（默认：output_new/outline.json）
- `--output-dir`/`-d`: 输出目录（默认：output_new）
- `--genre`/`-g`: 小说流派（默认：玄幻）
- `--output-file`/`-f`: 导出文件名（默认：使用小说标题）

输出目录结构：
```
output_new/
├── outline.json        # 大纲文件（会自动复制到输出目录）
├── style_guide.txt     # 风格指南
├── background.json     # 背景设定
├── novel_info.json     # 小说信息
├── chapter_1.txt       # 第1章
├── chapter_2.txt       # 第2章
└── ...
```

## 系统流程

1. 用户输入小说流派
2. 系统生成风格提示词
3. 系统生成背景设定、人物卡片和伏笔管理
4. 系统生成章节大纲
5. 系统基于大纲生成章节内容
6. 系统检查章节质量
7. 根据检查结果更新人物状态和伏笔使用情况
8. 循环步骤4-7直至完成所有章节

## 配置

可在`config.py`中配置API密钥和其他参数：

```python
# DeepSeek V3 API配置
DEEPSEEK_API_URL = "https://llm.chutes.ai/v1/chat/completions"
DEEPSEEK_API_KEY = "your_api_key"

# 输出配置
OUTPUT_DIR = "output"

# 小说配置
DEFAULT_CHAPTER_LENGTH = 10000  # 默认章节长度（汉字数）
TARGET_NOVEL_LENGTH = 550000    # 目标小说总长度（汉字，50-60万字）
```

## 测试API

可以使用测试脚本验证API配置是否正确：

```
python test_api.py
```

如果测试通过，则说明API配置正确，可以开始生成小说。

## 输出目录结构

```
output/
├── background.json     # 背景设定
├── characters.json     # 人物卡片
├── chapter_index.json  # 章节索引
├── foreshadowings.json # 伏笔管理
├── novel_info.json     # 小说基本信息
├── outline.json        # 大纲
└── chapters/           # 章节内容
    ├── chapter_001.txt
    ├── chapter_002.txt
    └── ...
```

## 限制

- API请求可能受速率限制，建议使用延迟
- 生成大量内容需要较长时间
- LLM生成内容可能存在随机性

## 许可

MIT许可 
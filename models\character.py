"""
人物卡片模型
"""

import json
import os
import random
from typing import Dict, Any, List, Optional

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import OUTPUT_DIR
from utils.file_manager import (
    CHARACTERS_DIR, ensure_directory, save_character_group,
    load_character_group, get_all_character_groups, migrate_characters_to_groups
)

# 角色类型常量
ROLE_PROTAGONIST = "主角"  # 主角
ROLE_FEMALE_LEAD = "女主"  # 女主
ROLE_MAJOR_SUPPORTING = "主要配角"  # 主要配角
ROLE_MINOR_SUPPORTING = "次要配角"  # 次要配角
ROLE_MAJOR_ANTAGONIST = "主要反派"  # 主要反派
ROLE_MINOR_ANTAGONIST = "次要反派"  # 次要反派

# 角色类型到相关度的映射
ROLE_RELEVANCE_MAP = {
    ROLE_PROTAGONIST: 100,  # 主角
    ROLE_FEMALE_LEAD: 95,   # 女主
    ROLE_MAJOR_ANTAGONIST: 90,  # 主要反派
    ROLE_MAJOR_SUPPORTING: 80,  # 主要配角
    ROLE_MINOR_SUPPORTING: 60,  # 次要配角
    ROLE_MINOR_ANTAGONIST: 70,  # 次要反派
}

# 关系类型到相关度的映射
RELATIONSHIP_RELEVANCE_MAP = {
    # 亲密关系（90-95）
    "道侣": 95,
    "爱人": 95,
    "夫妻": 95,
    "恋人": 95,
    "伴侣": 95,
    "情人": 90,

    # 家庭关系（85-95）
    "父母": 95,
    "子女": 95,
    "父亲": 95,
    "母亲": 95,
    "儿子": 95,
    "女儿": 95,
    "兄弟": 90,
    "姐妹": 90,
    "兄长": 90,
    "弟弟": 90,
    "姐姐": 90,
    "妹妹": 90,
    "祖父": 85,
    "祖母": 85,
    "外祖父": 85,
    "外祖母": 85,
    "孙子": 85,
    "孙女": 85,
    "外孙": 85,
    "外孙女": 85,
    "叔叔": 85,
    "阿姨": 85,
    "舅舅": 85,
    "姑姑": 85,
    "堂兄弟": 85,
    "堂姐妹": 85,
    "表兄弟": 85,
    "表姐妹": 85,

    # 师徒关系（80-90）
    "师父": 90,
    "师傅": 90,
    "师尊": 90,
    "师徒": 85,
    "弟子": 85,
    "门徒": 85,
    "学生": 80,
    "老师": 80,
    "导师": 80,

    # 友好关系（70-85）
    "知己": 85,
    "挚友": 85,
    "好友": 80,
    "朋友": 75,
    "同伴": 75,
    "伙伴": 75,
    "盟友": 75,
    "同学": 70,
    "同窗": 70,
    "同事": 70,
    "战友": 75,

    # 社会关系（60-80）
    "君臣": 80,
    "主仆": 75,
    "上司": 75,
    "下属": 75,
    "领导": 75,
    "属下": 75,
    "主人": 75,
    "仆人": 70,
    "侍从": 70,
    "护卫": 70,
    "邻居": 60,
    "同乡": 60,

    # 特殊关系（75-90）
    "救命恩人": 90,
    "恩人": 85,
    "引路人": 85,
    "守护者": 85,
    "被守护者": 85,
    "结拜兄弟": 85,
    "结拜姐妹": 85,
    "义父": 85,
    "义母": 85,
    "义子": 85,
    "义女": 85,
    "契约者": 80,
    "契约伙伴": 80,
    "追随者": 75,
    "崇拜者": 75,

    # 对立关系（75-90）
    "宿敌": 90,
    "死敌": 90,
    "仇人": 90,
    "敌人": 85,
    "对手": 85,
    "竞争对手": 80,
    "政敌": 80,
    "情敌": 80,
    "冤家": 75,
    "对立者": 75,

    # 复杂关系（80-95）
    "亦敌亦友": 85,
    "相互利用": 80,
    "杀父仇人": 95,
    "杀母仇人": 95,
    "杀师仇人": 90,
    "血海深仇": 95,
    "世仇": 90,
    "前世因果": 90,
    "命中注定": 90,
    "暗中保护": 85,
    "秘密守护": 85,
    "暗中监视": 80,
    "生死之交": 90,
    "背叛者": 85,
    "叛徒": 85,
    "棋子": 75,
    "傀儡": 75,
    "主公": 85,
    "效忠对象": 85,
    "忠臣": 85,
    "心腹": 85,
    "密探": 80,
    "卧底": 80,
    "双面间谍": 85
}

def standardize_role(role: str) -> str:
    """
    标准化角色类型

    Args:
        role: 原始角色类型

    Returns:
        标准化后的角色类型
    """
    role = role.strip()

    if role == ROLE_PROTAGONIST:
        return ROLE_PROTAGONIST
    elif role == ROLE_FEMALE_LEAD:
        return ROLE_FEMALE_LEAD
    elif role == ROLE_MAJOR_SUPPORTING or role == "重要配角":
        return ROLE_MAJOR_SUPPORTING
    elif role == ROLE_MINOR_SUPPORTING or "配角" in role and "主要" not in role and "重要" not in role:
        return ROLE_MINOR_SUPPORTING
    elif role == ROLE_MAJOR_ANTAGONIST or role == "反派" or role == "主反派":
        return ROLE_MAJOR_ANTAGONIST
    elif role == ROLE_MINOR_ANTAGONIST or "次要反派" in role or ("反派" in role and "次要" in role):
        return ROLE_MINOR_ANTAGONIST
    else:
        # 默认返回次要配角
        return ROLE_MINOR_SUPPORTING

def determine_character_appearance(character: 'Character', current_chapter: int, total_chapters: int) -> bool:
    """
    根据角色特性和章节信息，决定该角色是否应该在当前章节出场

    Args:
        character: 角色对象
        current_chapter: 当前章节号
        total_chapters: 总章节数

    Returns:
        是否应该出场
    """
    # 首先检查预定义的出场章节列表
    if character.appearance_chapters and current_chapter in character.appearance_chapters:
        return True

    # 如果预定义列表中明确不出场，则不出场
    if character.appearance_chapters and current_chapter not in character.appearance_chapters:
        return False
    
    # 如果没有预定义的出场章节，使用原有算法推算
    # 检查是否已经超出了角色的首次出场章节
    if current_chapter < character.first_appearance_chapter:
        return False

    # 对于主角，大部分章节都应该出场
    if character.role == "主角":
        # 主角在95%的章节中出场
        return True
    
    # 对于女主，约60-70%的章节出场
    if character.role == "女主":
        # 女主在首次出场后的章节中，大约70%的章节出场
        return random.random() < 0.7
    
    # 对于其他角色，根据与主线的相关程度决定出场频率
    # 相关程度越高，出场频率越高
    appearance_chance = character.relevance_to_main_plot / 100.0
    
    # 最近几章没有出场的角色，出场概率增加
    if character.absent_chapters > 0:
        # 每缺席一章，出场概率增加10%，但总概率不超过95%
        appearance_chance = min(0.95, appearance_chance + 0.1 * character.absent_chapters)
    
    return random.random() < appearance_chance


class Character:
    """人物模型"""

    def __init__(self, name: str, role: str, basic_info: Dict[str, Any] = None,
                 appearance: str = "", personality: str = "", abilities: List[str] = None,
                 background: str = "", motivation: str = "", relationships: Dict[str, List[str]] = None,
                 growth_path: str = "", current_status: str = "", current_power: str = "",
                 relevance_to_main_plot: int = 0, absent_chapters: int = 0, total_appearances: int = 0,
                 first_appearance_chapter: int = 1, appearance_chapters: List[int] = None):
        """
        初始化人物模型

        Args:
            name: 人物姓名
            role: 角色类型（主角/女主/主要配角/次要配角/主要反派/次要反派）
            basic_info: 基本信息
            appearance: 外貌特征
            personality: 性格特点
            abilities: 能力/技能
            background: 背景故事
            motivation: 动机与目标
            relationships: 与其他角色的关系，格式为 {角色名: [关系1, 关系2, ...]}
            growth_path: 成长轨迹预期
            current_status: 当前状态
            current_power: 当前战力
            relevance_to_main_plot: 与主线相关程度（0-100）
            absent_chapters: 掉线章节数
            total_appearances: 总出场章节数
            first_appearance_chapter: 角色首次出场的章节号
            appearance_chapters: 该人物应该出场的章节列表
        """
        self.name = name
        self.role = role
        self.basic_info = basic_info or {}
        self.appearance = appearance
        self.personality = personality
        self.abilities = abilities or []
        self.background = background
        self.motivation = motivation
        self.relationships = relationships or {}

        # 确保 relationships 的值是列表
        for char_name, relations in list(self.relationships.items()):
            if isinstance(relations, str):
                self.relationships[char_name] = [relations]

        self.growth_path = growth_path
        self.current_status = current_status
        self.current_power = current_power
        self.relevance_to_main_plot = relevance_to_main_plot
        self.absent_chapters = absent_chapters
        self.total_appearances = total_appearances
        self.first_appearance_chapter = first_appearance_chapter
        self.appearance_chapters = appearance_chapters or []

        # 如果是主角，自动设置相关度为100和首次出场章节为1
        if self.role == "主角":
            self.relevance_to_main_plot = 100
            self.first_appearance_chapter = 1
        
        # 如果是女主，默认首次出场章节为2（除非显式指定）
        elif self.role == "女主" and first_appearance_chapter == 1:
            self.first_appearance_chapter = 2

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            字典格式的人物
        """
        return {
            "name": self.name,
            "role": self.role,
            "basic_info": self.basic_info,
            "appearance": self.appearance,
            "personality": self.personality,
            "abilities": self.abilities,
            "background": self.background,
            "motivation": self.motivation,
            "relationships": self.relationships,
            "growth_path": self.growth_path,
            "current_status": self.current_status,
            "current_power": self.current_power,
            "relevance_to_main_plot": self.relevance_to_main_plot,
            "absent_chapters": self.absent_chapters,
            "total_appearances": self.total_appearances,
            "first_appearance_chapter": self.first_appearance_chapter,
            "appearance_chapters": self.appearance_chapters
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Character':
        """
        从字典创建人物模型

        Args:
            data: 字典格式的人物

        Returns:
            人物模型实例
        """
        # 获取背景字段
        background = data.get("background", "")

        # 检查背景字段是否包含嵌套的JSON结构
        if isinstance(background, str) and background.strip().startswith("{") and background.strip().endswith("}"):
            try:
                # 尝试解析JSON
                bg_data = json.loads(background)

                # 如果解析成功且包含characters字段，说明这是一个嵌套的人物卡片JSON
                if isinstance(bg_data, dict) and "characters" in bg_data and isinstance(bg_data["characters"], list):
                    # 提取第一个角色的信息（如果有）
                    if bg_data["characters"]:
                        char_data = bg_data["characters"][0]
                        # 使用嵌套JSON中的数据更新当前数据
                        if data.get("name") == "原始人物卡片" or not data.get("name"):
                            data["name"] = char_data.get("name", data.get("name", ""))
                        if data.get("role") == "集合" or not data.get("role"):
                            data["role"] = char_data.get("role", data.get("role", ""))
                        if not data.get("basic_info") or data.get("basic_info") == {}:
                            data["basic_info"] = char_data.get("basic_info", {})
                        if not data.get("appearance"):
                            data["appearance"] = char_data.get("appearance", "")
                        if not data.get("personality"):
                            data["personality"] = char_data.get("personality", "")
                        if not data.get("abilities") or data.get("abilities") == []:
                            data["abilities"] = char_data.get("abilities", [])
                        # 使用嵌套JSON中的背景字段替换原始背景字段
                        background = char_data.get("background", background)
                        if not data.get("motivation"):
                            data["motivation"] = char_data.get("motivation", "")
                        if not data.get("relationships") or data.get("relationships") == {}:
                            data["relationships"] = char_data.get("relationships", {})
                        if not data.get("growth_path"):
                            data["growth_path"] = char_data.get("growth_path", "")
                        if not data.get("current_status"):
                            data["current_status"] = char_data.get("current_status", "")
            except Exception as e:
                # 如果解析失败，保留原始背景字段
                print(f"解析嵌套的人物卡片JSON时出错: {str(e)}")

        # 获取新增的人物参数，如果不存在则使用默认值
        relevance_to_main_plot = data.get("relevance_to_main_plot", 0)
        absent_chapters = data.get("absent_chapters", 0)
        total_appearances = data.get("total_appearances", 0)

        # 处理关系数据，确保值是列表
        relationships = data.get("relationships", {})
        processed_relationships = {}
        for char_name, relations in relationships.items():
            if isinstance(relations, str):
                # 如果是字符串，转换为列表
                processed_relationships[char_name] = [relations]
            elif isinstance(relations, list):
                # 如果是列表，保持不变
                processed_relationships[char_name] = relations
            else:
                # 其他情况，忽略
                print(f"警告：角色 '{data.get('name', '')}' 的关系数据格式不正确")

        # 如果相关度未设置，根据角色类型设置默认值
        if relevance_to_main_plot == 0:
            role = data.get("role", "")

            # 标准化角色类型
            standardized_role = standardize_role(role)

            # 根据标准化后的角色类型设置相关度
            relevance_to_main_plot = ROLE_RELEVANCE_MAP.get(standardized_role, 30)

            # 根据与其他角色的关系调整相关度
            for char_name, relations in processed_relationships.items():
                for relation in relations:
                    if relation in RELATIONSHIP_RELEVANCE_MAP:
                        relevance_to_main_plot = max(relevance_to_main_plot, RELATIONSHIP_RELEVANCE_MAP[relation])

        return cls(
            name=data.get("name", ""),
            role=data.get("role", ""),
            basic_info=data.get("basic_info", {}),
            appearance=data.get("appearance", ""),
            personality=data.get("personality", ""),
            abilities=data.get("abilities", []),
            background=background,
            motivation=data.get("motivation", ""),
            relationships=processed_relationships,  # 使用处理后的关系数据
            growth_path=data.get("growth_path", ""),
            current_status=data.get("current_status", ""),
            current_power=data.get("current_power", ""),
            relevance_to_main_plot=relevance_to_main_plot,
            absent_chapters=absent_chapters,
            total_appearances=total_appearances,
            first_appearance_chapter=data.get("first_appearance_chapter", 1),
            appearance_chapters=data.get("appearance_chapters", [])
        )

    def update_status(self, new_status: str, new_power: str = None) -> None:
        """
        更新人物状态和战力

        Args:
            new_status: 新状态
            new_power: 新战力（可选）
        """
        self.current_status = new_status
        if new_power is not None:
            self.current_power = new_power

    def update_relationships(self, character_name: str, relationship: str) -> None:
        """
        更新人物关系

        Args:
            character_name: 相关人物名称
            relationship: 关系描述，必须是 RELATIONSHIP_RELEVANCE_MAP 中的关系类型
        """
        # 检查关系类型是否有效
        if relationship not in RELATIONSHIP_RELEVANCE_MAP:
            print(f"警告：关系类型 '{relationship}' 不在预定义的关系类型中")
            return

        # 如果角色不存在，则创建一个空列表
        if character_name not in self.relationships:
            self.relationships[character_name] = []

        # 如果关系不存在，则添加
        if relationship not in self.relationships[character_name]:
            self.relationships[character_name].append(relationship)

    def add_ability(self, ability: str) -> None:
        """
        添加能力

        Args:
            ability: 能力描述
        """
        if ability not in self.abilities:
            self.abilities.append(ability)

    def update_appearance_stats(self, appeared: bool) -> None:
        """
        更新人物出场统计信息

        Args:
            appeared: 是否在当前章节出场
        """
        if appeared:
            self.total_appearances += 1
            self.absent_chapters = 0  # 重置掉线章节数
        else:
            self.absent_chapters += 1

    def update_relevance(self, relevance: int) -> None:
        """
        更新人物与主线的相关程度

        Args:
            relevance: 新的相关程度值（0-100）
        """
        if 0 <= relevance <= 100:
            self.relevance_to_main_plot = relevance
        else:
            print(f"警告：相关程度值必须在0-100之间，当前值为{relevance}")
            # 限制在有效范围内
            self.relevance_to_main_plot = max(0, min(100, relevance))


class CharacterManager:
    """人物管理器"""

    def __init__(self, characters: List[Character] = None):
        """
        初始化人物管理器

        Args:
            characters: 人物列表
        """
        self.characters = characters or []
        self.file_path = os.path.join(OUTPUT_DIR, "characters.json")

    def add_character(self, character: Character) -> None:
        """
        添加人物

        Args:
            character: 人物
        """
        # 检查人物是否已存在，如果存在则更新
        for i, ch in enumerate(self.characters):
            if ch.name == character.name:
                self.characters[i] = character
                return

        # 如果不存在则添加
        self.characters.append(character)

    def get_character(self, name: str) -> Optional[Character]:
        """
        获取人物

        Args:
            name: 人物姓名

        Returns:
            人物，如果不存在则返回None
        """
        for character in self.characters:
            if character.name == name:
                return character
        return None

    def get_main_character(self) -> Optional[Character]:
        """
        获取主角

        Returns:
            主角，如果不存在则返回None
        """
        for character in self.characters:
            if character.role == "主角":
                return character
        return None

    def get_antagonist(self) -> Optional[Character]:
        """
        获取主要反派

        Returns:
            主要反派，如果不存在则返回None
        """
        for character in self.characters:
            if character.role == "反派":
                return character
        return None

    def update_character_status(self, name: str, status: str, power: str = None) -> bool:
        """
        更新人物状态和战力

        Args:
            name: 人物姓名
            status: 新状态
            power: 新战力（可选）

        Returns:
            更新是否成功
        """
        character = self.get_character(name)
        if character:
            character.update_status(status, power)
            return True
        return False

    def batch_update_character_status(self, updates: List[Dict[str, str]]) -> int:
        """
        批量更新人物状态和战力

        Args:
            updates: 更新列表，每项包含name、status和可选的power

        Returns:
            成功更新的人物数量
        """
        success_count = 0
        for update in updates:
            name = update.get("name", "")
            status = update.get("status", "")
            power = update.get("power", None)

            if name and status:
                if self.update_character_status(name, status, power):
                    success_count += 1

        # 如果有更新，保存到文件
        if success_count > 0:
            self.save()

        return success_count

    def add_character_from_dict(self, char_data: Dict[str, Any]) -> None:
        """
        从字典创建并添加人物

        Args:
            char_data: 字典格式的人物数据
        """
        # 获取角色类型和相关度
        role = char_data.get("role", "")
        relevance_to_main_plot = char_data.get("relevance_to_main_plot", 0)

        # 如果相关度未设置，根据角色类型自动设置
        if relevance_to_main_plot == 0 or isinstance(relevance_to_main_plot, str):
            # 尝试将字符串转换为数值
            if isinstance(relevance_to_main_plot, str):
                try:
                    relevance_to_main_plot = int(relevance_to_main_plot)
                except ValueError:
                    relevance_to_main_plot = 0

            # 标准化角色类型
            standardized_role = standardize_role(role)

            # 根据标准化后的角色类型设置相关度
            relevance_to_main_plot = ROLE_RELEVANCE_MAP.get(standardized_role, 30)

            # 获取所有主角
            main_characters = [c for c in self.characters if c.role == ROLE_PROTAGONIST]

            # 处理关系数据，确保值是列表
            relationships = char_data.get("relationships", {})
            processed_relationships = {}
            for char_name, relations in relationships.items():
                if isinstance(relations, str):
                    # 如果是字符串，转换为列表
                    processed_relationships[char_name] = [relations]
                elif isinstance(relations, list):
                    # 如果是列表，保持不变
                    processed_relationships[char_name] = relations
                else:
                    # 其他情况，忽略
                    print(f"警告：角色 '{char_data.get('name', '')}' 的关系数据格式不正确")

            # 更新原始数据中的关系
            char_data["relationships"] = processed_relationships

            # 如果有主角，检查与主角的关系
            if main_characters:
                for main_char in main_characters:
                    if main_char.name in processed_relationships:
                        for relation in processed_relationships[main_char.name]:
                            if relation in RELATIONSHIP_RELEVANCE_MAP:
                                relevance_to_main_plot = max(relevance_to_main_plot, RELATIONSHIP_RELEVANCE_MAP[relation])
            else:
                # 如果没有主角，检查所有关系
                for char_name, relations in processed_relationships.items():
                    for relation in relations:
                        if relation in RELATIONSHIP_RELEVANCE_MAP:
                            relevance_to_main_plot = max(relevance_to_main_plot, RELATIONSHIP_RELEVANCE_MAP[relation])

        # 创建人物对象
        character = Character(
            name=char_data.get("name", ""),
            role=role,
            basic_info=char_data.get("basic_info", {}),
            appearance=char_data.get("appearance", ""),
            personality=char_data.get("personality", ""),
            abilities=char_data.get("abilities", []),
            background=char_data.get("background", ""),
            motivation=char_data.get("motivation", ""),
            relationships=char_data.get("relationships", {}),
            growth_path=char_data.get("growth_path", ""),
            current_status=char_data.get("current_status", ""),
            current_power=char_data.get("current_power", ""),
            relevance_to_main_plot=relevance_to_main_plot,
            absent_chapters=char_data.get("absent_chapters", 0),
            total_appearances=char_data.get("total_appearances", 0),
            first_appearance_chapter=char_data.get("first_appearance_chapter", 1),
            appearance_chapters=char_data.get("appearance_chapters", [])
        )

        # 添加到人物列表
        self.add_character(character)

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            字典格式的人物管理器
        """
        return {
            "characters": [character.to_dict() for character in self.characters]
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CharacterManager':
        """
        从字典创建人物管理器

        Args:
            data: 字典格式的人物管理器

        Returns:
            人物管理器实例
        """
        characters = [Character.from_dict(ch) for ch in data.get("characters", [])]
        return cls(characters=characters)

    def save(self) -> bool:
        """
        保存人物管理器到文件

        Returns:
            保存是否成功
        """
        try:
            # 确保目录存在
            ensure_directory(CHARACTERS_DIR)

            # 为了向后兼容，也保存到原来的位置
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)

            # 将人物按角色类型分组保存
            characters_dict = [character.to_dict() for character in self.characters]

            # 获取小说流派
            novel_info_path = os.path.join(OUTPUT_DIR, "novel_info.json")
            genre = ""
            if os.path.exists(novel_info_path):
                try:
                    with open(novel_info_path, 'r', encoding='utf-8') as f:
                        novel_info = json.load(f)
                        genre = novel_info.get("genre", "")
                except:
                    pass

            # 迁移到分组文件
            migrate_characters_to_groups(characters_dict, genre)

            return True
        except Exception as e:
            print(f"保存人物管理器时出错: {str(e)}")
            return False

    @classmethod
    def load(cls, file_path: Optional[str] = None) -> Optional['CharacterManager']:
        """
        从文件加载人物管理器

        Args:
            file_path: 文件路径，如果为None则使用默认路径

        Returns:
            人物管理器实例，如果加载失败则返回None
        """
        try:
            # 首先尝试从分组文件加载
            all_characters = []
            groups = get_all_character_groups()

            if groups:
                # 从各个分组加载人物
                for group_name in groups:
                    if group_name == "index":  # 跳过索引文件
                        continue

                    try:
                        group_characters = load_character_group(group_name)
                        if group_characters:
                            # 检查是否有嵌套的JSON结构
                            for char_data in group_characters:
                                # 检查所有可能包含嵌套JSON的字段
                                for field in ["background", "motivation", "relationships"]:
                                    field_value = char_data.get(field, "")
                                    if isinstance(field_value, str) and field_value.strip().startswith("{") and field_value.strip().endswith("}"):
                                        try:
                                            # 尝试解析JSON
                                            parsed_data = json.loads(field_value)

                                            # 如果是relationships字段，特殊处理
                                            if field == "relationships" and isinstance(parsed_data, dict):
                                                char_data[field] = parsed_data
                                                print(f"成功解析角色 {char_data.get('name', '未知')} 的关系数据")

                                            # 如果解析成功且包含characters字段，说明这是一个嵌套的人物卡片JSON
                                            if isinstance(parsed_data, dict) and "characters" in parsed_data and isinstance(parsed_data["characters"], list):
                                                print(f"检测到嵌套的人物卡片JSON结构，尝试修复...")
                                                # 提取嵌套的角色信息并替换当前组
                                                nested_characters = []
                                                for nested_char in parsed_data["characters"]:
                                                    if nested_char.get("name") != "原始人物卡片":
                                                        # 检查嵌套角色的字段是否也有嵌套JSON
                                                        for nested_field in ["background", "motivation", "relationships"]:
                                                            nested_value = nested_char.get(nested_field, "")
                                                            if isinstance(nested_value, str) and nested_value.strip().startswith("{") and nested_value.strip().endswith("}"):
                                                                try:
                                                                    nested_parsed = json.loads(nested_value)
                                                                    nested_char[nested_field] = nested_parsed
                                                                except:
                                                                    pass

                                                        nested_characters.append(nested_char)

                                                if nested_characters:
                                                    # 如果找到了嵌套的角色，替换当前组
                                                    print(f"从嵌套JSON中提取了{len(nested_characters)}个角色")
                                                    # 保存修复后的角色组
                                                    save_character_group(group_name, nested_characters)
                                                    # 更新当前加载的角色
                                                    group_characters = nested_characters
                                                    break  # 找到嵌套角色后跳出字段循环
                                        except Exception as e:
                                            print(f"解析角色 {char_data.get('name', '未知')} 的 {field} 字段时出错: {str(e)}")

                            all_characters.extend(group_characters)
                    except Exception as e:
                        print(f"加载角色组 {group_name} 时出错: {str(e)}")

                if all_characters:
                    characters = [Character.from_dict(ch) for ch in all_characters]
                    return cls(characters=characters)

            # 如果分组文件不存在或为空，尝试从旧文件加载
            file_path = file_path or os.path.join(OUTPUT_DIR, "characters.json")
            if not os.path.exists(file_path):
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 检查是否有嵌套的JSON结构
            if "characters" in data:
                for char_data in data["characters"]:
                    # 检查所有可能包含嵌套JSON的字段
                    for field in ["background", "motivation", "relationships"]:
                        field_value = char_data.get(field, "")
                        if isinstance(field_value, str) and field_value.strip().startswith("{") and field_value.strip().endswith("}"):
                            try:
                                # 尝试解析JSON
                                parsed_data = json.loads(field_value)

                                # 如果是relationships字段，特殊处理
                                if field == "relationships" and isinstance(parsed_data, dict):
                                    char_data[field] = parsed_data
                                    print(f"成功解析角色 {char_data.get('name', '未知')} 的关系数据")

                                # 如果解析成功且包含characters字段，说明这是一个嵌套的人物卡片JSON
                                if isinstance(parsed_data, dict) and "characters" in parsed_data and isinstance(parsed_data["characters"], list):
                                    print(f"检测到嵌套的人物卡片JSON结构，尝试修复...")
                                    # 提取嵌套的角色信息并替换当前数据
                                    nested_characters = []
                                    for nested_char in parsed_data["characters"]:
                                        if nested_char.get("name") != "原始人物卡片":
                                            # 检查嵌套角色的字段是否也有嵌套JSON
                                            for nested_field in ["background", "motivation", "relationships"]:
                                                nested_value = nested_char.get(nested_field, "")
                                                if isinstance(nested_value, str) and nested_value.strip().startswith("{") and nested_value.strip().endswith("}"):
                                                    try:
                                                        nested_parsed = json.loads(nested_value)
                                                        nested_char[nested_field] = nested_parsed
                                                    except:
                                                        pass

                                            nested_characters.append(nested_char)

                                    if nested_characters:
                                        # 如果找到了嵌套的角色，替换当前数据
                                        print(f"从嵌套JSON中提取了{len(nested_characters)}个角色")
                                        data["characters"] = nested_characters
                                        break  # 找到嵌套角色后跳出字段循环
                            except Exception as e:
                                print(f"解析角色 {char_data.get('name', '未知')} 的 {field} 字段时出错: {str(e)}")

            # 加载后，自动迁移到分组文件
            manager = cls.from_dict(data)
            manager.save()  # 这会触发迁移

            return manager
        except Exception as e:
            print(f"加载人物管理器时出错: {str(e)}")
            return None

    def get_characters_by_role(self, role: str) -> List[Character]:
        """
        按角色类型获取人物

        Args:
            role: 角色类型

        Returns:
            符合条件的人物列表
        """
        return [c for c in self.characters if c.role == role]

    def get_appearing_characters(self, current_chapter: int, total_chapters: int) -> List[Character]:
        """
        获取应该在当前章节出场的人物

        Args:
            current_chapter: 当前章节号
            total_chapters: 总章节数

        Returns:
            应该出场的人物列表
        """
        appearing_characters = []
        for character in self.characters:
            # 确保角色只有在达到或超过其首次出场章节时才能出场
            if current_chapter >= character.first_appearance_chapter:
                if determine_character_appearance(character, current_chapter, total_chapters):
                    appearing_characters.append(character)
        return appearing_characters

    def update_appearance_stats(self, chapter_number: int, appearing_character_names: List[str]) -> None:
        """
        更新所有人物的出场统计信息

        Args:
            chapter_number: 当前章节号
            appearing_character_names: 出场人物名称列表
        """
        for character in self.characters:
            appeared = character.name in appearing_character_names
            character.update_appearance_stats(appeared)

    def update_all_relevance(self) -> int:
        """
        根据角色类型和描述更新所有角色的相关度值

        Returns:
            更新的角色数量
        """
        updated_count = 0

        # 获取所有主角
        main_characters = [c for c in self.characters if c.role == ROLE_PROTAGONIST]

        for character in self.characters:
            # 如果已经设置了相关度，则跳过
            if character.relevance_to_main_plot > 0:
                continue

            # 标准化角色类型
            standardized_role = standardize_role(character.role)

            # 根据标准化后的角色类型设置相关度
            character.relevance_to_main_plot = ROLE_RELEVANCE_MAP.get(standardized_role, 30)
            updated_count += 1

            # 确保 relationships 的值是列表
            for char_name, relations in list(character.relationships.items()):
                if isinstance(relations, str):
                    character.relationships[char_name] = [relations]

            # 如果有主角，检查与主角的关系
            if main_characters:
                for main_char in main_characters:
                    if main_char.name in character.relationships:
                        for relation in character.relationships[main_char.name]:
                            if relation in RELATIONSHIP_RELEVANCE_MAP:
                                character.relevance_to_main_plot = max(character.relevance_to_main_plot, RELATIONSHIP_RELEVANCE_MAP[relation])
                                updated_count += 1
            else:
                # 如果没有主角，检查所有关系
                for char_name, relations in character.relationships.items():
                    for relation in relations:
                        if relation in RELATIONSHIP_RELEVANCE_MAP:
                            character.relevance_to_main_plot = max(character.relevance_to_main_plot, RELATIONSHIP_RELEVANCE_MAP[relation])
                            updated_count += 1

        # 如果有更新，保存到文件
        if updated_count > 0:
            self.save()

        return updated_count

    def __str__(self) -> str:
        """
        字符串表示

        Returns:
            人物管理器的字符串表示
        """
        result = []
        for character in self.characters:
            result.append(f"{character.name}（{character.role}）：{character.current_status}")
        return "\n".join(result)
#!/usr/bin/env python3
"""
从大纲文件中直接生成章节
"""

import os
import json
import re # Moved import to top
import time
import shutil
import textwrap
import traceback # Moved import to top
from typing import Dict, Any, List, Optional, Union, Tuple

from llm.deepseek_all import DeepSeekAllAPI
# from utils.file_manager import read_chapter_file # Shadowed by local definition

# 最大重试次数常量
MAX_RETRIES = 3

def read_chapter_file(chapter_number: int, output_dir: str = 'output_new') -> Optional[str]:
    """
    读取指定章节的内容文件
    
    参数:
        chapter_number: 章节编号
        output_dir: 输出目录
        
    返回:
        章节内容字符串，如果文件不存在则返回None
    """
    chapter_file = os.path.join(output_dir, f'chapter_{chapter_number}.txt')
    if os.path.exists(chapter_file):
        try:
            with open(chapter_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"读取章节文件出错: {str(e)}")
    return None

# 添加其他文件管理相关函数
def read_chapter(chapter_number: int) -> Optional[Dict[str, Any]]:
    """
    读取指定章节的内容
    
    参数:
        chapter_number: 章节编号
        
    返回:
        章节内容字典，包含标题和内容，如果文件不存在则返回None
    """
    from config import OUTPUT_DIR
    chapter_file = os.path.join(OUTPUT_DIR, f'chapter_{chapter_number}.txt')
    if os.path.exists(chapter_file):
        try:
            with open(chapter_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 提取标题
                title_match = re.search(r'^(第.+?章\s*.+?)[\n\r]', content)
                title = f"第{chapter_number}章"
                if title_match:
                    title = title_match.group(1).strip()
                    
                return {
                    "title": title,
                    "content": content,
                    "chapter_number": chapter_number
                }
        except Exception as e:
            print(f"读取章节文件出错: {str(e)}")
    return None

def save_chapter(chapter_number: int, content: str) -> bool:
    """
    保存章节内容到文件
    
    参数:
        chapter_number: 章节编号
        content: 章节内容
        
    返回:
        保存是否成功
    """
    from config import OUTPUT_DIR
    try:
        if not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)
            
        chapter_file = os.path.join(OUTPUT_DIR, f'chapter_{chapter_number}.txt')
        with open(chapter_file, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        print(f"保存章节文件出错: {str(e)}")
        return False

def load_novel_info() -> Optional[Dict[str, Any]]:
    """
    加载小说信息
    
    返回:
        小说信息字典，如果文件不存在则返回None
    """
    from config import OUTPUT_DIR
    info_file = os.path.join(OUTPUT_DIR, 'novel_info.json')
    if os.path.exists(info_file):
        try:
            with open(info_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载小说信息出错: {str(e)}")
    return None

def save_novel_info(title: str = "", author: str = "AI作家", genre: str = "", description: str = "") -> bool:
    """
    保存小说信息到文件
    
    参数:
        title: 小说标题
        author: 作者
        genre: 流派
        description: 描述
        
    返回:
        保存是否成功
    """
    from config import OUTPUT_DIR
    try:
        if not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)
            
        # 加载现有信息（如果有）
        info_file = os.path.join(OUTPUT_DIR, 'novel_info.json')
        novel_info = {}
        if os.path.exists(info_file):
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    novel_info = json.load(f)
            except:
                pass
        
        # 更新信息
        if title:
            novel_info['title'] = title
        if author:
            novel_info['author'] = author
        if genre:
            novel_info['genre'] = genre
        if description:
            novel_info['description'] = description
            
        # 设置更新时间
        novel_info['update_time'] = time.strftime("%Y-%m-%d %H:%M:%S")
        if 'create_time' not in novel_info:
            novel_info['create_time'] = novel_info['update_time']
            
        # 保存到文件
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(novel_info, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存小说信息出错: {str(e)}")
        return False

def get_all_chapters() -> List[int]:
    """
    获取所有已生成的章节编号
    
    返回:
        章节编号列表，按顺序排列
    """
    from config import OUTPUT_DIR
    try:
        if not os.path.exists(OUTPUT_DIR):
            return []
            
        chapters = []
        for filename in os.listdir(OUTPUT_DIR):
            match = re.match(r'chapter_(\d+)\.txt', filename)
            if match:
                chapters.append(int(match.group(1)))
        return sorted(chapters)
    except Exception as e:
        print(f"获取章节列表出错: {str(e)}")
        return []

def export_novel_as_txt(output_path: Optional[str] = None, output_dir: str = None) -> bool:
    """
    将小说导出为单个txt文件
    
    参数:
        output_path: 输出文件路径，如果为None则使用默认路径
        output_dir: 输出目录，如果为None则使用默认目录
        
    返回:
        导出是否成功
    """
    try:
        # 确定输出目录
        from config import OUTPUT_DIR
        source_dir = OUTPUT_DIR if output_dir is None else output_dir
        
        # 获取小说信息
        novel_info = None
        info_file = os.path.join(source_dir, 'novel_info.json')
        if os.path.exists(info_file):
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    novel_info = json.load(f)
            except:
                pass
        
        # 如果没有找到小说信息，尝试从大纲文件中获取
        title = "未命名小说"
        author = "AI作家"
        
        # 尝试从大纲文件中获取标题
        if source_dir == 'output_new':
            # 从 output_new/outline.json 获取标题
            outline_file = os.path.join(source_dir, 'outline.json')
            if os.path.exists(outline_file):
                try:
                    with open(outline_file, 'r', encoding='utf-8') as f:
                        outline_data = json.load(f)
                        if 'story_title' in outline_data:
                            title = outline_data['story_title']
                except:
                    pass
        else:
            # 从 output/main_storyline.json 获取标题
            storyline_file = os.path.join(source_dir, 'main_storyline.json')
            if os.path.exists(storyline_file):
                try:
                    with open(storyline_file, 'r', encoding='utf-8') as f:
                        storyline_data = json.load(f)
                        if 'story_title' in storyline_data:
                            title = storyline_data['story_title']
                except:
                    pass
        
        # 如果找到了小说信息，使用其中的标题和作者
        if novel_info:
            if 'title' in novel_info and novel_info['title']:
                title = novel_info['title']
            if 'author' in novel_info and novel_info['author']:
                author = novel_info['author']
        
        # 确定输出文件路径
        if not output_path:
            output_path = f"{title}.txt"
        
        # 获取所有章节文件
        chapter_files = []
        for filename in os.listdir(source_dir):
            match = re.match(r'chapter_(\d+)\.txt', filename)
            if match:
                chapter_number = int(match.group(1))
                chapter_files.append((chapter_number, os.path.join(source_dir, filename)))
        
        # 按章节编号排序
        chapter_files.sort(key=lambda x: x[0])
        
        if not chapter_files:
            print("未找到任何章节文件")
            return False
        
        # 创建小说内容
        novel_content = f"{title}\n\n作者：{author}\n\n"
        
        # 添加每个章节
        for chapter_number, chapter_file in chapter_files:
            try:
                with open(chapter_file, 'r', encoding='utf-8') as f:
                    chapter_content = f.read()
                novel_content += f"{chapter_content}\n\n"
            except Exception as e:
                print(f"读取章节 {chapter_number} 出错: {str(e)}")
        
        # 保存到文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(novel_content)
        
        print(f"小说已导出到: {output_path}")
        return True
    except Exception as e:
        print(f"导出小说出错: {str(e)}")
        traceback.print_exc()
        return False

class ChapterGenerator:
    """直接从大纲文件生成章节"""
    
    def __init__(self):
        """初始化章节生成器"""
        self.api = DeepSeekAllAPI()
        self.output_dir = 'output_new'
        
    def generate_novel_from_outline(self, genre: str, output_dir: str = 'output_new') -> bool:
        """
        生成写作风格和背景设定，并保存到指定目录
        
        参数:
            genre: 小说流派
            output_dir: 保存生成内容的目录（默认: output_new）
            
        返回:
            生成是否成功
        """
        try:
            # 设置输出目录
            self.output_dir = output_dir
            
            # 如果输出目录不存在，创建它
            os.makedirs(output_dir, exist_ok=True)
            
            print(f"正在为{genre}流派小说生成风格提示词...")
            # 生成风格提示词
            style_guide = self.api.generate_novel_style(
                genre, 
                target_length=None,  # 使用默认值
                total_chapters=None  # 使用默认值
            )
            
            if not style_guide:
                print("生成风格提示词失败")
                return False

            # 保存风格提示词到文件
            style_guide_path = os.path.join(output_dir, "style_guide.txt")
            try:
                with open(style_guide_path, "w", encoding="utf-8") as f:
                    f.write(style_guide)
                print(f"风格提示词已保存至：{style_guide_path}")
            except Exception as e:
                print(f"保存风格提示词时出错: {str(e)}")
                return False
                
            print("风格提示词生成成功")
            
            # 生成背景设定
            print("正在生成背景设定...")
            background_data = self.api.generate_background(genre, style_guide)
            
            if not background_data:
                print("生成背景设定失败")
                return False
                
            # 保存背景设定到文件
            background_path = os.path.join(output_dir, "background.json")
            try:
                with open(background_path, "w", encoding="utf-8") as f:
                    json.dump(background_data, f, ensure_ascii=False, indent=2)
                print(f"背景设定已保存至：{background_path}")
            except Exception as e:
                print(f"保存背景设定时出错: {str(e)}")
                return False
                
            print("背景设定生成成功")
            
            # 创建基本小说信息文件
            novel_info = {
                "genre": genre,
                "author": "AI作家",
                "title": f"{genre}小说",  # 临时标题
                "description": "基于大纲生成的小说",
                "create_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "update_time": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            novel_info_path = os.path.join(output_dir, "novel_info.json")
            try:
                with open(novel_info_path, "w", encoding="utf-8") as f:
                    json.dump(novel_info, f, ensure_ascii=False, indent=2)
                print(f"小说信息已保存至：{novel_info_path}")
            except Exception as e:
                print(f"保存小说信息时出错: {str(e)}")
                return False
            
            print(f"\n风格和背景设定生成完成，已保存到{output_dir}目录！")
            print("您可以继续生成小说大纲，或直接从大纲文件生成小说内容。")
            
            return True
            
        except Exception as e:
            print(f"生成风格和背景设定时出错: {str(e)}")
            traceback.print_exc()
            return False

    def generate_all_chapters(self, outline_path: str, output_dir: str = 'output_new') -> None:
        """
        从大纲文件生成所有章节并保存到输出目录
        
        参数:
            outline_path: outline.json文件的路径
            output_dir: 保存生成章节的目录（默认: output_new）
        """
        # 如果输出目录不存在，创建它
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置输出目录
        self.output_dir = output_dir
        
        # 将大纲文件复制到输出目录（如果源文件和目标文件不是同一个文件）
        output_outline_path = os.path.join(output_dir, 'outline.json')
        if os.path.abspath(outline_path) != os.path.abspath(output_outline_path):
            shutil.copy(outline_path, output_outline_path)
            print(f"已将大纲文件复制到: {output_outline_path}")
        
        # 加载大纲
        with open(outline_path, 'r', encoding='utf-8') as f:
            outline_data = json.load(f)
        
        # 优先从output_new加载风格指南
        style_guide_path = os.path.join(self.output_dir, 'style_guide.txt') # Use self.output_dir
        
        # 如果output_new中不存在风格指南，则尝试从大纲文件所在目录加载
        if not os.path.exists(style_guide_path):
            style_guide_path = os.path.join(os.path.dirname(outline_path), 'style_guide.txt')
        
        style_guide = ""
        if os.path.exists(style_guide_path):
            with open(style_guide_path, 'r', encoding='utf-8') as f:
                style_guide = f.read()
            print(f"已加载风格指南: {style_guide_path}")
        else:
            print("未找到风格指南文件，将使用空风格指南")
        
        # 获取总章节数
        chapters = outline_data.get('outlines', [])
        total_chapters = len(chapters)
        
        print(f"在大纲文件中找到 {total_chapters} 个章节")
        print(f"输出目录: {output_dir}")
        
        # 生成每个章节
        for i, chapter in enumerate(chapters, 1):
            chapter_number = chapter.get('index', i)
            print(f"\n正在生成第 {chapter_number} 章 ({i}/{total_chapters})...")
            
            # 生成章节内容
            previous_chapters_data = chapters[:i-1] if i > 1 else None
            chapter_content = self._generate_chapter(chapter, chapter_number, total_chapters, 
                                                     previous_chapters_data, style_guide)
            
            if chapter_content:
                # 创建章节文件名
                chapter_file = os.path.join(output_dir, f'chapter_{chapter_number}.txt')
                
                # 在保存前再次确保没有重复标题
                # import re # Moved to top
                
                # 尝试提取标题行
                title_match = re.search(r'^(第.+?章\s*.+?)[\n\r]', chapter_content)
                if title_match:
                    title = title_match.group(1).strip()
                    # 移除原始标题行
                    clean_content = re.sub(r'^第.+?章\s*.+?[\n\r]+', '', chapter_content, 1)
                    
                    # 移除可能的Markdown格式标题
                    markdown_title_pattern = re.compile(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]')
                    clean_content = re.sub(markdown_title_pattern, '', clean_content)
                    
                    # 移除正文中可能出现的标题行
                    clean_content = re.sub(r'[\n\r]+第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]+', '\n\n', clean_content)
                    
                    # 去除可能的连续空行
                    clean_content = re.sub(r'[\n\r]{3,}', '\n\n', clean_content).strip() # Add strip
                    
                    # 重新组合标题和内容
                    chapter_content = f"{title}\n\n{clean_content}"
                
                # 保存章节内容
                with open(chapter_file, 'w', encoding='utf-8') as f:
                    f.write(chapter_content)
                
                print(f"第 {chapter_number} 章已生成并保存到 {chapter_file}")
            else:
                print(f"生成第 {chapter_number} 章失败")
        
        print("\n所有章节已成功生成！")
        
        # 自动导出小说
        print("\n正在导出小说...")
        # 尝试从大纲中获取标题
        story_title = outline_data.get('story_title', None)
        output_path = f"{story_title}.txt" if story_title else None
        
        success = export_novel_as_txt(output_path, output_dir)
        if success:
            print(f"小说导出成功！")
        else:
            print("小说导出失败。")
            
    def _generate_chapter(self, chapter_data: Dict[str, Any], chapter_number: int, 
                          total_chapters: int, previous_chapters_data: Optional[List[Dict[str, Any]]] = None,
                          style_guide: str = "") -> Optional[str]:
        """
        根据大纲中的章节数据生成单个章节
        
        参数:
            chapter_data: 大纲中的章节数据
            chapter_number: 当前章节编号
            total_chapters: 总章节数
            previous_chapters_data: 之前章节的数据列表（如果有）
            style_guide: 风格指南文本
            
        返回:
            生成的章节内容，如果生成失败则返回None
        """
        try:
            # 提取章节标题
            chapter_title = chapter_data.get('title', f'第{chapter_number}章')
            if not chapter_title.startswith(f'第{chapter_number}章'):
                chapter_title = f'第{chapter_number}章 {chapter_title}'
            
            # 提取章节摘要 (original name was chapter_summary, which is fine)
            current_chapter_summary_data = chapter_data.get('chapter_summary', {})
            
            # 提取角色
            characters = chapter_data.get('characters', [])
            
            # 提取关键点
            key_points = chapter_data.get('key_points', []) # Not used directly in API calls in this method?
            
            # 提取伏笔
            foreshadowings = chapter_data.get('foreshadowings', {})
            
            # 如果有之前章节，创建其摘要
            previous_chapter_summary_text = None # Renamed for clarity
            if previous_chapters_data and len(previous_chapters_data) > 0:
                prev_summaries_list = []
                for prev_ch_data in previous_chapters_data[-3:]:  # 只使用最后3章作为上下文
                    prev_num = prev_ch_data.get('index', 0)
                    prev_title = prev_ch_data.get('title', f'第{prev_num}章')
                    prev_summary_content = prev_ch_data.get('chapter_summary', {}) # This is summary *data*
                    
                    summary_line = f"第{prev_num}章 {prev_title}："
                    if prev_summary_content:
                        opening = prev_summary_content.get('opening', '')
                        if opening:
                            summary_line += f" 开端：{opening[:100]}..."
                        
                        ending = prev_summary_content.get('ending', '')
                        if ending:
                            summary_line += f" 结尾：{ending[:100]}..."
                    
                    prev_summaries_list.append(summary_line)
                
                previous_chapter_summary_text = "\n".join(prev_summaries_list)
            
            # 格式化章节大纲用于API
            formatted_outline = self._format_chapter_outline(chapter_data, chapter_number, total_chapters)
            
            # 格式化角色信息用于API
            formatted_characters = self._format_characters(characters)
            
            # 格式化伏笔信息用于API
            formatted_foreshadowings = self._format_foreshadowings(foreshadowings)

            # 获取前一章的结尾内容，用于保持连贯性
            previous_chapter_ending_text = None # Renamed
            if chapter_number > 1:
                previous_content_full = read_chapter_file(chapter_number - 1, self.output_dir)
                if previous_content_full:
                    paragraphs = previous_content_full.split('\n\n')
                    last_paragraphs_list = []
                    current_total_length = 0
                    for p_text in reversed(paragraphs):
                        if current_total_length + len(p_text) <= 500:
                            last_paragraphs_list.insert(0, p_text)
                            current_total_length += len(p_text)
                        else:
                            break
                    previous_chapter_ending_text = '\n\n'.join(last_paragraphs_list)
                    print(f"已获取第{chapter_number-1}章结尾内容，长度：{len(previous_chapter_ending_text)}字符")

            MAX_TOTAL_RETRIES = 5
            total_retry_count = 0
            
            while total_retry_count < MAX_TOTAL_RETRIES:
                print(f"开始分段生成第{chapter_number}章内容...（总重试次数：{total_retry_count + 1}/{MAX_TOTAL_RETRIES}）")

                opening_content = None
                for attempt in range(MAX_RETRIES):
                    try:
                        opening_content = self.api.generate_chapter_segment(
                            '开端', chapter_number, formatted_outline, "", 
                            formatted_characters, formatted_foreshadowings, 
                            previous_chapter_ending_text, generation_mode="outline"
                        )
                        if opening_content: break
                        print(f"生成开头部分失败，尝试重试 ({attempt + 1}/{MAX_RETRIES})...")
                    except Exception as e:
                        print(f"生成开头部分出错: {str(e)}，尝试重试 ({attempt + 1}/{MAX_RETRIES})...")

                if not opening_content:
                    print("生成章节开头失败，尝试使用传统方式生成完整章节...")
                    traditional_content = self._generate_chapter_traditional(chapter_number, formatted_outline, formatted_characters, formatted_foreshadowings, previous_chapter_summary_text)
                    if traditional_content: 
                        return traditional_content
                    total_retry_count += 1
                    continue

                development_content = None
                for attempt in range(MAX_RETRIES):
                    try:
                        development_content = self.api.generate_chapter_segment(
                            '发展', chapter_number, formatted_outline, "", 
                            formatted_characters, formatted_foreshadowings, 
                            opening_content, generation_mode="outline"
                        )
                        if development_content: break
                        print(f"生成发展部分失败，尝试重试 ({attempt + 1}/{MAX_RETRIES})...")
                    except Exception as e:
                        print(f"生成发展部分出错: {str(e)}，尝试重试 ({attempt + 1}/{MAX_RETRIES})...")

                if not development_content:
                    print("生成章节发展部分失败，尝试使用传统方式生成完整章节...")
                    traditional_content = self._generate_chapter_traditional(chapter_number, formatted_outline, formatted_characters, formatted_foreshadowings, previous_chapter_summary_text)
                    if traditional_content: 
                        return traditional_content
                    total_retry_count += 1
                    continue
                
                previous_segments_for_climax = opening_content + "\n\n" + development_content
                climax_content = None
                for attempt in range(MAX_RETRIES):
                    try:
                        climax_content = self.api.generate_chapter_segment(
                            '高潮', chapter_number, formatted_outline, "", 
                            formatted_characters, formatted_foreshadowings, 
                            previous_segments_for_climax, generation_mode="outline"
                        )
                        if climax_content: break
                        print(f"生成高潮部分失败，尝试重试 ({attempt + 1}/{MAX_RETRIES})...")
                    except Exception as e:
                        print(f"生成高潮部分出错: {str(e)}，尝试重试 ({attempt + 1}/{MAX_RETRIES})...")

                if not climax_content:
                    print("生成章节高潮部分失败，尝试使用传统方式生成完整章节...")
                    traditional_content = self._generate_chapter_traditional(chapter_number, formatted_outline, formatted_characters, formatted_foreshadowings, previous_chapter_summary_text)
                    if traditional_content: 
                        return traditional_content
                    total_retry_count += 1
                    continue

                previous_segments_for_ending = opening_content + "\n\n" + development_content + "\n\n" + climax_content
                ending_content = None
                for attempt in range(MAX_RETRIES):
                    try:
                        ending_content = self.api.generate_chapter_segment(
                            '结尾', chapter_number, formatted_outline, "", 
                            formatted_characters, formatted_foreshadowings, 
                            previous_segments_for_ending, generation_mode="outline"
                        )
                        if ending_content: break
                        print(f"生成结尾部分失败，尝试重试 ({attempt + 1}/{MAX_RETRIES})...")
                    except Exception as e:
                        print(f"生成结尾部分出错: {str(e)}，尝试重试 ({attempt + 1}/{MAX_RETRIES})...")

                if not ending_content:
                    print("生成章节结尾部分失败，尝试使用传统方式生成完整章节...")
                    traditional_content = self._generate_chapter_traditional(chapter_number, formatted_outline, formatted_characters, formatted_foreshadowings, previous_chapter_summary_text)
                    if traditional_content: 
                        return traditional_content
                    total_retry_count += 1
                    continue

                full_content = opening_content + "\n\n" + development_content + "\n\n" + climax_content + "\n\n" + ending_content

                full_title_match = re.search(r'^(第.+?章\s*.+?)[\n\r]', full_content)
                final_title = chapter_title # Default to outline title
                if full_title_match:
                    final_title = full_title_match.group(1).strip()
                    full_content = re.sub(r'^第.+?章\s*.+?[\n\r]+', '', full_content, 1)
                
                markdown_title_pattern = re.compile(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]')
                full_content = re.sub(markdown_title_pattern, '', full_content)
                full_content = re.sub(r'[\n\r]+第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]+', '\n\n', full_content)
                full_content = f"{final_title}\n\n{full_content.strip()}" # Add strip before combining
                full_content = re.sub(r'[\n\r]{3,}', '\n\n', full_content).strip()

                check_result = self._check_chapter(
                    full_content, formatted_outline, formatted_characters, formatted_foreshadowings, style_guide
                )
                
                if check_result and check_result.get('passed', False):
                    print(f"第 {chapter_number} 章验证通过")
                    return full_content
                else:
                    print(f"第 {chapter_number} 章验证失败: {check_result.get('issues', [])}")
                    issues = check_result.get('issues', [])
                    
                    if not issues: # If check_result failed but no issues reported
                        print("验证失败但无具体问题，将重试生成...")
                        total_retry_count += 1
                        if total_retry_count >= MAX_TOTAL_RETRIES: return full_content # Give up and return current
                        continue

                    word_count_issue_found = False
                    is_insufficient_words = False
                    
                    for issue_item in issues:
                        issue_type_detail = issue_item.get('type', '')
                        if '字数不足' in issue_type_detail or '字数过多' in issue_type_detail:
                            word_count_issue_found = True
                            print(f"字数检查未通过，详细信息：{issue_item.get('description', '')}")
                            if '字数不足' in issue_type_detail:
                                is_insufficient_words = True
                                # break # Process this issue first for retry
                    
                    if is_insufficient_words:
                        print(f"章节字数不足，将进行重试生成（总重试次数：{total_retry_count + 1}/{MAX_TOTAL_RETRIES}）...")
                        total_retry_count += 1
                        if total_retry_count >= MAX_TOTAL_RETRIES:
                            print(f"达到最大重试次数 {MAX_TOTAL_RETRIES}，使用当前生成结果...")
                            # Try to fix other issues if they exist
                            non_word_issues = [iss for iss in issues if '字数不足' not in iss.get('type','')]
                            if non_word_issues:
                                print("尝试修复其他非字数问题...")
                                # Determine fix type for remaining issues
                                fix_type = "大纲冲突" # default
                                for iss in non_word_issues:
                                    current_type = iss.get('type','')
                                    if '背景' in current_type: fix_type = "背景冲突"; break
                                    if '人物' in current_type: fix_type = "人物冲突"; break
                                fixed_ver = self.fix_chapter(full_content, non_word_issues, formatted_outline, formatted_characters, formatted_foreshadowings, fix_type)
                                if fixed_ver: return fixed_ver
                            return full_content # Return as is if no other fixes or fix failed
                        continue # Retry generation due to insufficient words

                    # If not insufficient words, or other issues exist (including "字数过多")
                    print("内容验证存在非字数问题或字数过多，尝试修复...")
                    issue_type_for_fix = "大纲冲突"  # Default
                    for issue_item in issues: # Determine fix type from actual issues
                        current_issue_detail_type = issue_item.get('type', '')
                        if '背景' in current_issue_detail_type: issue_type_for_fix = "背景冲突"; break
                        elif '人物' in current_issue_detail_type: issue_type_for_fix = "人物冲突"; break
                        elif '大纲' in current_issue_detail_type or '结构' in current_issue_detail_type or '伏笔' in current_issue_detail_type: issue_type_for_fix = "大纲冲突"; break
                        # if "字数过多" is an issue, it might also be passed to fix_chapter if not handled by re-generation
                    
                    fixed_content_attempt = self.fix_chapter(
                        full_content, issues, formatted_outline, 
                        formatted_characters, formatted_foreshadowings, issue_type_for_fix
                    )
                    
                    if fixed_content_attempt:
                        print("章节内容已修复，重新进行验证")
                        recheck_result = self._check_chapter(
                            fixed_content_attempt, formatted_outline, formatted_characters, 
                            formatted_foreshadowings, style_guide
                        )
                        if recheck_result and recheck_result.get('passed', False):
                            print(f"第 {chapter_number} 章修复后验证通过")
                            return fixed_content_attempt
                        else:
                            print(f"第 {chapter_number} 章修复后仍未通过验证: {recheck_result.get('issues', [])}")
                            # Check for word count issue again after fix
                            has_word_count_issue_after_fix = False
                            is_insufficient_after_fix = False
                            for issue_item_recheck in recheck_result.get('issues', []):
                                if '字数不足' in issue_item_recheck.get('type', ''):
                                    is_insufficient_after_fix = True; break
                                elif '字数过多' in issue_item_recheck.get('type', ''): # Other word count issue
                                    has_word_count_issue_after_fix = True 
                            
                            if is_insufficient_after_fix and total_retry_count < MAX_TOTAL_RETRIES:
                                print(f"修复后仍有字数不足问题，继续重试生成...")
                                total_retry_count += 1
                                continue
                            print("使用修复后的内容（可能仍有问题）或修复失败，放弃此轮尝试")
                            return fixed_content_attempt # Return fixed content even if it has issues, if not retrying word count
                    else: # Fix failed
                        print("章节内容修复失败，尝试重新生成")
                        total_retry_count += 1
                        continue
            
            print(f"经过 {MAX_TOTAL_RETRIES} 次重试后，仍未能生成符合要求的章节内容")
            return None
            
        except Exception as e:
            print(f"生成第 {chapter_number} 章时出错: {str(e)}")
            traceback.print_exc()
            return None

    def _generate_chapter_traditional(self, chapter_number: int, 
                                      formatted_outline: str, 
                                      formatted_characters: str, 
                                      formatted_foreshadowings: str, 
                                      previous_summary_text: Optional[str] = None) -> Optional[str]: # Added Optional type hint
        """
        使用传统方式（不分段）生成章节内容
        """
        try:
            MAX_TRADITIONAL_RETRIES = 3 # Adjusted to be less than main loop for fallback
            retries = 0
            
            while retries < MAX_TRADITIONAL_RETRIES:
                print(f"使用传统方式生成第 {chapter_number} 章内容...（尝试 {retries + 1}/{MAX_TRADITIONAL_RETRIES}）")
                
                generation_instruction = ""
                if retries > 0: # If this is a retry, emphasize word count
                    generation_instruction = "请确保生成至少5000字的内容，详细展开情节，使字数充足。"
                
                content = self.api.generate_chapter(
                    chapter_number, formatted_outline, "", 
                    formatted_characters, formatted_foreshadowings, 
                    previous_summary_text, generation_instruction
                )
                
                if not content:
                    print(f"传统方式生成第 {chapter_number} 章内容失败，尝试重试 ({retries + 1}/{MAX_TRADITIONAL_RETRIES})")
                    retries += 1
                    continue
                
                # import re # Moved to top
                full_title_match = re.search(r'^(第.+?章\s*.+?)[\n\r]', content)
                final_title = f"第{chapter_number}章" # Default
                chapter_title_from_outline_match = re.search(r'第\d+章\s*(.+)', formatted_outline) # Simplified if outline has "第X章 Title"
                if chapter_title_from_outline_match:
                    final_title = f"第{chapter_number}章 {chapter_title_from_outline_match.group(1).strip()}"


                if full_title_match:
                    final_title = full_title_match.group(1).strip()
                    content = re.sub(r'^第.+?章\s*.+?[\n\r]+', '', content, 1)
                else: # If no title in content, construct from outline if possible
                    # This part reuses final_title logic from above which is now better.
                    pass

                markdown_title_pattern = re.compile(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]')
                content = re.sub(markdown_title_pattern, '', content)
                content = re.sub(r'[\n\r]+第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]+', '\n\n', content)
                content = f"{final_title}\n\n{content.strip()}"
                content = re.sub(r'[\n\r]{3,}', '\n\n', content).strip()
                
                style_guide_text = ""
                style_path = os.path.join(self.output_dir, "style_guide.txt")
                if os.path.exists(style_path):
                    try:
                        with open(style_path, 'r', encoding='utf-8') as f:
                            style_guide_text = f.read()
                    except Exception as e:
                        print(f"读取风格指南出错: {str(e)}")
                
                check_result = self._check_chapter(
                    content, formatted_outline, formatted_characters, formatted_foreshadowings, style_guide_text
                )
                
                if check_result and check_result.get('passed', False):
                    print(f"第 {chapter_number} 章（传统方式）验证通过")
                    return content
                else:
                    print(f"第 {chapter_number} 章（传统方式）验证失败: {check_result.get('issues', [])}")
                    issues = check_result.get('issues', [])

                    if not issues:
                        print("传统方式验证失败但无具体问题，将重试...")
                        retries += 1
                        if retries >= MAX_TRADITIONAL_RETRIES: 
                            return content # Give up and return
                        continue

                    is_insufficient_words = False
                    for issue_item in issues:
                        if '字数不足' in issue_item.get('type', ''):
                            is_insufficient_words = True
                            print(f"传统方式生成字数不足，详细信息：{issue_item.get('description', '')}")
                            break 
                    
                    if is_insufficient_words:
                        print(f"传统方式生成章节字数不足，将进行重试...")
                        retries += 1
                        if retries >= MAX_TRADITIONAL_RETRIES:
                            print(f"达到最大重试次数 {MAX_TRADITIONAL_RETRIES}，使用当前生成结果...")
                            # Try to fix other issues if any before returning
                            non_word_issues = [iss for iss in issues if '字数不足' not in iss.get('type','')]
                            if non_word_issues:
                                fix_type = "大纲冲突" # default
                                for iss in non_word_issues:
                                    current_type = iss.get('type','')
                                    if '背景' in current_type: fix_type = "背景冲突"; break
                                    if '人物' in current_type: fix_type = "人物冲突"; break
                                fixed_ver = self.fix_chapter(content, non_word_issues, formatted_outline, formatted_characters, formatted_foreshadowings, fix_type)
                                if fixed_ver: 
                                    return fixed_ver                                
                            return content # Return as is
                        continue

                    # For other issues or "字数过多" from traditional generation
                    print("传统方式生成内容存在其他问题，尝试修复...")
                    issue_type_for_fix = "大纲冲突" 
                    for issue_item in issues:
                        current_issue_detail_type = issue_item.get('type', '')
                        if '背景' in current_issue_detail_type: issue_type_for_fix = "背景冲突"; break
                        elif '人物' in current_issue_detail_type: issue_type_for_fix = "人物冲突"; break
                        elif '大纲' in current_issue_detail_type or '结构' in current_issue_detail_type or '伏笔' in current_issue_detail_type: issue_type_for_fix = "大纲冲突"; break
                    
                    fixed_content_attempt = self.fix_chapter(
                        content, issues, formatted_outline,
                        formatted_characters, formatted_foreshadowings, issue_type_for_fix
                    )
                    if fixed_content_attempt:
                        print("传统方式章节内容已修复，重新验证")
                        recheck_result = self._check_chapter(fixed_content_attempt, formatted_outline, formatted_characters, formatted_foreshadowings, style_guide_text)
                        if recheck_result and recheck_result.get('passed', False):
                            print(f"第 {chapter_number} 章（传统方式）修复后验证通过")
                            return fixed_content_attempt
                        else:
                            print(f"第 {chapter_number} 章（传统方式）修复后仍未通过: {recheck_result.get('issues', [])}")
                            # if still word count issue, retry if possible
                            is_insufficient_after_fix = False
                            for iss_recheck in recheck_result.get('issues', []):
                                if '字数不足' in iss_recheck.get('type',''):
                                    is_insufficient_after_fix = True; break
                            if is_insufficient_after_fix and retries < MAX_TRADITIONAL_RETRIES -1 : # -1 as retry will be incremented
                                print("修复后仍字数不足，重试传统生成...")
                                retries += 1
                                continue
                            return fixed_content_attempt # Return fixed (possibly still flawed) if not retrying for word count
                    else: # Fix failed
                        print("传统方式章节内容修复失败.")
                
                retries += 1 # Increment retry for the while loop if continue wasn't hit earlier for word count
            
            print(f"经过 {MAX_TRADITIONAL_RETRIES} 次传统方式重试后，仍未能生成符合要求的章节内容")
            return None
            
        except Exception as e:
            print(f"使用传统方式生成第 {chapter_number} 章时出错: {str(e)}")
            traceback.print_exc()
            return None
    
    def _format_chapter_outline(self, chapter_data: Dict[str, Any], chapter_number: int, total_chapters: int) -> str:
        """格式化章节大纲数据用于API"""
        title = chapter_data.get('title', f'第{chapter_number}章')
        chapter_summary_data = chapter_data.get('chapter_summary', {})
        
        formatted_outline = f"# 第{chapter_number}章 {title.replace(f'第{chapter_number}章', '').strip()}\n\n"
        formatted_outline += "## 章节概要\n\n"
        
        if chapter_summary_data.get('opening'):
            formatted_outline += f"### 开端\n{chapter_summary_data['opening']}\n\n"
        if chapter_summary_data.get('development'):
            formatted_outline += f"### 发展\n{chapter_summary_data['development']}\n\n"
        if chapter_summary_data.get('climax'):
            formatted_outline += f"### 高潮\n{chapter_summary_data['climax']}\n\n"
        if chapter_summary_data.get('ending'):
            formatted_outline += f"### 结尾\n{chapter_summary_data['ending']}\n\n"
        
        if chapter_data.get('characters'):
            formatted_outline += "## 出场人物\n"
            for char in chapter_data['characters']:
                formatted_outline += f"- **{char.get('name', '')}**: {char.get('actions', '')}\n"
            formatted_outline += "\n"
        
        if chapter_data.get('key_points'):
            formatted_outline += "## 关键情节点\n"
            for i, point in enumerate(chapter_data['key_points'], 1):
                formatted_outline += f"{i}. {point}\n"
            formatted_outline += "\n"
        
        if chapter_data.get('foreshadowings') and isinstance(chapter_data['foreshadowings'], dict):
            fs = chapter_data['foreshadowings']
            formatted_outline += "## 伏笔安排\n"
            if fs.get('planted'):
                formatted_outline += "- **新埋下**：\n"
                for plant in fs['planted']:
                    formatted_outline += f"  - {plant.get('id', '')}: {plant.get('content', '')}, 埋下方式: {plant.get('method', '')}\n"
            if fs.get('revealed'):
                formatted_outline += "- **回收的**：\n"
                for reveal in fs['revealed']:
                    formatted_outline += f"  - {reveal.get('id', '')}: {reveal.get('content', '')}, 回收效果: {reveal.get('effect', '')}\n"
        return formatted_outline
    
    def _format_characters(self, characters: List[Dict[str, Any]]) -> str:
        """格式化角色数据用于API"""
        if not characters:
            return "无"
        formatted_chars = "本章出场人物：\n"
        for char in characters:
            name = char.get('name', '')
            actions = char.get('actions', '')
            formatted_chars += f"- {name}: {actions}\n"
        return formatted_chars
    
    def _format_foreshadowings(self, foreshadowings: Dict[str, Any]) -> str:
        """格式化伏笔数据用于API"""
        if not foreshadowings:
            return "无"
        formatted_fs = "伏笔安排：\n"
        if foreshadowings.get('planted'):
            formatted_fs += "待埋下的伏笔：\n"
            for plant in foreshadowings['planted']:
                content = plant.get('content', '')
                method = plant.get('method', '')
                formatted_fs += f"- {content}（埋下方式：{method}）\n"
        if foreshadowings.get('revealed'):
            formatted_fs += "\n待回收的伏笔：\n"
            for reveal in foreshadowings['revealed']:
                content = reveal.get('content', '')
                effect = reveal.get('effect', '')
                formatted_fs += f"- {content}（回收效果：{effect}）\n"
        return formatted_fs
    
    def _check_chapter(self, chapter_content: str, chapter_outline: str, 
                       characters: str, foreshadowing: str, style_guide: str = "") -> Dict[str, Any]:
        """
        简化的章节验证，只检查大纲和风格，不检查和更新人物状态和伏笔状态
        """
        try:
            print("严格检查章节字数...")
            word_count_result = self.api.check_chapter_word_count(chapter_content)
            word_count = word_count_result.get("word_count", 0)
            print(f"章节字数: {word_count}字")
            
            if not word_count_result.get("passed", False):
                # issues = word_count_result.get("issues", []) # Not directly used here before return
                return word_count_result # Return immediately if word count fails

            if style_guide:
                print("检查章节风格...")
                style_result = self.api.check_chapter_style(chapter_content, style_guide)
                if not style_result.get("passed", False):
                    print(f"风格检查未通过: {style_result.get('issues', [])}")
                    return style_result
            else:
                print("跳过风格检查，因为没有提供风格指南...")
            
            print("按照需求跳过背景检查...")
            print("按照需求跳过角色状态检查和更新...")
            
            print("检查大纲冲突...")
            outline_result_json = self.api.check_chapter_outline(chapter_content, chapter_outline, foreshadowing)
            
            try:
                outline_result = json.loads(outline_result_json)
            except (json.JSONDecodeError, TypeError) as e:
                print(f"解析大纲检查结果时出错: {str(e)}")
                return {"passed": False, "issues": [{"type": "解析错误", "description": f"无法解析大纲检查结果: {str(e)}", "suggestion": "请重新检查"}]}
                
            if not outline_result.get("passed", False):
                print(f"大纲冲突检查未通过: {outline_result.get('issues', [])}")
                return outline_result
            
            print("章节验证通过!")
            return {"passed": True, "issues": []}
            
        except Exception as e:
            print(f"章节验证过程中出错: {str(e)}")
            traceback.print_exc()
            return {"passed": False, "issues": [{"type": "验证错误", "description": f"验证过程中出错: {str(e)}", "suggestion": "请检查章节内容并重试"}]}
    
    def fix_chapter(self, chapter_content: str, issues: List[Dict[str, Any]], 
                    chapter_outline: str, characters: str, foreshadowing: str, 
                    issue_type: str) -> Optional[str]:
        """
        根据提供的问题列表修复章节内容
        """
        try:
            fix_suggestions = []
            for issue in issues:
                suggestion = issue.get("suggestion", "")
                description = issue.get("description", "")
                fix_detail = ""
                if description: fix_detail += f"问题：{description}\n"
                if suggestion: fix_detail += f"建议：{suggestion}\n"
                if fix_detail: fix_suggestions.append(fix_detail)
            
            if not fix_suggestions:
                if "背景" in issue_type:
                    fix_suggestions = ["确保章节内容中的世界观、设定与背景信息保持一致", "移除或修改与已有背景设定冲突的内容"]
                elif "人物" in issue_type:
                    fix_suggestions = ["确保人物行为、能力与已有人物设定一致", "修正人物关系、动机或对话中的冲突或矛盾"]
                elif "大纲" in issue_type:
                    fix_suggestions = ["确保章节情节与大纲保持一致", "修正与伏笔、关键情节点相关的冲突"]
                else:
                    fix_suggestions = [f"修正{issue_type}中存在的问题", "确保内容的逻辑性和连贯性"]
            
            print(f"正在修复章节中的{issue_type}问题...")
            print(f"修复建议: {fix_suggestions}")
            
            fixed_content = self.api.fix_chapter_content(
                chapter_content, fix_suggestions, chapter_outline, 
                "", characters, foreshadowing, issue_type
            )
            
            if not fixed_content:
                print(f"修复{issue_type}问题失败")
                return None
            
            # import re # Moved to top
            fixed_content = re.sub(r'^（[^）]*）[\n\r]+', '', fixed_content)
            fixed_content = re.sub(r'^【[^】]*】[\n\r]+', '', fixed_content)
            fixed_content = re.sub(r'^---[\n\r]+', '', fixed_content)
            fixed_content = re.sub(r'\*\*[^*]+\*\*', '', fixed_content)
            fixed_content = re.sub(r'（[^）]*部分[^）]*）', '', fixed_content)
            fixed_content = re.sub(r'（[^）]*修复[^）]*）', '', fixed_content)
            fixed_content = re.sub(r'_(.+?)_', r'\1', fixed_content)
            fixed_content = re.sub(r'-{3,}[\n\r]+', '', fixed_content)
            fixed_content = re.sub(r'【[^】]*修复[^】]*】[\n\r]+', '', fixed_content)
            fixed_content = re.sub(r'【[^】]*调整[^】]*】[\n\r]+', '', fixed_content)
            fixed_content = re.sub(r'（[^）]*修复[^）]*）[\n\r]+', '', fixed_content)
            fixed_content = re.sub(r'（[^）]*调整[^）]*）[\n\r]+', '', fixed_content)
            
            cleaned_content = fixed_content.strip()
            if cleaned_content:
                last_char = cleaned_content[-1]
                if last_char not in '。！？.!?"\'》）)':
                    print(f"警告：修复后的章节内容可能不完整，最后一个字符是：{last_char}")
                    cleaned_content += "。"
            
            print(f"章节中的{issue_type}问题已修复")
            return cleaned_content
            
        except Exception as e:
            print(f"修复章节内容时出错: {str(e)}")
            traceback.print_exc()
            return None

    def continue_novel_generation(self, outline_path: str, output_dir: str = 'output_new') -> None:
        """
        从当前已生成的章节继续生成后续章节直到结束
        """
        os.makedirs(output_dir, exist_ok=True)
        self.output_dir = output_dir
        
        output_outline_path = os.path.join(output_dir, 'outline.json')
        if os.path.abspath(outline_path) != os.path.abspath(output_outline_path):
            shutil.copy(outline_path, output_outline_path)
            print(f"已将大纲文件复制到: {output_outline_path}")
        
        with open(outline_path, 'r', encoding='utf-8') as f:
            outline_data = json.load(f)
        
        style_guide_path = os.path.join(self.output_dir, 'style_guide.txt') # Use self.output_dir
        if not os.path.exists(style_guide_path):
            style_guide_path = os.path.join(os.path.dirname(outline_path), 'style_guide.txt')
        
        style_guide = ""
        if os.path.exists(style_guide_path):
            with open(style_guide_path, 'r', encoding='utf-8') as f:
                style_guide = f.read()
            print(f"已加载风格指南: {style_guide_path}")
        else:
            print("未找到风格指南文件，将使用空风格指南")
        
        chapters = outline_data.get('outlines', [])
        total_chapters = len(chapters)
        
        start_chapter_idx = 0
        for i, chapter_info in enumerate(chapters): # Use chapter_info to avoid conflict
            chapter_number_check = chapter_info.get('index', i + 1)
            chapter_file_check = os.path.join(output_dir, f'chapter_{chapter_number_check}.txt')
            if os.path.exists(chapter_file_check):
                start_chapter_idx = i + 1 
        
        if start_chapter_idx >= len(chapters):
            print(f"所有章节都已生成完成，共{total_chapters}章")
            return
            
        print(f"在大纲文件中找到 {total_chapters} 个章节")
        print(f"已生成 {start_chapter_idx} 章，将从第 {chapters[start_chapter_idx].get('index', start_chapter_idx + 1)} 章继续生成")
        print(f"输出目录: {output_dir}")
        
        for i, chapter_data_loop in enumerate(chapters[start_chapter_idx:], start_chapter_idx): # Loop index `i` is 0-based for the slice
            current_chapter_data = chapter_data_loop
            chapter_number = current_chapter_data.get('index', i + 1) # i is original index, so i+1 for 1-based chapter_number
            
            print(f"\n正在生成第 {chapter_number} 章 ({chapter_number}/{total_chapters})...") # Display chapter_number from data
            
            previous_chapters_list_data = None
            if chapter_number > 1 : # If current chapter_number is not the first
                # We need outline data of chapters from 0 up to index of (chapter_number - 1)
                # The original `chapters` list contains all outline data.
                # `start_chapter_idx` is the index in `chapters` from where we start generating.
                # `i` is the current index in `chapters` for `current_chapter_data`.
                # So, all chapters from `chapters[0]` to `chapters[i-1]` are previous.
                if i > 0 : # if not the very first chapter in the outline
                     previous_chapters_list_data = chapters[:i]


            chapter_content = self._generate_chapter(current_chapter_data, chapter_number, total_chapters, 
                                                     previous_chapters_list_data, style_guide)
            
            if chapter_content:
                chapter_file = os.path.join(output_dir, f'chapter_{chapter_number}.txt')
                # import re # Moved to top
                title_match = re.search(r'^(第.+?章\s*.+?)[\n\r]', chapter_content)
                if title_match:
                    title = title_match.group(1).strip()
                    clean_content = re.sub(r'^第.+?章\s*.+?[\n\r]+', '', chapter_content, 1)
                    markdown_title_pattern = re.compile(r'#\s*第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]')
                    clean_content = re.sub(markdown_title_pattern, '', clean_content)
                    clean_content = re.sub(r'[\n\r]+第[一二三四五六七八九十百千万\d]+章\s*[：:]*\s*.+?[\n\r]+', '\n\n', clean_content)
                    clean_content = re.sub(r'[\n\r]{3,}', '\n\n', clean_content).strip()
                    chapter_content = f"{title}\n\n{clean_content}"
                
                with open(chapter_file, 'w', encoding='utf-8') as f:
                    f.write(chapter_content)
                print(f"第 {chapter_number} 章已生成并保存到 {chapter_file}")
            else:
                print(f"生成第 {chapter_number} 章失败")
        
        print("\n所有剩余章节已成功生成！")
        
        # 自动导出小说
        print("\n正在导出小说...")
        # 尝试从大纲中获取标题
        story_title = outline_data.get('story_title', None)
        output_path = f"{story_title}.txt" if story_title else None
        
        success = export_novel_as_txt(output_path, output_dir)
        if success:
            print(f"小说导出成功！")
        else:
            print("小说导出失败。")

def generate_chapters_from_outline(outline_path: str, output_dir: str = 'output_new') -> None:
    """
    从大纲生成章节的主函数
    """
    generator = ChapterGenerator()
    generator.generate_all_chapters(outline_path, output_dir)

def generate_novel_style_and_background(genre: str, output_dir: str = 'output_new') -> bool:
    """
    生成写作风格和背景设定的主函数
    """
    generator = ChapterGenerator()
    return generator.generate_novel_from_outline(genre, output_dir)

def continue_novel_generation_entry(outline_path: str, output_dir: str = 'output_new') -> None: # Renamed to avoid conflict
    """
    从当前已生成的章节继续生成后续章节直到结束的主函数
    """
    generator = ChapterGenerator()
    generator.continue_novel_generation(outline_path, output_dir)

if __name__ == "__main__":
    import sys # Already imported at top but good practice in __main__ if not module level
    
    if len(sys.argv) > 1 and sys.argv[1] == "generate_style":
        genre = "玄幻" if len(sys.argv) <= 2 else sys.argv[2]
        output_dir_arg = "output_new" if len(sys.argv) <= 3 else sys.argv[3]
        print(f"生成{genre}流派小说的风格和背景设定...")
        print(f"输出目录: {output_dir_arg}")
        generate_novel_style_and_background(genre, output_dir_arg)
    elif len(sys.argv) > 1 and sys.argv[1] == "continue":
        outline_path_arg = "output_new/outline.json" if len(sys.argv) <= 2 else sys.argv[2]
        output_dir_arg = "output_new" if len(sys.argv) <= 3 else sys.argv[3]
        print(f"从当前章节继续生成小说: {outline_path_arg}")
        print(f"输出目录: {output_dir_arg}")
        continue_novel_generation_entry(outline_path_arg, output_dir_arg) # Use renamed function
    else:
        # Default action: generate all chapters from an outline
        outline_path_arg = "output_new/outline.json" # Default
        output_dir_arg = "output_new" # Default
        
        if len(sys.argv) > 1: # First arg is outline_path for default action
            outline_path_arg = sys.argv[1]
        if len(sys.argv) > 2: # Second arg is output_dir for default action
            output_dir_arg = sys.argv[2]
        
        print(f"从大纲生成章节: {outline_path_arg}")
        print(f"输出目录: {output_dir_arg}")
        generate_chapters_from_outline(outline_path_arg, output_dir_arg)
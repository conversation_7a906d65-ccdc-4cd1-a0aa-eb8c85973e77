"""
测试伏笔JSON解析功能
"""

import json
import sys
import os
import re
from typing import Dict, Any, Optional, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.foreshadowing import ForeshadowingManager, Foreshadowing
from utils.json_helper import parse_json_safely, fix_json, manual_json_parse

def test_standard_json():
    """测试标准JSON格式"""
    print("\n=== 测试标准JSON格式 ===")
    
    # 标准格式JSON
    standard_json = """
    {
        "foreshadowings": [
            {
                "id": "f1",
                "type": "主线",
                "content": "主角无意中发现一本古书",
                "plant_chapter": 3,
                "reveal_chapter": 15,
                "importance": "高",
                "plant_method": "意外发现",
                "reveal_effect": "揭示秘密",
                "status": "未埋下",
                "related_characters": ["主角", "老者"]
            },
            {
                "id": "f2",
                "type": "角色",
                "content": "副角色的秘密身份暗示",
                "plant_chapter": 5,
                "reveal_chapter": 20,
                "importance": "中",
                "plant_method": "对话中的暗示",
                "reveal_effect": "身份揭露",
                "status": "未埋下",
                "related_characters": ["副角色"]
            }
        ]
    }
    """
    
    # 使用安全的JSON解析函数
    foreshadowing_data = parse_json_safely(standard_json, "foreshadowing")
    
    if not foreshadowing_data:
        print("× 标准伏笔JSON解析失败")
        return False
    
    print(f"✓ 标准伏笔JSON解析成功")
    fs_count = len(foreshadowing_data.get("foreshadowings", []))
    print(f"解析到 {fs_count} 个伏笔")
    
    # 测试创建伏笔管理器
    try:
        manager = ForeshadowingManager()
        for fs_data in foreshadowing_data.get("foreshadowings", []):
            foreshadowing = Foreshadowing(
                id=fs_data.get("id", ""),
                type=fs_data.get("type", ""),
                content=fs_data.get("content", ""),
                plant_chapter=fs_data.get("plant_chapter", 0),
                reveal_chapter=fs_data.get("reveal_chapter", 0),
                importance=fs_data.get("importance", ""),
                plant_method=fs_data.get("plant_method", ""),
                reveal_effect=fs_data.get("reveal_effect", ""),
                status=fs_data.get("status", "未埋下"),
                related_characters=fs_data.get("related_characters", [])
            )
            manager.add_foreshadowing(foreshadowing)
        
        print(f"✓ 成功创建伏笔管理器，包含 {len(manager.foreshadowings)} 个伏笔")
        return True
    except Exception as e:
        print(f"× 创建伏笔管理器失败: {str(e)}")
        return False

def test_single_quotes():
    """测试使用单引号的JSON格式"""
    print("\n=== 测试使用单引号的JSON格式 ===")
    
    # 使用单引号的JSON格式
    single_quotes_json = """
    {
        'foreshadowings': [
            {
                'id': 'f1',
                'type': '主线',
                'content': '主角无意中发现一本古书',
                'plant_chapter': 3,
                'reveal_chapter': 15,
                'importance': '高',
                'plant_method': '意外发现',
                'reveal_effect': '揭示秘密',
                'status': '未埋下',
                'related_characters': ['主角', '老者']
            }
        ]
    }
    """
    
    # 使用安全的JSON解析函数
    foreshadowing_data = parse_json_safely(single_quotes_json, "foreshadowing")
    
    if not foreshadowing_data:
        print("× 单引号伏笔JSON解析失败")
        return False
    
    print(f"✓ 单引号伏笔JSON解析成功")
    fs_count = len(foreshadowing_data.get("foreshadowings", []))
    print(f"解析到 {fs_count} 个伏笔")
    return True

def test_missing_quotes():
    """测试缺少引号的JSON格式"""
    print("\n=== 测试缺少引号的JSON格式 ===")
    
    # 缺少引号的JSON格式
    missing_quotes_json = """
    {
        foreshadowings: [
            {
                id: "f1",
                type: "主线",
                content: "主角无意中发现一本古书",
                plant_chapter: 3,
                reveal_chapter: 15,
                importance: "高",
                plant_method: "意外发现",
                reveal_effect: "揭示秘密",
                status: "未埋下",
                related_characters: ["主角", "老者"]
            }
        ]
    }
    """
    
    # 使用安全的JSON解析函数
    foreshadowing_data = parse_json_safely(missing_quotes_json, "foreshadowing")
    
    if not foreshadowing_data:
        print("× 缺少引号伏笔JSON解析失败")
        return False
    
    print(f"✓ 缺少引号伏笔JSON解析成功")
    fs_count = len(foreshadowing_data.get("foreshadowings", []))
    print(f"解析到 {fs_count} 个伏笔")
    return True

def test_extra_commas():
    """测试多余逗号的JSON格式"""
    print("\n=== 测试多余逗号的JSON格式 ===")
    
    # 多余逗号的JSON格式
    extra_commas_json = """
    {
        "foreshadowings": [
            {
                "id": "f1",
                "type": "主线",
                "content": "主角无意中发现一本古书",
                "plant_chapter": 3,
                "reveal_chapter": 15,
                "importance": "高",
                "plant_method": "意外发现",
                "reveal_effect": "揭示秘密",
                "status": "未埋下",
                "related_characters": ["主角", "老者"],
            },
        ]
    }
    """
    
    # 使用安全的JSON解析函数
    foreshadowing_data = parse_json_safely(extra_commas_json, "foreshadowing")
    
    if not foreshadowing_data:
        print("× 多余逗号伏笔JSON解析失败")
        return False
    
    print(f"✓ 多余逗号伏笔JSON解析成功")
    fs_count = len(foreshadowing_data.get("foreshadowings", []))
    print(f"解析到 {fs_count} 个伏笔")
    return True

def test_double_quotes():
    """测试双重引号的JSON格式（DeepSeek API常见问题）"""
    print("\n=== 测试双重引号的JSON格式 ===")
    
    # 双重引号的JSON格式
    double_quotes_json = """
    { 
        ""foreshadowings"": [ 
            { 
                ""id"": "f1",
                ""type"": "主线",
                ""content"": "主角无意中发现一本古书",
                ""plant_chapter"": 3,
                ""reveal_chapter"": 15,
                ""importance"": "高",
                ""plant_method"": "意外发现",
                ""reveal_effect"": "揭示秘密",
                ""status"": "未埋下",
                ""related_characters"": ["主角", "老者"]
            }
        ] 
    }
    """
    
    # 使用安全的JSON解析函数
    foreshadowing_data = parse_json_safely(double_quotes_json, "foreshadowing")
    
    if not foreshadowing_data:
        print("× 双重引号伏笔JSON解析失败")
        return False
    
    print(f"✓ 双重引号伏笔JSON解析成功")
    fs_count = len(foreshadowing_data.get("foreshadowings", []))
    print(f"解析到 {fs_count} 个伏笔")
    return True

def test_missing_braces():
    """测试缺少括号的JSON格式"""
    print("\n=== 测试缺少括号的JSON格式 ===")
    
    # 这个格式缺少了最外层的大括号
    missing_braces_json = """
    "foreshadowings": [
        {
            "id": "f1",
            "type": "主线",
            "content": "主角无意中发现一本古书",
            "plant_chapter": 3,
            "reveal_chapter": 15,
            "importance": "高",
            "plant_method": "意外发现",
            "reveal_effect": "揭示秘密",
            "status": "未埋下",
            "related_characters": ["主角", "老者"]
        }
    ]
    """
    
    # 使用安全的JSON解析函数
    foreshadowing_data = parse_json_safely(missing_braces_json, "foreshadowing")
    
    if not foreshadowing_data or "foreshadowings" not in foreshadowing_data:
        print("× 缺少括号伏笔JSON解析失败")
        return False
    
    print(f"✓ 缺少括号伏笔JSON解析成功")
    fs_count = len(foreshadowing_data.get("foreshadowings", []))
    print(f"解析到 {fs_count} 个伏笔")
    return True

def test_only_array():
    """测试只有数组的JSON格式"""
    print("\n=== 测试只有数组的JSON格式 ===")
    
    # 只有数组的JSON格式
    array_only_json = """
    [
        {
            "id": "f1",
            "type": "主线",
            "content": "主角无意中发现一本古书",
            "plant_chapter": 3,
            "reveal_chapter": 15,
            "importance": "高",
            "plant_method": "意外发现", 
            "reveal_effect": "揭示秘密",
            "status": "未埋下",
            "related_characters": ["主角", "老者"]
        },
        {
            "id": "f2",
            "type": "角色",
            "content": "副角色的秘密身份暗示",
            "plant_chapter": 5,
            "reveal_chapter": 20,
            "importance": "中",
            "plant_method": "对话中的暗示",
            "reveal_effect": "身份揭露",
            "status": "未埋下",
            "related_characters": ["副角色"]
        }
    ]
    """
    
    # 使用安全的JSON解析函数
    foreshadowing_data = parse_json_safely(array_only_json, "foreshadowing")
    
    if not foreshadowing_data or "foreshadowings" not in foreshadowing_data:
        print("× 只有数组的伏笔JSON解析失败")
        return False
    
    print(f"✓ 只有数组的伏笔JSON解析成功")
    fs_count = len(foreshadowing_data.get("foreshadowings", []))
    print(f"解析到 {fs_count} 个伏笔")
    return True

def test_mixed_issues():
    """测试混合问题的JSON格式"""
    print("\n=== 测试混合问题的JSON格式 ===")
    
    # 混合多种格式问题的JSON
    mixed_issues_json = """
    { 
        'foreshadowings': [ 
            { 
                id: "f1",
                "type": '主线',
                content: "主角无意中发现一本古书",
                plant_chapter: 3,
                reveal_chapter: 15,
                'importance': "高",
                plant_method: '意外发现',
                reveal_effect: "揭示秘密",
                status: "未埋下",
                'related_characters': ["主角", '老者'],
            }, 
        ] 
    }
    """
    
    # 使用安全的JSON解析函数
    foreshadowing_data = parse_json_safely(mixed_issues_json, "foreshadowing")
    
    if not foreshadowing_data:
        print("× 混合问题伏笔JSON解析失败")
        return False
    
    print(f"✓ 混合问题伏笔JSON解析成功")
    fs_count = len(foreshadowing_data.get("foreshadowings", []))
    print(f"解析到 {fs_count} 个伏笔")
    return True

def test_corrupted_json():
    """测试严重损坏的JSON格式"""
    print("\n=== 测试严重损坏的JSON格式 ===")
    
    # 严重损坏的JSON
    corrupted_json = """
    { 
        foreshadowings: 
            { 
                id: f1,
                type: 主线
                content: "主角无意中发现一本古书
                plant_chapter: 3
                reveal_chapter: 15
                importance: 高
                plant_method: 意外发现
                reveal_effect: 揭示秘密
                status: 未埋下
                related_characters: [主角, 老者]
            } 
        } 
    }
    """
    
    # 使用安全的JSON解析函数
    foreshadowing_data = parse_json_safely(corrupted_json, "foreshadowing")
    
    # 这种情况我们至少期望返回一个有效的空结构
    if not foreshadowing_data or "foreshadowings" not in foreshadowing_data:
        print("× 严重损坏的JSON解析失败")
        return False
    
    print(f"✓ 严重损坏的JSON解析成功处理")
    fs_count = len(foreshadowing_data.get("foreshadowings", []))
    print(f"解析到 {fs_count} 个伏笔")
    return True

def run_all_tests():
    """运行所有测试"""
    tests = [
        test_standard_json,
        test_single_quotes,
        test_missing_quotes,
        test_extra_commas,
        test_double_quotes,
        test_missing_braces,
        test_only_array,
        test_mixed_issues,
        test_corrupted_json
    ]
    
    total = len(tests)
    passed = 0
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n测试结果: {passed}/{total} 通过")
    return passed == total

if __name__ == "__main__":
    run_all_tests() 
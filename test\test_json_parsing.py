#!/usr/bin/env python3
"""
测试JSON解析功能
"""

import os
import sys
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.json_helper import parse_json_safely, fix_json

def test_json_parsing():
    """测试通用JSON解析功能"""
    print("\n========== 测试通用JSON解析功能 ==========")
    
    test_cases = [
        # 正常的JSON
        ('{"name": "测试", "value": 123}', "标准JSON"),
        # 单引号代替双引号
        ("{'name': '测试', 'value': 123}", "单引号JSON"),
        # 引号未转义的JSON
        ('{"text": "引号"测试"未转义"}', "引号未转义"),
        # 属性名未加引号
        ('{name: "测试", value: 123}', "属性名无引号"),
        # 布尔值和null
        ('{"valid": True, "empty": None}', "布尔值和null"),
        # 常见错误：多余逗号
        ('{"name": "测试", "value": 123,}', "多余逗号"),
        # 常见错误：缺少逗号
        ('{"name": "测试" "value": 123}', "缺少逗号"),
        # 嵌套结构缺少逗号
        ('{"obj": {"a": 1 "b": 2}, "arr": [1 2 3]}', "嵌套结构缺少逗号"),
        # 复杂嵌套结构
        ('{"obj": {"a": {"x": 1 "y": 2} "b": [1 2]}, "arr": [{"k": "v"} {"k2": "v2"}]}', "复杂嵌套结构缺少逗号"),
        # 对象作为数组元素
        ('[{"a": 1} {"b": 2}]', "数组中的对象元素间缺少逗号"),
        # 特殊错误：在某一行特定列缺少逗号
        ('{\n  "field1": "value1"\n  "field2": "value2"\n}', "特定行列缺少逗号")
    ]
    
    success_count = 0
    for json_str, case_name in test_cases:
        print(f"\n测试案例: {case_name}")
        print(f"输入: {json_str}")
        
        result = parse_json_safely(json_str, case_name)
        if result:
            success_count += 1
            print(f"[成功] 解析成功: {result}")
        else:
            print(f"[失败] 解析失败")
    
    print(f"\n总体结果: {success_count}/{len(test_cases)} 测试通过 ({success_count/len(test_cases)*100:.1f}%)")
    return success_count == len(test_cases)

def test_complex_real_world_json():
    """测试复杂的真实世界JSON解析"""
    print("\n========== 测试复杂真实世界JSON解析 ==========")
    
    # 模拟DeepSeek API返回的常见错误JSON格式
    test_cases = [
        # 1. 嵌套对象中缺少逗号
        ('''
        {
          "story_title": "测试小说"
          "outlines": [
            {
              "index": 1
              "title": "第一章"
              "content": "内容..."
            }
            {
              "index": 2
              "title": "第二章"
              "content": "更多内容..."
            }
          ]
        }
        ''', "故事大纲JSON缺少逗号"),
        
        # 2. 数组元素之间缺少逗号
        ('''
        {
          "characters": [
            {"name": "角色1", "age": 25}
            {"name": "角色2", "age": 30}
          ]
        }
        ''', "角色数组缺少逗号"),
        
        # 3. 混合多种问题
        ('''
        {
          "title": "测试文档"
          "sections": [
            {
              "name": "部分1"
              "content": "这是第一部分内容"
              "subsections": [
                {"title": "1.1" "text": "子部分内容"}
                {"title": "1.2" "text": "更多子部分内容"}
              ]
            }
            {
              "name": "部分2"
              "content": "这是第二部分内容"
            }
          ]
          "metadata": {
            "author": "测试作者"
            "date": "2023-05-01"
            "tags": ["测试" "JSON" "解析"]
          }
        }
        ''', "混合多种逗号缺失问题")
    ]
    
    success_count = 0
    for json_str, case_name in test_cases:
        print(f"\n测试案例: {case_name}")
        
        result = parse_json_safely(json_str, case_name)
        if result:
            success_count += 1
            print(f"[成功] 解析成功")
        else:
            print(f"[失败] 解析失败")
    
    print(f"\n总体结果: {success_count}/{len(test_cases)} 测试通过 ({success_count/len(test_cases)*100:.1f}%)")
    return success_count == len(test_cases)

if __name__ == "__main__":
    basic_success = test_json_parsing()
    complex_success = test_complex_real_world_json()
    
    print(f"\n基础测试结果: {'成功' if basic_success else '失败'}")
    print(f"复杂测试结果: {'成功' if complex_success else '失败'}")
    
    overall_success = basic_success and complex_success
    print(f"总体测试结果: {'成功' if overall_success else '失败'}")
    
    sys.exit(0 if overall_success else 1) 
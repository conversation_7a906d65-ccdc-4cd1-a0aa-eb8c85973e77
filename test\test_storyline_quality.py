#!/usr/bin/env python3
"""
测试故事主线质量检查功能
"""

import os
import sys
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.fix_storyline import validate_storyline_quality

def test_with_real_data():
    """测试真实的故事主线数据"""
    print("测试真实的故事主线数据:")
    try:
        # 尝试读取真实的故事主线数据
        file_path = os.path.join("output", "main_storyline.json")
        if os.path.exists(file_path):
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                print(f"已读取{file_path}，包含{len(data.get('outlines', []))}章")
                
                result = validate_storyline_quality(data)
                print("\n验证结果:")
                print(f"是否有效: {result['valid']}")
                print(f"总字数: {result['total_length']}")
                
                if not result['valid']:
                    print(f"问题: {result['issues']}")
        else:
            print(f"文件不存在: {file_path}")
    except Exception as e:
        print(f"测试失败: {e}")

def test_with_mock_data():
    """使用模拟数据测试边界情况"""
    print("\n测试模拟数据:")
    
    # 测试场景1: 字数不足的故事主线
    test_case1 = {
        "story_title": "测试小说",
        "outlines": [
            {"index": 1, "title": "第一章", "content": "很短"},
            {"index": 2, "title": "第二章", "content": "也很短"}
        ]
    }
    
    result1 = validate_storyline_quality(test_case1)
    print("\n测试场景1: 字数不足")
    print(f"是否有效: {result1['valid']}")
    print(f"总字数: {result1['total_length']}")
    if not result1['valid']:
        print(f"问题: {result1['issues']}")
    
    # 测试场景2: 使用泛称的故事主线
    test_case2 = {
        "story_title": "测试小说",
        "outlines": [
            {"index": 1, "title": "第一章", "content": "男主进入了修真门派，开始了修炼之路。女主也在同一时期加入了另一个门派。"},
            {"index": 2, "title": "第二章", "content": "男主在修炼中遇到了困难，但他坚持不懈。女主展现出了惊人的天赋，引起了宗门高层的注意。"}
        ]
    }
    
    result2 = validate_storyline_quality(test_case2)
    print("\n测试场景2: 使用泛称")
    print(f"是否有效: {result2['valid']}")
    print(f"总字数: {result2['total_length']}")
    if not result2['valid']:
        print(f"问题: {result2['issues']}")
    
    # 测试场景3: 不使用泛称的故事主线
    test_case3 = {
        "story_title": "测试小说",
        "outlines": [
            {"index": 1, "title": "第一章", "content": "李阳进入了修真门派，开始了修炼之路。林雪也在同一时期加入了另一个门派。"},
            {"index": 2, "title": "第二章", "content": "李阳在修炼中遇到了困难，但他坚持不懈。林雪展现出了惊人的天赋，引起了宗门高层的注意。"}
        ]
    }
    
    result3 = validate_storyline_quality(test_case3)
    print("\n测试场景3: 不使用泛称")
    print(f"是否有效: {result3['valid']}")
    print(f"总字数: {result3['total_length']}")
    if not result3['valid']:
        print(f"问题: {result3['issues']}")

if __name__ == "__main__":
    print("开始测试故事主线质量检查功能...")
    
    # 测试真实数据
    test_with_real_data()
    
    # 测试模拟数据
    test_with_mock_data()
    
    print("\n测试完成!") 
import sys
from typing import Optional, Dict, Any, List

# 模拟DeepSeekAllAPI类
class MockDeepSeekAllAPI:
    """模拟DeepSeekAllAPI类，仅用于参数测试"""
    
    def generate_chapter_segment(self, segment_type: str, chapter_number: int,
                                 chapter_outline: str, background: str,
                                 characters: str, foreshadowing: str,
                                 previous_content: Optional[str] = None) -> Optional[str]:
        """
        模拟生成章节片段方法，只测试参数数量

        Args:
            segment_type: 片段类型（开端/发展/高潮/结尾）
            chapter_number: 章节号
            chapter_outline: 章节大纲
            background: 背景设定
            characters: 人物卡片
            foreshadowing: 伏笔管理
            previous_content: 前面已生成的内容或前一章结尾内容（可选）

        Returns:
            章节片段，如果出错则返回None
        """
        print(f"测试调用 generate_chapter_segment 参数数量: {7}")  # 7个参数
        return "模拟生成内容"

def test_api_calls():
    # 使用模拟API而不是真实API
    api = MockDeepSeekAllAPI()
    
    # 替换实际方法，只记录参数
    original_method = api.generate_chapter_segment
    called_args = []
    
    def mock_generate_chapter_segment(*args, **kwargs):
        called_args.append((args, kwargs))
        print(f"测试调用 generate_chapter_segment 参数数量: {len(args)}")
        return "模拟生成内容"
    
    api.generate_chapter_segment = mock_generate_chapter_segment
    
    # 准备测试数据
    segment_type = "开端"
    chapter_number = 1
    chapter_outline = "测试大纲"
    background = "测试背景"
    characters = "测试人物"
    foreshadowing = "测试伏笔"
    previous_content = "前一章内容"
    
    # 正确的调用方式
    print("\n===== 正确参数数量 =====")
    result = api.generate_chapter_segment(
        segment_type,
        chapter_number,
        chapter_outline,
        background,
        characters,
        foreshadowing,
        previous_content
    )
    
    # 多传一个参数（错误的调用方式）
    print("\n===== 参数数量过多 =====")
    try:
        result = api.generate_chapter_segment(
            segment_type,
            chapter_number,
            chapter_outline,
            background,
            characters,
            foreshadowing,
            previous_content,
            "额外参数"  # 多余的参数
        )
    except TypeError as e:
        print(f"捕获到预期的TypeError: {e}")
    
    # 测试参数整合
    print("\n===== 参数整合测试 =====")
    generation_instruction = "请确保最终章节内容至少5000字，详细展开情节，使字数充足。"
    enhanced_previous_content = f"{previous_content}\n\n【生成指示】：{generation_instruction}"
    
    result = api.generate_chapter_segment(
        segment_type,
        chapter_number,
        chapter_outline,
        background,
        characters,
        foreshadowing,
        enhanced_previous_content  # 使用整合了生成指示的内容
    )
    
    print("\n测试完成")

if __name__ == "__main__":
    test_api_calls() 
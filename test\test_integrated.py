#!/usr/bin/env python3
"""
整合测试，测试完整的JSON解析流程
"""

import os
import sys
import json
import traceback

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.json_helper import parse_json_safely
from utils.fix_storyline import load_storyline, get_chapter_from_storyline
from utils.file_manager import save_main_storyline, load_main_storyline

def test_storyline_conversion():
    """测试故事主线数据的解析和转换"""
    print("\n========== 测试故事主线数据的解析和转换 ==========")
    
    # 准备测试数据，使用未转义的引号和其他格式问题
    test_content = '''{
        "story_title": "引号"测试"小说",
        "outlines": [
            {"index": 1, "title": "第"一"章", "content": "内容1"},
            {"index": 2, "title": "第二章", "content": "内容2"},
            {"index": 3, "title": "第三章", "content": "这是"引号内容"测试"}
        ]
    }'''
    
    try:
        # 测试1: 保存和加载故事主线
        print("\n测试1: 保存和加载故事主线")
        save_result = save_main_storyline(test_content, 3)
        if save_result:
            print("保存成功")
            
            # 加载故事主线
            storyline = load_main_storyline(3)
            if storyline and isinstance(storyline, dict):
                print("加载成功")
                print(f"故事标题: {storyline.get('story_title', '未找到')}")
                
                # 获取章节
                print("\n查找章节信息:")
                for i in range(1, 4):
                    chapter = get_chapter_from_storyline(storyline, i)
                    if chapter:
                        print(f"第{i}章: {chapter.get('title', '无标题')}")
                
                # 测试缩减后的变量名
                outlines = storyline.get("outlines", [])
                outlines_len = len(outlines) if isinstance(outlines, list) else 0
                print(f"\n使用outlines字段，共{outlines_len}章")
                
                chapter_outlines = storyline.get("chapter_outlines", [])
                co_len = len(chapter_outlines) if isinstance(chapter_outlines, list) else 0
                print(f"使用chapter_outlines字段，共{co_len}章")
                
                if outlines_len == co_len == 3:
                    print("\n测试通过: 两种字段名都能正确访问章节数据")
                    
                    # 检查章节索引字段
                    if outlines and isinstance(outlines[0], dict):
                        index = outlines[0].get("index")
                        chapter_number = outlines[0].get("chapter_number")
                        print(f"第一章索引值: index={index}, chapter_number={chapter_number}")
                        
                        if index == chapter_number == 1:
                            print("测试通过: index和chapter_number字段同时存在且值一致")
                        else:
                            print("测试失败: index和chapter_number字段不一致")
                    
                    return True
                else:
                    print(f"测试失败: 章节数量不符，outlines={outlines_len}, chapter_outlines={co_len}")
            else:
                print("加载失败")
        else:
            print("保存失败")
        
        return False
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = test_storyline_conversion()
        print(f"\n总体测试结果: {'成功' if success else '失败'}")
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"测试脚本执行过程中发生错误: {str(e)}")
        traceback.print_exc()
        sys.exit(2) 
#!/usr/bin/env python3
"""
测试章节数量验证功能
"""

import os
import sys
import json
import traceback

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.fix_storyline import fix_storyline_json

def test_chapter_validation():
    """测试章节数量验证功能"""
    print("\n========== 测试章节数量验证功能 ==========")
    
    # 创建一个简单的测试文件
    temp_file = "temp_test_storyline.json"
    
    # 准备测试数据
    test_data = {
        "story_title": "测试小说",
        "outlines": [
            {"index": 1, "title": "第一章", "content": "内容1"},
            {"index": 2, "title": "第二章", "content": "内容2"},
            {"index": 3, "title": "第三章", "content": "内容3"}
        ]
    }
    
    try:
        # 写入测试文件
        with open(temp_file, "w", encoding="utf-8") as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print("已创建测试文件，包含3章内容")
        
        # 测试1: 正确的章节数量
        print("\n测试1: 正确的章节数量验证 (期望3章)")
        result1 = fix_storyline_json(temp_file, 3)
        validation1 = bool(result1)
        print(f"测试1结果: {'通过' if validation1 else '失败'}")
        
        # 测试2: 错误的章节数量
        print("\n测试2: 错误的章节数量验证 (期望5章)")
        result2 = fix_storyline_json(temp_file, 5)
        validation2 = not bool(result2)  # 应该返回空字典表示验证失败
        print(f"测试2结果: {'通过' if validation2 else '失败'}")
        
        # 测试3: 无章节数量要求
        print("\n测试3: 无章节数量要求")
        result3 = fix_storyline_json(temp_file)
        validation3 = bool(result3)
        print(f"测试3结果: {'通过' if validation3 else '失败'}")
        
        # 删除测试文件
        try:
            os.remove(temp_file)
            print("\n已删除测试文件")
        except:
            print("\n警告: 无法删除测试文件")
        
        all_passed = validation1 and validation2 and validation3
        print(f"\n总体结果: {'全部通过' if all_passed else '部分失败'}")
        return all_passed
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        traceback.print_exc()
        
        # 确保清理测试文件
        if os.path.exists(temp_file):
            try:
                os.remove(temp_file)
                print("已删除测试文件")
            except:
                pass
        
        return False

if __name__ == "__main__":
    success = test_chapter_validation()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1) 
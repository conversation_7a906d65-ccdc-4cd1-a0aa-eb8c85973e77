#!/usr/bin/env python3
"""
测试故事主线JSON验证和修复功能
"""

import os
import sys
import json
import tempfile
import shutil

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.json_helper import parse_json_safely, fix_json
from utils.fix_storyline import fix_storyline_json, validate_storyline_data

def test_fix_storyline():
    """测试storyline修复和验证功能"""
    print("\n========== 测试故事主线修复和验证功能 ==========")
    
    # 创建临时目录存放测试文件
    temp_dir = tempfile.mkdtemp()
    try:
        # 测试用例1：正常的JSON
        valid_json = {
            "story_title": "测试小说",
            "outlines": [
                {"index": 1, "title": "第一章", "content": "这是第一章的内容摘要，长度足够测试"},
                {"index": 2, "title": "第二章", "content": "这是第二章的内容摘要，长度足够测试"},
                {"index": 3, "title": "第三章", "content": "这是第三章的内容摘要，长度足够测试"}
            ]
        }
        
        # 测试用例2：缺少逗号的JSON
        invalid_json_missing_comma = '''
        {
          "story_title": "测试小说"
          "outlines": [
            {
              "index": 1
              "title": "第一章"
              "content": "这是第一章的内容"
            }
            {
              "index": 2
              "title": "第二章"
              "content": "这是第二章的内容"
            }
            {
              "index": 3
              "title": "第三章"
              "content": "这是第三章的内容"
            }
          ]
        }
        '''
        
        # 测试用例3：章节索引不连续
        invalid_json_non_continuous = {
            "story_title": "测试小说",
            "outlines": [
                {"index": 1, "title": "第一章", "content": "这是第一章的内容摘要，长度足够测试"},
                {"index": 3, "title": "第三章", "content": "这是第三章的内容摘要，长度足够测试"},
                {"index": 4, "title": "第四章", "content": "这是第四章的内容摘要，长度足够测试"}
            ]
        }
        
        # 测试用例4：章节数量不匹配
        invalid_json_wrong_count = {
            "story_title": "测试小说",
            "outlines": [
                {"index": 1, "title": "第一章", "content": "这是第一章的内容摘要，长度足够测试"},
                {"index": 2, "title": "第二章", "content": "这是第二章的内容摘要，长度足够测试"}
            ]
        }
        
        # 测试用例5：缺少必要字段
        invalid_json_missing_fields = {
            "story_title": "测试小说",
            "outlines": [
                {"index": 1, "title": "第一章"},
                {"index": 2, "title": "第二章", "content": "这是第二章的内容摘要，长度足够测试"},
                {"index": 3, "content": "这是第三章的内容摘要，长度足够测试"}
            ]
        }
        
        # 测试用例6：乱码文件
        invalid_json_corrupted = "{\xff\xfe\u0000\u0000story_title\"错误的编码}"
        
        # 保存测试文件
        test_cases = [
            ("valid.json", json.dumps(valid_json, ensure_ascii=False), True),
            ("missing_comma.json", invalid_json_missing_comma, True), # 应该可以修复
            ("non_continuous.json", json.dumps(invalid_json_non_continuous, ensure_ascii=False), False),
            ("wrong_count.json", json.dumps(invalid_json_wrong_count, ensure_ascii=False), False, 3), # 预期3章，但只有2章
            ("missing_fields.json", json.dumps(invalid_json_missing_fields, ensure_ascii=False), False),
            ("corrupted.json", invalid_json_corrupted, False)
        ]
        
        for idx, (filename, content, expected_result, *args) in enumerate(test_cases):
            file_path = os.path.join(temp_dir, filename)
            expected_chapters = args[0] if args else None
            
            print(f"\n测试用例 {idx+1}: {filename}")
            
            # 写入测试文件
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            except:
                # 对于乱码，尝试用二进制模式写入
                with open(file_path, 'wb') as f:
                    f.write(content.encode('utf-8', errors='ignore'))
            
            # 测试修复功能
            result = fix_storyline_json(file_path, expected_chapters)
            is_successful = bool(result)  # 空字典表示失败
            
            if is_successful == expected_result:
                print(f"[测试通过] 预期结果: {expected_result}, 实际结果: {is_successful}")
            else:
                print(f"[测试失败] 预期结果: {expected_result}, 实际结果: {is_successful}")
            
            # 如果测试用例1（有效JSON）成功，进一步测试验证函数
            if idx == 0 and is_successful:
                print("\n测试验证函数:")
                validation_result = validate_storyline_data(result, expected_chapters)
                print(f"验证结果: {'通过' if validation_result else '失败'}")
    
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)
    
    print("\n========== 测试故事主线数据结构修复 ==========")
    
    # 测试直接从字符串修复
    test_strings = [
        # 正常JSON
        ('{"story_title": "测试", "outlines": [{"index": 1, "title": "t", "content": "c"}]}', True),
        # 缺少逗号
        ('{"story_title": "测试" "outlines": [{"index": 1 "title": "t" "content": "c"}]}', True),
        # 内容过短
        ('{"story_title": "测试", "outlines": [{"index": 1, "title": "t", "content": ""}]}', False),
        # 结构错误
        ('{"story_title": "测试", "outlines": "不是数组"}', False)
    ]
    
    for idx, (json_str, expected_result) in enumerate(test_strings):
        print(f"\n字符串测试 {idx+1}:")
        print(f"输入: {json_str[:30]}..." if len(json_str) > 30 else f"输入: {json_str}")
        
        data = parse_json_safely(json_str, "测试")
        if data:
            validation_result = validate_storyline_data(data)
            if validation_result == expected_result:
                print(f"[测试通过] 预期结果: {expected_result}, 实际结果: {validation_result}")
            else:
                print(f"[测试失败] 预期结果: {expected_result}, 实际结果: {validation_result}")
        else:
            if not expected_result:
                print(f"[测试通过] 预期无法解析，实际确实解析失败")
            else:
                print(f"[测试失败] 预期能解析，但解析失败")

if __name__ == "__main__":
    test_fix_storyline()
    print("\n所有测试完成") 
"""
测试fix_storyline.py模块的功能
"""

import os
import sys
import json
import traceback
from utils.fix_storyline import fix_storyline_json, get_chapter_from_storyline

def test_fix_storyline():
    """测试fix_storyline模块的修复功能"""
    print("========== 测试fix_storyline模块 ==========")
    
    # 测试情况1：有效的JSON
    test_valid_json = '''
    {
      "story_title": "轮回道主",
      "outlines": [
        {"index": 1, "title": "测试章节1", "content": "内容1"},
        {"index": 2, "title": "测试章节2", "content": "内容2"}
      ]
    }
    '''
    
    print("\n测试1：有效的JSON")
    result = fix_storyline_json(test_valid_json)
    print(f"修复结果: 包含 {len(result.get('outlines', []))} 个章节")
    
    # 测试情况2：修复引号问题
    test_quote_json = '''
    {
      "story_title": "引号"测试"小说",
      "outlines": [
        {"index": 1, "title": "第"一"章", "content": "内容1"},
        {"index": 2, "title": "第二章", "content": "内容2"}
      ]
    }
    '''
    
    print("\n测试2：引号问题")
    result = fix_storyline_json(test_quote_json)
    print(f"修复结果: 包含 {len(result.get('outlines', []))} 个章节")
    
    # 测试情况3：逗号问题
    test_comma_json = '''
    {
      "story_title": "逗号问题测试"
      "outlines": [
        {"index": 1, "title": "第一章" "content": "内容1"},
        {"index": 2, "title": "第二章", "content": "内容2"}
      ]
    }
    '''
    
    print("\n测试3：逗号问题")
    result = fix_storyline_json(test_comma_json)
    print(f"修复结果: 包含 {len(result.get('outlines', []))} 个章节")
    
    # 测试情况4：严重损坏的JSON
    test_broken_json = '''
    "story_title": "损坏的JSON",
    "outlines": 
      {"index": 1, "title": 第一章, "content": 内容1},
      {"index": 2, "title": 第二章, "content": 内容2},
    ]
    '''
    
    print("\n测试4：严重损坏的JSON")
    result = fix_storyline_json(test_broken_json)
    print(f"修复结果: {result}")
    
    # 测试情况5：从文件加载的测试
    print("\n测试5：从main_storyline.json文件加载")
    try:
        from config import OUTPUT_DIR
        main_storyline_path = os.path.join(OUTPUT_DIR, "main_storyline.json")
        if os.path.exists(main_storyline_path):
            with open(main_storyline_path, "r", encoding="utf8") as f:
                main_storyline_content = f.read()
            
            result = fix_storyline_json(main_storyline_content)
            print(f"修复结果: 包含 {len(result.get('outlines', []))} 个章节")
            
            # 测试获取特定章节
            chapter_number = 1
            chapter_data = get_chapter_from_storyline(result, chapter_number)
            if chapter_data:
                print(f"成功获取第{chapter_number}章: {chapter_data.get('title')}")
            else:
                print(f"无法获取第{chapter_number}章")
        else:
            print(f"文件不存在: {main_storyline_path}")
    except Exception as e:
        print(f"从文件加载测试失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_fix_storyline() 
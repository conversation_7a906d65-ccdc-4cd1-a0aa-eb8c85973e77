"""
简单测试JSON清理功能
"""

import sys
import os
import re
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def clean_json_string(json_str, aggressive=False):
    """
    清理JSON字符串，修复常见的格式问题
    
    Args:
        json_str: 原始JSON字符串
        aggressive: 是否使用更激进的清理方法
        
    Returns:
        清理后的JSON字符串
    """
    # 记录原始JSON字符串（截断以避免日志过长）
    if len(json_str) > 200:
        print(f"原始JSON（前200字符）: {json_str[:200]}...")
    else:
        print(f"原始JSON: {json_str}")
        
    # 记录是否使用激进模式
    print(f"使用{'激进' if aggressive else '常规'}清理模式")
    
    # 移除注释
    json_str = re.sub(r'//.*?(\n|$)', '', json_str)
    
    # 处理换行和缩进问题
    if aggressive:
        # 更激进的清理：移除所有换行和多余空格
        original = json_str
        json_str = re.sub(r'\s+', ' ', json_str)
        if original != json_str:
            print("应用激进清理: 移除所有换行和多余空格")
            
        # 在冒号后添加一个空格
        original = json_str
        json_str = re.sub(r':\s*', ': ', json_str)
        if original != json_str:
            print("应用激进清理: 规范化冒号后的空格")
            
        # 在逗号后添加一个空格
        original = json_str
        json_str = re.sub(r',\s*', ', ', json_str)
        if original != json_str:
            print("应用激进清理: 规范化逗号后的空格")
    else:
        # 常规清理：规范化换行和缩进
        original = json_str
        json_str = re.sub(r'\n\s*"', ' "', json_str)
        if original != json_str:
            print("应用常规清理: 规范化换行和缩进")
    
    # 修复引号问题
    original = json_str
    json_str = json_str.replace('\\"', '"')  # 移除转义的引号
    if original != json_str:
        print("修复: 移除转义的引号")
        
    original = json_str
    json_str = json_str.replace('""', '"')   # 修复双引号问题
    if original != json_str:
        print("修复: 修复双引号问题")
    
    # 修复属性名问题
    original = json_str
    json_str = re.sub(r'{\s*([^"{\s][^:]*?):', r'{ "\1":', json_str)  # 修复开头的属性名
    if original != json_str:
        print("修复: 修复开头的属性名")
        
    original = json_str
    json_str = re.sub(r',\s*([^"{\s][^:]*?):', r', "\1":', json_str)  # 修复中间的属性名
    if original != json_str:
        print("修复: 修复中间的属性名")
    
    # 修复多余的逗号
    original = json_str
    json_str = re.sub(r',\s*}', '}', json_str)  # 移除对象末尾多余的逗号
    if original != json_str:
        print("修复: 移除对象末尾多余的逗号")
        
    original = json_str
    json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组末尾多余的逗号
    if original != json_str:
        print("修复: 移除数组末尾多余的逗号")
    
    # 修复单引号问题
    original = json_str
    json_str = json_str.replace("'", '"')
    if original != json_str:
        print("修复: 将单引号替换为双引号")
    
    # 修复特殊问题：'\n          "title"' 这样的格式
    original = json_str
    json_str = re.sub(r'\\n\s+"', ' "', json_str)
    if original != json_str:
        print("修复: 处理特殊的换行+空格+引号格式")
    
    # 记录清理后的JSON字符串（截断以避免日志过长）
    if len(json_str) > 200:
        print(f"清理后JSON（前200字符）: {json_str[:200]}...")
    else:
        print(f"清理后JSON: {json_str}")
        
    return json_str

def test_clean_json_string():
    """测试JSON清理功能"""
    # 测试特殊的换行+空格+引号格式
    problem_json = '{"title\\n          "title": "测试标题", "content": "测试内容"}'
    print("\n测试特殊的换行+空格+引号格式")
    cleaned = clean_json_string(problem_json)
    
    # 验证清理后的JSON可以被解析
    try:
        data = json.loads(cleaned)
        print("测试通过：清理后的JSON可以被解析")
        print(f"解析结果: {data}")
    except json.JSONDecodeError as e:
        print(f"测试失败：清理后的JSON仍然无法解析 - {e}")

if __name__ == "__main__":
    test_clean_json_string()

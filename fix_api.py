import re
import sys
import os

def fix_file(file_path):
    print(f"尝试打开文件: {file_path}")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"成功读取文件，共 {len(content)} 字符")
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
        return
    
    # 统计原始文件中的关键词出现次数
    original_new_foreshadowings_count = content.count("new_foreshadowings")
    original_new_characters_count = content.count("new_characters")
    
    print(f"原始文件中 'new_foreshadowings' 出现次数: {original_new_foreshadowings_count}")
    print(f"原始文件中 'new_characters' 出现次数: {original_new_characters_count}")
    
    # 使用正则表达式移除new_foreshadowings和new_characters相关代码
    modifications = 0
    
    # 移除 new_foreshadowings 字段
    pattern1 = r',\s*"new_foreshadowings":\s*\[\]'
    replacement1 = ''
    content1 = re.sub(pattern1, replacement1, content)
    modifications += (content.count('"new_foreshadowings": []') - content1.count('"new_foreshadowings": []'))
    content = content1
    
    # 移除打印新伏笔的语句
    pattern2 = r'print\(f"发现 \{len\(final_result\[\'new_foreshadowings\'\]\)\} 个新伏笔"\)\s*'
    replacement2 = ''
    content1 = re.sub(pattern2, replacement2, content)
    modifications += (content.count("print(f\"发现 {len(final_result['new_foreshadowings'])} 个新伏笔\")") - 
                     content1.count("print(f\"发现 {len(final_result['new_foreshadowings'])} 个新伏笔\")"))
    content = content1
    
    # 移除初始化new_foreshadowings的代码
    pattern3 = r'if\s*"new_foreshadowings"\s*not\s*in\s*(result|check_result):\s*\1\["new_foreshadowings"\]\s*=\s*\[\]\s*'
    replacement3 = ''
    content1 = re.sub(pattern3, replacement3, content)
    modifications += len(re.findall(pattern3, content)) - len(re.findall(pattern3, content1))
    content = content1
    
    # 移除 new_characters 相关代码
    pattern4 = r',\s*"new_characters":\s*\[\]'
    replacement4 = ''
    content1 = re.sub(pattern4, replacement4, content)
    modifications += (content.count('"new_characters": []') - content1.count('"new_characters": []'))
    content = content1
    
    # 移除初始化new_characters的代码
    pattern5 = r'if\s*"new_characters"\s*not\s*in\s*check_result:\s*check_result\["new_characters"\]\s*=\s*\[\]\s*'
    replacement5 = ''
    content1 = re.sub(pattern5, replacement5, content)
    modifications += len(re.findall(pattern5, content)) - len(re.findall(pattern5, content1))
    content = content1
    
    # 删除确保不包含new_characters字段的代码
    pattern6 = r'#\s*确保结果不包含new_characters字段.*?\n\s*if\s*"new_characters"\s*in\s*check_result:.*?\n\s*del\s*check_result\["new_characters"\].*?\n'
    replacement6 = ''
    content1 = re.sub(pattern6, replacement6, content, flags=re.DOTALL)
    modifications += len(re.findall(pattern6, content)) - len(re.findall(pattern6, content1))
    content = content1
    
    # 更新提示词
    pattern7 = r'重要：不要包含new_characters字段，我们只关注已有人物的状态更新'
    replacement7 = '注意：只更新已有人物状态，不要创建新人物'
    content1 = re.sub(pattern7, replacement7, content)
    modifications += (content.count('重要：不要包含new_characters字段，我们只关注已有人物的状态更新') - 
                     content1.count('重要：不要包含new_characters字段，我们只关注已有人物的状态更新'))
    content = content1
    
    # 统计修改后的文件中的关键词出现次数
    final_new_foreshadowings_count = content.count("new_foreshadowings")
    final_new_characters_count = content.count("new_characters")
    
    print(f"修改后文件中 'new_foreshadowings' 出现次数: {final_new_foreshadowings_count}")
    print(f"修改后文件中 'new_characters' 出现次数: {final_new_characters_count}")
    
    if modifications == 0:
        print("警告：未进行任何修改")
    else:
        print(f"总共进行了 {modifications} 处修改")
    
    if final_new_foreshadowings_count > 0:
        print(f"警告：文件中仍有 {final_new_foreshadowings_count} 处 'new_foreshadowings' 未被移除")
    
    if final_new_characters_count > 0:
        print(f"警告：文件中仍有 {final_new_characters_count} 处 'new_characters' 未被移除")
        # 显示这些位置
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if "new_characters" in line:
                print(f"  行 {i+1}: {line.strip()}")
    
    # 确保输出目录存在
    output_dir = os.path.dirname(file_path)
    if not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"创建目录: {output_dir}")
        except Exception as e:
            print(f"创建目录失败: {str(e)}")
            return
    
    output_path = os.path.join(output_dir, 'deepseek_all_fixed2.py')
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"已创建修复后的文件: {output_path}")
    except Exception as e:
        print(f"写入文件失败: {str(e)}")

if __name__ == "__main__":
    # 获取当前工作目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 尝试不同的路径
    possible_paths = [
        "llm/deepseek_all.py",  # 相对路径
        "./llm/deepseek_all.py",  # 显式相对路径
        os.path.join(current_dir, "llm", "deepseek_all.py"),  # 绝对路径
    ]
    
    found = False
    for path in possible_paths:
        print(f"尝试路径: {path}")
        if os.path.exists(path):
            print(f"文件存在: {path}")
            fix_file(path)
            found = True
            break
    
    if not found:
        print("未找到文件，尝试列出可能的目录:")
        if os.path.exists("llm"):
            print("llm 目录存在")
            print("llm 目录内容:")
            for item in os.listdir("llm"):
                print(f"  - {item}")
        else:
            print("llm 目录不存在")
            print("当前目录内容:")
            for item in os.listdir("."):
                print(f"  - {item}") 
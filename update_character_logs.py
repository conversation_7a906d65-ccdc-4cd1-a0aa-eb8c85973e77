#!/usr/bin/env python
"""
修复novel/generator.py中的所有人物状态更新日志
"""

import os
import re
import sys
from pathlib import Path

# 向上级目录添加到sys.path，这样可以导入项目模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def update_generator_character_logs():
    """更新generator.py中的角色状态更新日志"""
    # 获取generator.py的路径
    generator_path = Path("novel/generator.py")
    if not generator_path.exists():
        print(f"错误: 文件 {generator_path} 不存在")
        return False
    
    # 读取文件内容
    with open(generator_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 查找并替换传统生成方法中的日志
    traditional_pattern = r'(# 创建NovelChecker实例并执行apply_chapter_updates\s*?from novel\.checker import NovelChecker\s*?checker = NovelChecker\(\)\s*?if checker\.load_data\(\):\s*?# 设置总章节数（必要属性）\s*?checker\.total_chapters = self\.total_chapters\s*?# 应用章节更新（更新人物状态和战力）\s*?)checker\.apply_chapter_updates\(chapter_number, check_result\)\s*?print\(f"第{chapter_number}章的人物状态和战力已更新"\)'
    traditional_replacement = r'\1update_success = checker.apply_chapter_updates(chapter_number, check_result)\n                        if update_success:\n                            character_updates = check_result.get("character_updates", [])\n                            character_names = [update.get("name") for update in character_updates if update.get("name")]\n                            print(f"✅ 第{chapter_number}章(传统生成)更新完成：{len(character_updates)}个角色 {", ".join(character_names)}")\n                        else:\n                            print(f"❌ 第{chapter_number}章(传统生成)人物状态和战力更新失败")'
    
    # 替换传统生成方法中的日志
    updated_content = re.sub(traditional_pattern, traditional_replacement, content)
    
    # 查找并替换重新生成方法中的日志
    regen_pattern = r'(# 创建NovelChecker实例并执行apply_chapter_updates\s*?from novel\.checker import NovelChecker\s*?checker = NovelChecker\(\)\s*?if checker\.load_data\(\):\s*?# 设置总章节数（必要属性）\s*?checker\.total_chapters = self\.total_chapters\s*?# 应用章节更新（更新人物状态和战力）\s*?)checker\.apply_chapter_updates\(chapter_number, new_check_result\)\s*?print\(f"第{chapter_number}章的人物状态和战力已更新"\)'
    regen_replacement = r'\1update_success = checker.apply_chapter_updates(chapter_number, new_check_result)\n                                if update_success:\n                                    character_updates = new_check_result.get("character_updates", [])\n                                    character_names = [update.get("name") for update in character_updates if update.get("name")]\n                                    print(f"✅ 第{chapter_number}章(重新生成)更新完成：{len(character_updates)}个角色 {", ".join(character_names)}")\n                                else:\n                                    print(f"❌ 第{chapter_number}章(重新生成)人物状态和战力更新失败")'
    
    # 替换重新生成方法中的日志
    updated_content = re.sub(regen_pattern, regen_replacement, updated_content)
    
    # 检查是否有变更
    if updated_content == content:
        print("未找到需要更新的角色状态日志")
        return False
    
    # 写入更新后的内容
    with open(generator_path, "w", encoding="utf-8") as f:
        f.write(updated_content)
    
    print(f"已更新 {generator_path} 中的角色状态日志")
    return True

def main():
    """主函数"""
    print("开始更新角色状态日志...")
    
    # 更新generator.py中的日志
    update_generator_character_logs()
    
    print("角色状态日志更新完成")

if __name__ == "__main__":
    main() 
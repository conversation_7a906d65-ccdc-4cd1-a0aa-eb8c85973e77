#!/usr/bin/env python3
"""
测试改进后的JSON解析功能
"""

import os
import sys
import json
import traceback
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.json_helper import parse_json_safely, fix_json
from utils.fix_storyline import fix_storyline_json, get_chapter_from_storyline, load_storyline
from utils.file_manager import save_main_storyline, load_main_storyline

def test_json_parsing():
    """测试通用JSON解析功能"""
    print("\n========== 测试通用JSON解析功能 ==========")
    
    test_cases = [
        # 正常的JSON
        ('{"name": "测试", "value": 123}', "标准JSON"),
        # 单引号代替双引号
        ("{'name': '测试', 'value': 123}", "单引号JSON"),
        # 引号未转义的JSON
        ('{"text": "引号"测试"未转义"}', "引号未转义"),
        # 属性名未加引号
        ('{name: "测试", value: 123}', "属性名无引号"),
        # 多行字符串
        ('{"text": "多行\n字符串"}', "多行字符串"),
        # 布尔值和null
        ('{"valid": True, "empty": None}', "布尔值和null"),
        # 复杂嵌套
        ('{"data": {"items": [{"id": 1, "name": "测试项目"}]}}', "复杂嵌套"),
        # 常见错误：多余逗号
        ('{"name": "测试", "value": 123,}', "多余逗号"),
        # 常见错误：缺少逗号
        ('{"name": "测试" "value": 123}', "缺少逗号")
    ]
    
    success_count = 0
    for json_str, case_name in test_cases:
        print(f"\n测试案例: {case_name}")
        print(f"输入: {json_str[:50]}...")
        
        result = parse_json_safely(json_str, case_name)
        if result:
            success_count += 1
            print(f"[成功] 解析成功: {str(result)[:50]}...")
        else:
            print(f"[失败] 解析失败")
    
    print(f"\n总体结果: {success_count}/{len(test_cases)} 测试通过 ({success_count/len(test_cases)*100:.1f}%)")
    return success_count == len(test_cases)

def test_chapter_validation():
    """测试章节数量验证功能"""
    print("\n========== 测试章节数量验证功能 ==========")
    
    # 创建一个简单的测试文件
    temp_file = "temp_test_storyline.json"
    
    # 准备测试数据
    test_data = {
        "story_title": "测试小说",
        "outlines": [
            {"index": 1, "title": "第一章", "content": "内容1"},
            {"index": 2, "title": "第二章", "content": "内容2"},
            {"index": 3, "title": "第三章", "content": "内容3"}
        ]
    }
    
    try:
        # 写入测试文件
        with open(temp_file, "w", encoding="utf-8") as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print("已创建测试文件，包含3章内容")
        
        # 测试1: 正确的章节数量
        print("\n测试1: 正确的章节数量验证 (期望3章)")
        result1 = fix_storyline_json(temp_file, 3)
        validation1 = bool(result1)
        print(f"测试1结果: {'通过' if validation1 else '失败'}")
        
        # 测试2: 错误的章节数量
        print("\n测试2: 错误的章节数量验证 (期望5章)")
        result2 = fix_storyline_json(temp_file, 5)
        validation2 = not bool(result2)  # 应该返回空字典表示验证失败
        print(f"测试2结果: {'通过' if validation2 else '失败'}")
        
        # 测试3: 无章节数量要求
        print("\n测试3: 无章节数量要求")
        result3 = fix_storyline_json(temp_file)
        validation3 = bool(result3)
        print(f"测试3结果: {'通过' if validation3 else '失败'}")
        
        # 删除测试文件
        try:
            os.remove(temp_file)
            print("\n已删除测试文件")
        except:
            print("\n警告: 无法删除测试文件")
        
        all_passed = validation1 and validation2 and validation3
        print(f"\n章节验证测试总体结果: {'全部通过' if all_passed else '部分失败'}")
        return all_passed
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        traceback.print_exc()
        
        # 确保清理测试文件
        if os.path.exists(temp_file):
            try:
                os.remove(temp_file)
                print("已删除测试文件")
            except:
                pass
        
        return False

def test_file_manager_integration():
    """测试与file_manager集成功能"""
    print("\n========== 测试与file_manager集成 ==========")
    
    # 准备测试数据
    test_content = json.dumps({
        "story_title": "测试集成小说",
        "outlines": [
            {"index": 1, "title": "第一章", "content": "内容1"},
            {"index": 2, "title": "第二章", "content": "内容2"},
            {"index": 3, "title": "第三章", "content": "内容3"},
            {"index": 4, "title": "第四章", "content": "内容4"}
        ]
    }, ensure_ascii=False)
    
    try:
        # 测试1: 正确的章节数量
        print("\n测试1: 正确章节数量保存")
        result1 = save_main_storyline(test_content, 4)
        print(f"保存结果: {'成功' if result1 else '失败'}")
        
        # 测试2: 加载并验证
        print("\n测试2: 正确章节数量加载")
        data = load_main_storyline(4)
        result2 = data is not None
        print(f"加载结果: {'成功' if result2 else '失败'}")
        
        # 测试3: 错误章节数量验证
        print("\n测试3: 错误章节数量加载")
        data = load_main_storyline(5)  # 期望5章，但实际只有4章
        result3 = data is None  # 应该返回None表示验证失败
        print(f"加载结果: {'验证正确拒绝' if result3 else '验证错误接受'}")
        
        all_passed = result1 and result2 and result3
        print(f"\n文件管理器集成测试总体结果: {'全部通过' if all_passed else '部分失败'}")
        return all_passed
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success1 = test_json_parsing()
        success2 = test_chapter_validation()
        success3 = test_file_manager_integration()
        
        if success1 and success2 and success3:
            print("\n总体测试结果: 所有测试通过")
            sys.exit(0)
        else:
            print("\n总体测试结果: 部分测试失败")
            sys.exit(1)
    except Exception as e:
        print(f"测试脚本执行过程中发生错误: {str(e)}")
        traceback.print_exc()
        sys.exit(2) 
#!/usr/bin/env python3
"""
测试fix_storyline模块的功能，使用通用的方法解析JSON
"""

import os
import sys
import traceback
from utils.fix_storyline import fix_storyline_json, get_chapter_from_storyline, load_storyline

def test_fix_storyline():
    """测试fix_storyline模块的解析功能"""
    print("========== 开始测试fix_storyline模块 ==========")
    
    try:
        # 加载并解析故事主线
        print("正在加载并解析故事主线...")
        storyline_data = load_storyline()
        if not storyline_data:
            print("错误: 无法加载故事主线数据，JSON解析失败")
            return False
        
        print(f"成功加载故事主线数据: {storyline_data.get('story_title', '未找到标题')}")
        
        # 检查章节数据类型
        chapter_outlines = storyline_data.get("chapter_outlines", None)
        print(f"章节大纲类型: {type(chapter_outlines)}")
        
        if isinstance(chapter_outlines, list):
            print(f"章节大纲列表长度: {len(chapter_outlines)}")
            
            # 测试获取不同章节
            test_chapters = [1, 5, 10, 15, 18, 20]
            success_count = 0
            
            for chapter_num in test_chapters:
                print(f"\n测试获取第{chapter_num}章...")
                chapter = get_chapter_from_storyline(storyline_data, chapter_num)
                if chapter:
                    success_count += 1
                    print(f"  标题: {chapter.get('title', '未找到')}")
                    content = chapter.get("content", "")
                    print(f"  内容摘要: {content[:50]}...")
                else:
                    print(f"  无法获取第{chapter_num}章数据")
            
            # 检查成功获取的章节数量
            if success_count == len(test_chapters):
                print(f"\n成功: 所有{len(test_chapters)}章测试数据都能正确获取")
                return True
            else:
                print(f"\n部分成功: {success_count}/{len(test_chapters)}章测试数据能正确获取")
                return success_count > 0
        else:
            print("错误: 章节大纲不是列表类型，解析可能不成功")
            return False
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        traceback.print_exc()
        return False

def test_invalid_json():
    """测试处理无效JSON的情况"""
    print("\n========== 开始测试无效JSON处理 ==========")
    
    try:
        # 创建一个包含错误JSON的临时文件
        temp_file = "temp_invalid_storyline.json"
        with open(temp_file, "w", encoding="utf-8") as f:
            f.write("""
            {
                "story_title": "测试故事",
                "chapter_outlines": "[
                    {\\"chapter_number\\": 1, \\"title\\": \\"测试章节1\\", \\"content\\": \\"这是测试内容1\\"},
                    {\\"chapter_number\\": 2, \\"title\\": \\"测试章节2\\", \\"content\\": \\"这是测试内容2\\"} 
                "
            }
            """)
        
        print("已创建包含错误JSON的测试文件")
        
        # 测试解析无效JSON
        print("开始解析无效JSON...")
        result = fix_storyline_json(temp_file)
        
        # 删除临时文件
        try:
            os.remove(temp_file)
            print("已删除临时文件")
        except Exception as e:
            print(f"删除临时文件时出错: {str(e)}")
        
        # 检查结果
        print(f"解析结果: {result}")
        if result == {}:
            print("成功: 无效JSON返回空字典，表示需要重新生成")
            return True
        else:
            print("失败: 无效JSON未返回空字典")
            return False
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success1 = test_fix_storyline()
        print(f"\n测试1结果: {'成功' if success1 else '失败'}")
        
        success2 = test_invalid_json()
        print(f"\n测试2结果: {'成功' if success2 else '失败'}")
        
        if success1 and success2:
            print("\n总体测试结果: 所有测试通过")
            sys.exit(0)
        elif success1:
            print("\n总体测试结果: 只有valid JSON测试通过")
            sys.exit(1)
        else:
            print("\n总体测试结果: 所有测试失败")
            sys.exit(2)
    except Exception as e:
        print(f"测试脚本执行过程中发生错误: {str(e)}")
        traceback.print_exc()
        sys.exit(3) 
#!/usr/bin/env python3
"""
测试快速修复功能
"""

import sys
import os
import json
import time

# 添加项目根目录
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.json_helper import quick_fix_missing_commas

def main():
    # 简单的测试JSON，缺少逗号
    test_json = '{"field1": "value1" "field2": "value2"}'
    
    print(f"原始JSON: {test_json}")
    
    # 测试快速修复
    start_time = time.time()
    fixed = quick_fix_missing_commas(test_json)
    end_time = time.time()
    
    print(f"修复后: {fixed}")
    print(f"耗时: {(end_time - start_time)*1000:.2f}毫秒")
    
    # 验证修复结果
    try:
        data = json.loads(fixed)
        print("修复成功！解析结果:")
        print(data)
    except json.JSONDecodeError as e:
        print(f"修复失败: {e}")

if __name__ == "__main__":
    main() 
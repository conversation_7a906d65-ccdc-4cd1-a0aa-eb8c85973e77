"""
小说生成系统主程序入口
"""

import os
import sys
import argparse
import time
from typing import Optional

from config import OUTPUT_DIR, MAX_RETRIES
from novel.generator import NovelGenerator
from novel.checker import <PERSON><PERSON><PERSON><PERSON>

def create_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(description="男频网络小说生成系统")

    subparsers = parser.add_subparsers(dest="command", help="命令")

    # 创建新项目的命令
    create_parser = subparsers.add_parser("create", help="创建新项目")
    create_parser.add_argument("genre", help="小说流派")
    create_parser.add_argument("--author", default="AI作家", help="作者名（默认：AI作家）")
    create_parser.add_argument("--force", action="store_true", help="强制清空output目录，不提示确认")

    # 继续生成的命令
    continue_parser = subparsers.add_parser("continue", help="继续生成现有项目")
    continue_parser.add_argument("--chapters", type=int, default=-1, help="要生成的章节数（默认：-1，表示生成所有剩余章节，直到小说结束）")
    continue_parser.add_argument("--start", type=int, help="从哪一章开始生成（默认：当前进度的下一章）")

    # 生成单章的命令
    chapter_parser = subparsers.add_parser("chapter", help="生成单章")
    chapter_parser.add_argument("chapter_number", type=int, help="章节号")
    chapter_parser.add_argument("--outline-only", action="store_true", help="只生成大纲，不生成章节内容")

    # 检查章节的命令
    check_parser = subparsers.add_parser("check", help="检查章节")
    check_parser.add_argument("chapter_number", type=int, nargs="?", help="要检查的章节号，不指定则检查所有章节")

    # 导出小说的命令
    export_parser = subparsers.add_parser("export", help="导出小说")
    export_parser.add_argument("--output", help="输出路径（可选）")

    # 查看状态的命令
    status_parser = subparsers.add_parser("status", help="查看项目状态")

    # 一次性生成所有章节的命令
    generate_all_parser = subparsers.add_parser("generate_all", help="从第一章开始连续生成所有章节")
    generate_all_parser.add_argument("--force", action="store_true", help="强制重新生成已有章节")
    generate_all_parser.add_argument("--pause", type=int, default=5, help="章节间暂停时间（秒）")
    generate_all_parser.add_argument("--outline-only", action="store_true", help="只生成大纲，不生成章节内容")

    # 重新生成故事主线
    regenerate_parser = subparsers.add_parser('regenerate_storyline', help='重新生成故事主线')
    regenerate_parser.add_argument('--force', action='store_true', help='强制完全重新生成，不保留原有内容')

    # 只生成风格和背景
    style_bg_parser = subparsers.add_parser('generate_style_and_background', help='只生成风格和背景设定')
    style_bg_parser.add_argument("genre", help="小说流派")
    style_bg_parser.add_argument("--force", action="store_true", help="强制清空output目录，不提示确认")

    # 只生成人物和伏笔
    chars_parser = subparsers.add_parser('generate_characters_and_foreshadowings', help='只生成人物和伏笔设定(需要先生成风格和背景)')

    return parser

def handle_create(args) -> int:
    """处理创建项目命令"""
    # 清空output目录
    from utils.file_manager import clean_output_directory

    # 如果没有指定--force参数，则提示用户确认
    if not args.force:
        confirm = input("创建新项目将清空output目录下的所有文件，确定要继续吗？(y/n): ")
        if confirm.lower() != 'y':
            print("已取消创建项目")
            return 0

    # 清空output目录
    success = clean_output_directory()
    if not success:
        print("清空output目录失败")
        return 1
        
    # 创建必要的默认文件（在目录清空后）
    print("创建必要的默认文件...")
    try:
        from utils.create_default_files import main as create_defaults
        create_defaults()
    except Exception as e:
        print(f"警告：创建默认文件时出错: {str(e)}")

    # 初始化项目
    generator = NovelGenerator()
    success = generator.init_project(args.genre, "", args.author)

    if not success:
        print("创建项目失败")
        return 1

    # 移除生成第一章的代码
    # print("\n正在生成第一章...")
    # success = generator.process_next_chapter()
    # 
    # if not success:
    #     print("生成第一章失败")
    #     return 1

    print("\n项目创建成功！大纲已生成完毕。")
    print("您可以使用 'python main.py continue' 命令开始生成章节内容。")
    return 0

def handle_continue(args) -> int:
    """处理继续生成命令"""
    generator = NovelGenerator()
    success = generator.load_project()

    if not success:
        print("加载项目失败")
        return 1

    completed, total, percentage = generator.get_completion_progress()
    print(f"当前进度：{completed}/{total} 章（{percentage:.2f}%）")

    # 确定起始章节
    start_chapter = args.start if args.start is not None else completed + 1
    if start_chapter < 1:
        print("起始章节不能小于1")
        return 1
    if start_chapter > total:
        print(f"起始章节不能大于总章节数{total}")
        return 1

    # 如果指定了起始章节，则将当前章节设置为起始章节-1
    if args.start is not None:
        generator.current_chapter = start_chapter - 1
        print(f"将从第{start_chapter}章开始生成")

    # 获取要生成的章节数
    chapters_to_generate = args.chapters

    # 如果chapters_to_generate为-1，则生成所有剩余章节
    if chapters_to_generate == -1:
        chapters_to_generate = total - start_chapter + 1
        print(f"将生成所有剩余章节：{chapters_to_generate}章（从第{start_chapter}章到第{total}章）")
    else:
        # 如果指定的章节数超过了剩余章节数，则调整为剩余章节数
        if start_chapter + chapters_to_generate - 1 > total:
            adjusted_chapters = total - start_chapter + 1
            print(f"指定生成{chapters_to_generate}章，但剩余章节只有{adjusted_chapters}章")
            print(f"调整生成章节数为{adjusted_chapters}章（从第{start_chapter}章到第{total}章）")
            chapters_to_generate = adjusted_chapters
        else:
            print(f"将生成{chapters_to_generate}章（从第{start_chapter}章到第{start_chapter + chapters_to_generate - 1}章）")

    if chapters_to_generate <= 0:
        print("所有章节已生成完成")
        return 0

    for i in range(chapters_to_generate):
        current_chapter = start_chapter + i
        print(f"\n正在生成第{current_chapter}章（{i+1}/{chapters_to_generate}）...")

        # 确保有大纲
        chapter_outline = generator.outline.get_chapter(current_chapter)
        if not chapter_outline:
            print(f"正在生成第{current_chapter}章大纲...")
            success = generator.generate_chapter_outline(current_chapter)
            if not success:
                print(f"生成第{current_chapter}章大纲失败")
                return 1

        # 生成章节内容
        if not chapter_outline or not chapter_outline.is_generated:
            print(f"正在生成第{current_chapter}章内容...")
            success = generator.generate_chapter_content(current_chapter)
            if not success:
                print(f"生成第{current_chapter}章内容失败")
                return 1

        # 检查章节
        print(f"正在检查第{current_chapter}章内容...")
        success, check_result = generator.check_chapter(current_chapter)

        if not success:
            print(f"检查第{current_chapter}章内容失败")
            return 1

        # 处理检查结果
        if not check_result or not check_result.get("passed", False):
            # 最多重试MAX_RETRIES次
            retry_count = 0

            while retry_count < MAX_RETRIES:
                retry_count += 1
                print(f"第{current_chapter}章检查未通过，正在进行第{retry_count}/{MAX_RETRIES}次重试...")

                # 重新生成章节内容
                success = generator.regenerate_chapter_content(current_chapter)
                if not success:
                    print(f"重新生成第{current_chapter}章内容失败")
                    if retry_count >= MAX_RETRIES:
                        print(f"已达到最大重试次数({MAX_RETRIES})，请手动修复")
                        return 1
                    continue

                # 再次检查
                print(f"正在检查重新生成的第{current_chapter}章内容...")
                success, check_result = generator.check_chapter(current_chapter)

                if success and check_result and check_result.get("passed", True):
                    print(f"第{retry_count}次重试成功，章节检查通过")
                    break

                if retry_count >= MAX_RETRIES:
                    print(f"已达到最大重试次数({MAX_RETRIES})，所有重试均未通过，请手动修复")
                    return 1

                print(f"第{retry_count}次重试未通过，继续尝试...")
                # 短暂暂停，避免API速率限制
                time.sleep(3)

        # 短暂暂停，避免API速率限制
        if i < chapters_to_generate - 1:
            print("等待5秒后继续...")
            time.sleep(5)

    # 获取更新后的进度
    completed, total, percentage = generator.get_completion_progress()
    print(f"\n已生成{chapters_to_generate}章，当前进度：{completed}/{total}章（{percentage:.2f}%）")

    # 导出当前进度
    success = generator.export_novel()

    # 如果已经生成到最后一章，显示完成信息
    if completed >= total:
        print("\n恭喜！所有章节已全部生成完成！")

    return 0

def handle_chapter(args) -> int:
    """处理生成单章命令"""
    generator = NovelGenerator()
    success = generator.load_project()

    if not success:
        print("加载项目失败")
        return 1

    chapter_number = args.chapter_number
    print(f"正在生成第 {chapter_number} 章...")

    # 检查章节大纲是否存在
    chapter_outline = generator.outline.get_chapter(chapter_number)
    if not chapter_outline:
        print(f"正在生成第 {chapter_number} 章大纲...")
        success = generator.generate_chapter_outline(chapter_number)
        if not success:
            print(f"生成第 {chapter_number} 章大纲失败")
            return 1
        print(f"第 {chapter_number} 章大纲生成成功！")
        
        # 再次获取章节大纲（刚刚生成的）
        chapter_outline = generator.outline.get_chapter(chapter_number)

    # 如果指定了只生成大纲，则直接返回
    if hasattr(args, 'outline_only') and args.outline_only:
        print(f"根据--outline-only参数，仅生成大纲，不生成章节内容")
        return 0

    # 生成章节内容
    if not chapter_outline or not chapter_outline.is_generated:
        print(f"正在生成第 {chapter_number} 章内容...")
        success = generator.generate_chapter_content(chapter_number)
        if not success:
            print(f"生成第 {chapter_number} 章内容失败")
            return 1

    # 检查章节
    print(f"正在检查第 {chapter_number} 章内容...")
    success, check_result = generator.check_chapter(chapter_number)

    if not success:
        print(f"检查第 {chapter_number} 章内容失败")
        return 1

    # 处理检查结果
    if check_result and check_result.get("passed", False):
        print(f"第 {chapter_number} 章处理完成")
    else:
        # 最多重试MAX_RETRIES次
        retry_count = 0

        while retry_count < MAX_RETRIES:
            retry_count += 1
            print(f"第 {chapter_number} 章检查未通过，正在进行第 {retry_count}/{MAX_RETRIES} 次重试...")

            # 重新生成章节内容
            success = generator.regenerate_chapter_content(chapter_number)
            if not success:
                print(f"重新生成第 {chapter_number} 章内容失败")
                if retry_count >= MAX_RETRIES:
                    print(f"已达到最大重试次数({MAX_RETRIES})，请手动修复")
                    return 1
                continue

            # 再次检查
            print(f"正在检查重新生成的第 {chapter_number} 章内容...")
            success, check_result = generator.check_chapter(chapter_number)

            if success and check_result and check_result.get("passed", True):
                print(f"第 {retry_count} 次重试成功，章节检查通过")
                break

            if retry_count >= MAX_RETRIES:
                print(f"已达到最大重试次数({MAX_RETRIES})，将删除大纲并重新生成")

                # 删除当前章节大纲
                success = generator.delete_chapter_outline(chapter_number)
                if not success:
                    print(f"删除第{chapter_number}章大纲失败")
                    return 1

                # 重新生成大纲
                success = generator.generate_chapter_outline(chapter_number)
                if not success:
                    print(f"重新生成第{chapter_number}章大纲失败")
                    return 1

                # 生成章节内容
                success = generator.generate_chapter_content(chapter_number)
                if not success:
                    print(f"生成第{chapter_number}章内容失败")
                    return 1

                # 再次检查
                print(f"正在检查重新生成的第{chapter_number}章内容...")
                success, check_result = generator.check_chapter(chapter_number)

                if success and check_result and check_result.get("passed", True):
                    print(f"重新生成大纲后，章节检查通过")
                    break
                else:
                    print(f"重新生成大纲后，章节检查仍未通过，请手动修复")
                    return 1

            print(f"第 {retry_count} 次重试未通过，继续尝试...")
            # 短暂暂停，避免API速率限制
            time.sleep(3)

    return 0

def handle_check(args) -> int:
    """处理检查章节命令"""
    checker = NovelChecker()
    success = checker.load_data()

    if not success:
        print("加载项目失败")
        return 1

    if args.chapter_number is not None:
        # 检查单章
        chapter_number = args.chapter_number
        print(f"正在检查第 {chapter_number} 章...")
        success, check_result = checker.check_chapter(chapter_number)

        if not success:
            print(f"检查第 {chapter_number} 章失败")
            return 1

        # 应用更新
        if check_result and check_result.get("passed", False):
            checker.apply_chapter_updates(chapter_number, check_result)
            print(f"第 {chapter_number} 章检查完成")
        else:
            print(f"第 {chapter_number} 章检查未通过，建议修改")
            return 1
    else:
        # 检查所有章节
        print("正在检查所有已生成章节...")
        success = checker.check_all_chapters()

        if not success:
            print("部分章节检查未通过")
            return 1

    # 检查一致性问题
    print("\n正在检查全局一致性问题...")
    issues = checker.check_consistency()

    has_issues = False
    for issue_type, issue_list in issues.items():
        if issue_list:
            has_issues = True
            print(f"\n{issue_type.replace('_', ' ').title()}:")
            for issue in issue_list:
                print(f"- {issue}")

    if not has_issues:
        print("未发现一致性问题")

    return 0 if not has_issues else 1

def handle_export(args) -> int:
    """处理导出小说命令"""
    generator = NovelGenerator()
    success = generator.load_project()

    if not success:
        print("加载项目失败")
        return 1

    output_path = args.output
    success = generator.export_novel(output_path)

    if not success:
        print("导出小说失败")
        return 1

    return 0

def handle_status(args) -> int:
    """处理查看状态命令"""
    generator = NovelGenerator()
    success = generator.load_project()

    if not success:
        print("加载项目失败")
        return 1

    # 获取项目状态
    from utils.file_manager import load_novel_info
    novel_info = load_novel_info()
    if not novel_info:
        print("未找到项目信息")
        return 1

    print(f"项目：《{novel_info.get('title', '未命名小说')}》")
    print(f"作者：{novel_info.get('author', '佚名')}")
    print(f"流派：{novel_info.get('genre', '未知')}")

    # 获取进度
    completed, total, percentage = generator.get_completion_progress()
    print(f"进度：{completed}/{total} 章（{percentage:.2f}%）")

    # 获取字数统计
    from utils.file_manager import get_all_chapters, read_chapter
    chapters = get_all_chapters()
    total_chars = 0
    for chapter_number in chapters:
        chapter_data = read_chapter(chapter_number)
        if chapter_data:
            total_chars += len(chapter_data["content"])

    print(f"当前总字数：约 {total_chars} 字（{total_chars/10000:.2f} 万字）")

    # 获取小说摘要
    checker = NovelChecker()
    checker.load_data()
    print("\n正在生成小说摘要...")
    summary = checker.generate_summary()
    print("\n小说摘要：")
    print(summary)

    return 0

def handle_generate_all(args) -> int:
    """处理一次性生成所有章节命令"""
    generator = NovelGenerator()
    success = generator.load_project()

    if not success:
        print("加载项目失败")
        return 1

    completed, total, percentage = generator.get_completion_progress()
    print(f"当前进度：{completed}/{total} 章（{percentage:.2f}%）")

    # 确定起始章节
    start_chapter = 1

    # 如果不是强制重新生成，则从未完成的章节开始
    if not args.force and completed > 0:
        start_chapter = completed + 1

    if start_chapter > total:
        print(f"所有{total}章已生成完成")
        return 0

    # 设置章节间暂停时间
    pause_seconds = max(1, args.pause)

    # 检查是否只生成大纲
    outline_only = hasattr(args, 'outline_only') and args.outline_only
    
    if outline_only:
        print(f"即将从第{start_chapter}章开始，连续生成到第{total}章的大纲（不生成内容）")
    else:
        print(f"即将从第{start_chapter}章开始，连续生成到第{total}章")
    print(f"每章生成完成后将暂停{pause_seconds}秒")
    print("--------------------")

    # 生成所有章节
    for current_chapter in range(start_chapter, total + 1):
        if outline_only:
            print(f"\n正在生成第{current_chapter}/{total}章大纲...")
        else:
            print(f"\n正在生成第{current_chapter}/{total}章...")

        # 确保有大纲
        chapter_outline = generator.outline.get_chapter(current_chapter)
        if not chapter_outline:
            print(f"正在生成第{current_chapter}章大纲...")
            success = generator.generate_chapter_outline(current_chapter)
            if not success:
                print(f"生成第{current_chapter}章大纲失败")
                return 1
            print(f"第{current_chapter}章大纲生成成功！")

        # 如果只生成大纲，则跳过内容生成
        if outline_only:
            # 短暂暂停，避免API速率限制
            if current_chapter < total:
                print(f"等待{pause_seconds}秒后继续...")
                time.sleep(pause_seconds)
            continue

        # 生成章节内容
        if not chapter_outline or not chapter_outline.is_generated or args.force:
            print(f"正在生成第{current_chapter}章内容...")
            success = generator.generate_chapter_content(current_chapter)
            if not success:
                print(f"生成第{current_chapter}章内容失败")
                return 1

        # 检查章节
        print(f"正在检查第{current_chapter}章内容...")
        success, check_result = generator.check_chapter(current_chapter)

        if not success:
            print(f"检查第{current_chapter}章内容失败")
            return 1

        # 处理检查结果
        if not check_result or not check_result.get("passed", False):
            # 最多重试MAX_RETRIES次
            retry_count = 0

            while retry_count < MAX_RETRIES:
                retry_count += 1
                print(f"第{current_chapter}章检查未通过，正在进行第{retry_count}/{MAX_RETRIES}次重试...")

                # 重新生成章节内容
                success = generator.regenerate_chapter_content(current_chapter)
                if not success:
                    print(f"重新生成第{current_chapter}章内容失败")
                    if retry_count >= MAX_RETRIES:
                        print(f"已达到最大重试次数({MAX_RETRIES})，请手动修复")
                        return 1
                    continue

                # 再次检查
                print(f"正在检查重新生成的第{current_chapter}章内容...")
                success, check_result = generator.check_chapter(current_chapter)

                if success and check_result and check_result.get("passed", True):
                    print(f"第{retry_count}次重试成功，章节检查通过")
                    break

                if retry_count >= MAX_RETRIES:
                    print(f"已达到最大重试次数({MAX_RETRIES})，将删除大纲并重新生成")

                    # 删除当前章节大纲
                    success = generator.delete_chapter_outline(current_chapter)
                    if not success:
                        print(f"删除第{current_chapter}章大纲失败")
                        return 1

                    # 重新生成大纲
                    success = generator.generate_chapter_outline(current_chapter)
                    if not success:
                        print(f"重新生成第{current_chapter}章大纲失败")
                        return 1

                    # 生成章节内容
                    success = generator.generate_chapter_content(current_chapter)
                    if not success:
                        print(f"生成第{current_chapter}章内容失败")
                        return 1

                    # 再次检查
                    print(f"正在检查重新生成的第{current_chapter}章内容...")
                    success, check_result = generator.check_chapter(current_chapter)

                    if success and check_result and check_result.get("passed", True):
                        print(f"重新生成大纲后，章节检查通过")
                        break
                    else:
                        print(f"重新生成大纲后，章节检查仍未通过，请手动修复")
                        return 1

                print(f"第{retry_count}次重试未通过，继续尝试...")
                # 短暂暂停，避免API速率限制
                time.sleep(3)

        # 当前进度
        new_completed, _, new_percentage = generator.get_completion_progress()
        print(f"当前进度：{new_completed}/{total}章（{new_percentage:.2f}%）")

        # 每章生成后导出一次，避免数据丢失
        generator.export_novel()

        # 短暂暂停，避免API速率限制
        if current_chapter < total:
            print(f"等待{pause_seconds}秒后继续...")
            time.sleep(pause_seconds)

    print(f"\n所有{total}章已全部生成完成！")
    print(f"小说已导出到输出目录")

    return 0

def handle_regenerate_storyline(args) -> int:
    """处理重新生成故事主线命令"""
    generator = NovelGenerator()
    if generator.load_project():
        # 设置强制重新生成参数
        generator.force_regenerate = args.force if hasattr(args, 'force') else False
        
        print(f"准备重新生成故事主线，强制完全重新生成: {generator.force_regenerate}")
        
        if generator.regenerate_main_storyline():
            print("故事主线重新生成成功")
            
            # 重新加载项目以更新相关信息
            generator.load_project()
            
            # 显示结果信息
            print(f"当前故事章节数: {generator.total_chapters}")
            print(f"故事标题: {generator.main_storyline.get('story_title', '未知')}")
            
            # 检查是否有更新的角色数据
            if len(generator.character_manager.characters) > 0:
                print(f"当前角色数量: {len(generator.character_manager.characters)}")
                print("建议使用 python main.py regenerate_characters 重新生成角色信息")
            
            # 检查是否有更新的伏笔数据
            if hasattr(generator, 'foreshadowing_manager') and generator.foreshadowing_manager.get_total_foreshadowings() > 0:
                print(f"当前伏笔数量: {generator.foreshadowing_manager.get_total_foreshadowings()}")
                print("建议使用 python main.py regenerate_foreshadowing 重新生成伏笔信息")
        else:
            print("故事主线重新生成失败")
    else:
        print("加载项目失败，请确认项目已初始化")
    return 0

def handle_generate_style_and_background(args) -> int:
    """处理只生成风格和背景的命令"""
    # 清空output目录
    from utils.file_manager import clean_output_directory

    # 如果没有指定--force参数，则提示用户确认
    if not args.force:
        confirm = input("生成风格和背景将清空output目录下的所有文件，确定要继续吗？(y/n): ")
        if confirm.lower() != 'y':
            print("已取消生成")
            return 0

    # 清空output目录
    success = clean_output_directory()
    if not success:
        print("清空output目录失败")
        return 1
        
    # 创建必要的默认文件（在目录清空后）
    print("创建必要的默认文件...")
    try:
        from utils.create_default_files import main as create_defaults
        create_defaults()
    except Exception as e:
        print(f"警告：创建默认文件时出错: {str(e)}")

    # 初始化生成器并只生成风格和背景
    generator = NovelGenerator()
    genre = args.genre
    success = generator.generate_style_and_background(genre)

    if not success:
        print("生成风格和背景失败")
        return 1

    print("\n风格和背景生成成功！")
    print("您可以使用 'python main.py generate_characters_and_foreshadowings' 命令继续生成人物和伏笔。")
    return 0

def handle_generate_characters_and_foreshadowings(args) -> int:
    """处理只生成人物和伏笔的命令"""
    # 初始化生成器并生成人物和伏笔
    generator = NovelGenerator()
    success = generator.generate_characters_and_foreshadowings()

    if not success:
        print("生成人物和伏笔失败")
        return 1

    print("\n人物和伏笔生成成功！")
    print("您可以使用 'python main.py generate_all --outline-only' 命令继续生成所有章节的大纲。")
    return 0

def main() -> int:
    """主程序入口"""
    # 解析命令行参数
    parser = create_parser()
    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return 0

    # 确保output目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    try:
        # create命令单独处理（不需要预先创建默认文件）
        if args.command == "create":
            return handle_create(args)
        
        # 对于非create命令，先确保必要的默认文件存在
        try:
            from utils.create_default_files import main as create_defaults
            create_defaults()
        except Exception as e:
            print(f"警告：创建默认文件时出错: {str(e)}")
            print("程序将继续执行，但可能会遇到文件不存在的错误")
        
        # 根据命令分别处理
        if args.command == "continue":
            return handle_continue(args)
        elif args.command == "chapter":
            return handle_chapter(args)
        elif args.command == "check":
            return handle_check(args)
        elif args.command == "export":
            return handle_export(args)
        elif args.command == "status":
            return handle_status(args)
        elif args.command == "generate_all":
            return handle_generate_all(args)
        elif args.command == "regenerate_storyline":
            return handle_regenerate_storyline(args)
        elif args.command == "generate_style_and_background":
            return handle_generate_style_and_background(args)
        elif args.command == "generate_characters_and_foreshadowings":
            return handle_generate_characters_and_foreshadowings(args) 
        else:
            print(f"未知命令: {args.command}")
            parser.print_help()
            
            return 1
    except KeyboardInterrupt:
        print("\n操作已被用户中断")
        return 130
    except Exception as e:
        import traceback
        print(f"发生错误: {str(e)}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

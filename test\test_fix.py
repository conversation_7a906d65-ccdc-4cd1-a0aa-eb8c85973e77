#!/usr/bin/env python3
"""
测试fix_json功能
"""

import json
from utils.json_helper import fix_json, parse_json_safely

def test_specific_case():
    # 测试1：缺少逗号的简单对象
    test1 = '{"field1": "value1" "field2": "value2"}'
    fixed1 = fix_json(test1)
    print("原始JSON:", test1)
    print("修复后JSON:", fixed1)
    try:
        parsed1 = json.loads(fixed1)
        print("解析结果:", parsed1)
        print("解析成功!")
    except json.JSONDecodeError as e:
        print("解析失败:", str(e))
    
    print("\n" + "-" * 50 + "\n")
    
    # 测试2：嵌套对象中缺少逗号
    test2 = '{"obj": {"a": 1 "b": 2}, "arr": [1 2 3]}'
    fixed2 = fix_json(test2)
    print("原始JSON:", test2)
    print("修复后JSON:", fixed2)
    try:
        parsed2 = json.loads(fixed2)
        print("解析结果:", parsed2)
        print("解析成功!")
    except json.JSONDecodeError as e:
        print("解析失败:", str(e))
    
    print("\n" + "-" * 50 + "\n")
    
    # 测试3：复杂嵌套结构
    test3 = '''
    {
      "story_title": "测试小说"
      "outlines": [
        {
          "index": 1
          "title": "第一章"
          "content": "内容..."
        }
        {
          "index": 2
          "title": "第二章"
          "content": "更多内容..."
        }
      ]
    }
    '''
    fixed3 = fix_json(test3)
    print("原始JSON:", test3)
    print("修复后JSON:", fixed3)
    try:
        parsed3 = json.loads(fixed3)
        print("解析结果:", parsed3)
        print("解析成功!")
    except json.JSONDecodeError as e:
        print("解析失败:", str(e))
        # 尝试使用parse_json_safely
        print("尝试使用parse_json_safely:")
        result = parse_json_safely(test3, "复杂结构测试")
        if result:
            print("parse_json_safely解析成功:", result)
        else:
            print("parse_json_safely解析失败")

if __name__ == "__main__":
    test_specific_case() 